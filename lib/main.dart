import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'src/core/locale/locale.dart';
import 'src/core/locale/locale_controller.dart';
import 'src/core/utils/app_theme.dart';
import 'src/core/utils/custom_ar_message.dart';
import 'src/core/utils/injector.dart';
import 'src/core/router/app_router.dart';
import 'firebase_options.dart';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'src/features/notification/services/notification_service.dart';
import 'package:timeago/timeago.dart';
import 'package:intl/date_symbol_data_local.dart';

Future<void> firebaseMessagingBackgroundHandler(
    RemoteMessage remoteMessage) async {}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  if (!kIsWeb) {
    if (kDebugMode) {
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(false);
    } else {
      FlutterError.onError = (errorDetails) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      };
      // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };
    }
  }

  await FirebaseMessaging.instance.requestPermission(provisional: true);

// For apple platforms, ensure the APNS token is available before making any FCM plugin API calls
  final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
  // if (apnsToken != null) {
  print('APNS token: $apnsToken');
  // }
  await GetStorage.init();
  await DependencyInjection.init();
  // await FireBaseAPi().initNotifications();
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    // Get.find<ChatController>().receiveMessage(message);
  });
  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {});
  setLocaleMessages('ar', CustomArMessages());
  initializeDateFormatting();
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    LocaleController localeController = Get.find();
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) => GetMaterialApp(
        routingCallback: (routing) {
          if (routing?.current == AppRouter.homeScreen) {
            if (Get.isRegistered<NotificationService>()) {
              NotificationService.instance.getUnreadNotificationCount();
            }
          }
        },
        theme: AppTheme.theme,
        getPages: AppRouter.pages,
        debugShowCheckedModeBanner: false,
        locale: localeController.initialLang.value,
        translations: MyLocale(),
        initialRoute: AppRouter.splashScreen,
      ),
    );
  }
}
