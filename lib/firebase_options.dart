// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAFbck_laZM6wtB_IZiakJ2iPXjduNVvUo',
    appId: '1:52151034856:android:599d57e203899929ebfa5d',
    messagingSenderId: '52151034856',
    projectId: 'rawd-aljenan',
    storageBucket: 'rawd-aljenan.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA3bxc7qavDbO9o68o-aUTiGompXTfickw',
    appId: '1:52151034856:ios:8c0634456f305d63ebfa5d',
    messagingSenderId: '52151034856',
    projectId: 'rawd-aljenan',
    storageBucket: 'rawd-aljenan.appspot.com',
    iosBundleId: 'com.smartfingers.ryad',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAsa_UItCNlhyFBdo8KwqlcFhyTkYbAFmY',
    appId: '1:610808329778:web:9f93aa7a2900222a136e2b',
    messagingSenderId: '610808329778',
    projectId: 'kindergarten-839f8',
    authDomain: 'kindergarten-839f8.firebaseapp.com',
    storageBucket: 'kindergarten-839f8.appspot.com',
    measurementId: 'G-7YV88397D6',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBC-J8qQZ4-4IkHs5TBwHVpaCtN1Uf61Sg',
    appId: '1:610808329778:ios:71d2ffc370019a12136e2b',
    messagingSenderId: '610808329778',
    projectId: 'kindergarten-839f8',
    storageBucket: 'kindergarten-839f8.appspot.com',
    iosBundleId: 'com.smartfingers.ryad',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAsa_UItCNlhyFBdo8KwqlcFhyTkYbAFmY',
    appId: '1:610808329778:web:4f487804a92f2ba5136e2b',
    messagingSenderId: '610808329778',
    projectId: 'kindergarten-839f8',
    authDomain: 'kindergarten-839f8.firebaseapp.com',
    storageBucket: 'kindergarten-839f8.appspot.com',
    measurementId: 'G-HDWC6P00NF',
  );
}