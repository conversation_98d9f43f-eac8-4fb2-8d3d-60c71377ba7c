import 'package:flutter/material.dart';
import 'dart:core';

import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:universal_platform/universal_platform.dart';

import 'package:extended_image/extended_image.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../features/daily_post/presentation/widgets/post_card/post_media_slider.dart';
import '../data/models/daily_post_media.dart';
import '../../features/about_page/presentation/widgets/image_gallery.dart';
import '../widgets/custom_text.dart';
import 'app_assets.dart';
import 'app_color.dart';
import 'package:intl/intl.dart' as intl;
// import 'package:image_fade/image_fade.dart';

extension IconDataExtension on IconData {
  IconData setMatchTextDirection({bool condition = true}) {
    return condition
        ? IconData(codePoint, fontFamily: fontFamily, matchTextDirection: true)
        : this;
  }
}

extension ColorExtension on Color {
  Color fromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }
}

extension DateTimeExtension on DateTime {
  String format(String format) {
    print(Get.locale?.languageCode);
    return intl.DateFormat(format).format(this);
  }
}

extension StringExtention on String {
  String? insertToBeging(String text) {
    return text + this;
  }

  String? insertToEnd(String text) {
    return this + text;
  }

  String upperCaseFirstChar() {
    if (length <= 1) {
      return toUpperCase();
    }

    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }
}

extension Space on MultiChildRenderObjectWidget {
  MultiChildRenderObjectWidget verticalSpace(double size) {
    // ignore: no_leading_underscores_for_local_identifiers
    var _children = children
        .map(
          (wdgt) => Container(
            margin: EdgeInsets.only(bottom: size),
            child: wdgt,
          ),
        )
        .toList();
    children.clear();

    children.addAll(_children);
    MultiChildRenderObjectElement(this);

    return this;
  }
}

// extension ListViewChildrenPadding on ListView {
//   ListView childrenPadding({double horizontal = 0, double vertical = 0}) {}
// }

// extension ListViewChildrenPadding on ListView {
//   ListView childrenPadding({double horizontal = 0, double vertical = 0}) {}
// }

class CustomOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    event.lines.forEach(debugPrint);
  }
}

var logger = Logger(
  printer: PrettyPrinter(
    methodCount: 2,
    errorMethodCount: 8,
    lineLength: 120,
    colors: !UniversalPlatform.isIOS,
    printEmojis: true,
    dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
  ),
  output: CustomOutput(),
);

/// Logging config
void printLog([dynamic rawData, DateTime? startTime, Level? level]) {
  if (kDebugMode) {
    var time = '';
    if (startTime != null) {
      final endTime = DateTime.now().difference(startTime);
      final icon = endTime.inMilliseconds > 2000
          ? '⌛️Slow-'
          : endTime.inMilliseconds > 1000
              ? '⏰Medium-'
              : '⚡️Fast-';
      time = '[$icon${endTime.inMilliseconds}ms]';
    }

    try {
      final data = '$rawData';
      final log = '$time${data.toString()}';

      /// print log for ios
      if (UniversalPlatform.isIOS) {
        debugPrint(log);
        return;
      }

      /// print log for android
      switch (level) {
        case Level.error:
          printError(log, StackTrace.empty);
          break;
        case Level.warning:
          logger.w(log, stackTrace: StackTrace.empty);
          break;
        case Level.info:
          logger.i(log, stackTrace: StackTrace.empty);
          break;
        case Level.debug:
          logger.d(log, stackTrace: StackTrace.empty);
          break;
        case Level.trace:
          logger.t(log, stackTrace: StackTrace.empty);
          break;
        default:
          if (time.startsWith('[⌛️Slow-')) {
            logger.f(log, stackTrace: StackTrace.empty);
            break;
          }
          if (time.startsWith('[⏰Medium-')) {
            logger.w(log, stackTrace: StackTrace.empty);
            break;
          }
          logger.t(log, stackTrace: StackTrace.empty);
          break;
      }
    } catch (err, trace) {
      printError(err, trace);
    }
  }
}

void printError(dynamic err, [dynamic trace, dynamic message]) {
  if (!kDebugMode) {
    return;
  }

  final shouldHide = trace == null || '$trace'.isEmpty;
  if (shouldHide) {
    logger.d(err, error: message, stackTrace: StackTrace.empty);
    return;
  }

  logger.e(err, error: message ?? 'Stack trace:', stackTrace: trace);
}

const kCacheImageWidth = 700;

class SmartFingersImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Color? color;
  final String? package;
  final Widget? errorWidget;
  final Alignment alignment;
  final bool useExtendedImage;

  const SmartFingersImage({
    required this.imageUrl,
    super.key,
    this.width,
    this.height,
    this.fit,
    this.color,
    this.package,
    this.errorWidget,
    this.alignment = Alignment.center,
    this.useExtendedImage = true,
  });

  @override
  Widget build(BuildContext context) {
    var isSvgImage = imageUrl.split('.').last == 'svg';

    if (imageUrl.isEmpty) {
      return const SizedBox();
    }

    final cacheWidth = width != null && (width ?? 0) > 0
        ? (width! * 2.5).toInt()
        : kCacheImageWidth;

    if (!imageUrl.contains('http')) {
      if (isSvgImage) {
        return SvgPicture.asset(
          imageUrl,
          width: width,
          height: height,
          fit: fit ?? BoxFit.contain,
          colorFilter:
              color != null ? ColorFilter.mode(color!, BlendMode.srcIn) : null,
          alignment: alignment,
          package: package,
        );
      }

      if (useExtendedImage) {
        return ExtendedImage.asset(
          imageUrl,
          width: width,
          height: height,
          fit: fit,
          color: color,
          package: package,
          alignment: alignment,
          cacheWidth: kIsWeb ? null : cacheWidth,
        );
      } else {
        return Image.asset(
          imageUrl,
          width: width,
          height: height,
          fit: fit,
          color: color,
          package: package,
          alignment: alignment,
          cacheWidth: cacheWidth,
        );
      }
    }

    if (isSvgImage) {
      return SvgPicture.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit ?? BoxFit.contain,
        colorFilter:
            color != null ? ColorFilter.mode(color!, BlendMode.srcIn) : null,
        alignment: alignment,
      );
    }

    if (useExtendedImage) {
      return ExtendedImage.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        color: color,
        cache: true,
        alignment: alignment,
        cacheWidth: kIsWeb ? null : cacheWidth,
        loadStateChanged: (state) {
          switch (state.extendedImageLoadState) {
            case LoadState.completed:
              return state.completedWidget;

            case LoadState.failed:
              return errorWidget ?? const SizedBox();
            case LoadState.loading:
            default:
              return const SizedBox();
          }
        },
      );
    } else {
      return Image.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        color: color,
        alignment: alignment,
        cacheWidth: cacheWidth,
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ?? const SizedBox();
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            return child;
          }
          return const SizedBox();
        },
      );
    }
  }
}

class SmartFingersListTile extends StatelessWidget {
  const SmartFingersListTile({
    super.key,
    required this.title,
    required this.onTap,
    this.trailing,
    this.maxLine,
    this.leading,
    this.minTileHeight,
    this.subtitle,
    this.listTileTitleAlignment,
    this.color,
  });

  final VoidCallback onTap;
  final String title;

  final Widget? trailing;
  final int? maxLine;
  final Widget? leading;
  final double? minTileHeight;
  final Widget? subtitle;
  final ListTileTitleAlignment? listTileTitleAlignment;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    RoundedRectangleBorder shape = RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(10),
    );

    return Material(
      elevation: 0,
      shape: shape,
      child: ListTile(
        tileColor: color ?? Colors.white,
        shape: shape,
        minTileHeight: minTileHeight,
        leading: leading,
        titleAlignment: listTileTitleAlignment ?? ListTileTitleAlignment.center,
        title: title.isNotEmpty
            ? FittedBox(
                fit: BoxFit.scaleDown,
                alignment: AlignmentDirectional.centerStart,
                child: CustomText(
                  text: title,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                  maxLine: maxLine,
                ),
              )
            : const SizedBox.shrink(),
        trailing: trailing,
        onTap: onTap,
        subtitle: subtitle,
      ),
    );
  }
}

class SmartFingersCarousel extends StatefulWidget {
  final List<String>? imageUrls;
  final List<String>? videosUrls;

  final String? title;
  final double maxHeight;
  final double maxWidth;
  const SmartFingersCarousel({
    super.key,
    required this.imageUrls,
    required this.title,
    this.maxHeight = 200.0,
    this.maxWidth = 200.0,
    required this.videosUrls,
  });

  @override
  State<SmartFingersCarousel> createState() => _SmartFingersCarouselState();
}

class _SmartFingersCarouselState extends State<SmartFingersCarousel> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null &&
            (widget.videosUrls!.isNotEmpty || widget.imageUrls!.isNotEmpty))
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: CustomText(
              text: widget.title!,
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),

        if (widget.videosUrls != null && widget.videosUrls!.isNotEmpty)
          PostMediaSlider(
            borderRadius: BorderRadius.circular(8),
            media: widget.videosUrls!.asMap().entries.map((entry) {
              final index = entry.key;
              final videoFile = entry.value;
              final videoUrl = videoFile; // Adjust this path as needed
              return DailyPostMedia(
                id: index,
                fileUrl: videoUrl,
                type: MediaType.video,
              );
            }).toList(),
          ),
        if (widget.imageUrls != null)
          ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 100),
            child: CarouselView(
              itemExtent: widget.maxWidth,
              shrinkExtent: widget.maxHeight,
              // itemSnapping: true,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              onTap: (int index) {
                Navigator.push(
                  context,
                  PageRouteBuilder(pageBuilder: (context, __, ___) {
                    return ImageGalery(
                      images: widget.imageUrls ?? [],
                      index: index,
                      heroTagPrefix: 'slider_hero_tag_',
                    );
                  }),
                );
              },
              children: widget.imageUrls?.map((imageUrl) {
                    return UncontainedLayoutCard(
                      imageUrl: imageUrl,
                      imageUrls: widget.imageUrls ?? [],
                    );
                  }).toList() ??
                  [],
            ),
          ),
        // SizedBox(
        //   height: 100,
        //   width: 100,
        //   child: PostMediaSlider(
        //     media: [
        //       DailyPostMedia(
        //           id: 1,
        //           file: widget.videosUrls![0],
        //           fileUrl: widget.videosUrls?[0],
        //           type: MediaType.video),
        //     ],
        //   ),
        // ),
        // ConstrainedBox(
        //   constraints: BoxConstraints(maxHeight: widget.maxHeight),
        //   child: CarouselView(
        //     itemExtent: widget.maxWidth,
        //     shrinkExtent: widget.maxHeight,
        //     onTap: (p) {
        //       // _handlePlayPause(true);
        //     },
        //     children: widget.videosUrls!.map((imageUrl) {
        //       print(imageUrl);
        //       return
        //     }).toList(),
        //   ),
        // ),
      ],
    );
  }
}

class UncontainedLayoutCard extends StatelessWidget {
  final String imageUrl;
  final List<String?> imageUrls;

  const UncontainedLayoutCard({
    super.key,
    required this.imageUrl,
    required this.imageUrls,
  });

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: AppColor.greyBorder,
      child: SmartFingersImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
      ),
    );
  }
}

class SmartFingersBackBotton extends StatelessWidget {
  const SmartFingersBackBotton({
    super.key,
    this.color,
    this.icon,
    this.onPressed,
    this.style,
    this.tooltip,
  });
  final Widget? icon;
  final VoidCallback? onPressed;
  final Color? color;
  final ButtonStyle? style;
  final String? tooltip;

  void _onPressedCallback(BuildContext context) => Navigator.maybePop(context);

  String _getTooltip(BuildContext context) {
    return MaterialLocalizations.of(context).backButtonTooltip;
  }

  @override
  Widget build(BuildContext context) {
    assert(debugCheckHasMaterialLocalizations(context));
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 15.w,
        vertical: 15.h,
      ),
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.r),
            color: AppColor.scaffoldColor),
        child: Center(
            child: IconButton(
          padding: EdgeInsets.zero,
          icon: Padding(
            padding: const EdgeInsets.all(4),
            child: icon ??
                SmartFingersImage(
                  imageUrl: AppAssets.arrowBackword,
                  color: AppColor.primaryColor,
                ),
          ),
          style: style,
          color: color,
          tooltip: tooltip ?? _getTooltip(context),
          onPressed: () {
            if (onPressed != null) {
              onPressed!();
            } else {
              _onPressedCallback(context);
            }
          },
        )),
      ),
    );
  }
}

class SmartFingersTabBar extends StatelessWidget {
  const SmartFingersTabBar({
    super.key,
    required this.onTap,
    required this.tabs,
    this.controller,
    this.padding,
    this.labelPadding,
    this.dividerHeight,
    this.indicatorWeight,
    this.indicator,
    this.indicatorColor,
    this.labelColor,
    this.isScrollable = false,
    this.tabAlignment,
  });

  final Function(int) onTap;
  final List<Widget> tabs;
  final TabController? controller;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? labelPadding;
  final double? dividerHeight;
  final double? indicatorWeight;
  final Decoration? indicator;
  final Color? indicatorColor;
  final Color? labelColor;
  final bool isScrollable;
  final TabAlignment? tabAlignment;

  @override
  Widget build(BuildContext context) {
    return TabBar(
      controller: controller,
      splashFactory: NoSplash.splashFactory,
      indicatorColor: indicatorColor ?? AppColor.primaryColor,
      indicatorSize: TabBarIndicatorSize.label,
      indicatorWeight: indicatorWeight ?? 0,
      labelPadding: labelPadding ?? const EdgeInsets.symmetric(horizontal: 4),
      labelColor: labelColor,
      padding: padding,
      onTap: onTap,
      dividerHeight: dividerHeight,
      isScrollable: isScrollable,
      tabAlignment: tabAlignment,
      indicator: indicator ??
          BoxDecoration(
            border: Border(
              bottom: BorderSide(color: AppColor.primaryColor, width: 3),
            ),
          ),
      tabs: tabs,
    );
  }
}
