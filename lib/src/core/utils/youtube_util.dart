import 'package:http/http.dart' as http;

class YoutubeUtils {
  static final Map<String, String> _thumbnailCache = {};

  /// Validates if a given string is a valid YouTube URL.
  static bool isValidYoutubeUrl(String url) {
    if (url.isEmpty) {
      return false;
    }

    // Regular expression to match various YouTube URL formats.
    // Enhanced regex to handle more cases including shorts and live streams.
    final RegExp youtubeRegex = RegExp(
        r'^(?:https?:\/\/)?(?:www\.)?(?:m\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=|shorts\/|live\/))([a-zA-Z0-9_-]{11})(?:\S+)?$');
    return youtubeRegex.hasMatch(url);
  }

  /// Extracts the YouTube video ID from a valid YouTube URL.
  static String? extractYoutubeVideoId(String url) {
    if (!isValidYoutubeUrl(url)) {
      return null;
    }

    final RegExp youtubeRegex = RegExp(
        r'^(?:https?:\/\/)?(?:www\.)?(?:m\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=|shorts\/|live\/))([a-zA-Z0-9_-]{11})(?:\S+)?$');
    final match = youtubeRegex.firstMatch(url);

    if (match != null && match.groupCount >= 1) {
      return match.group(1);
    }
    return null;
  }

  /// Extracts the YouTube video ID from a valid YouTube URL using validators package.
  static String? extractYoutubeVideoIdUsingValidators(String url) {
    final Uri? uri = Uri.tryParse(url);
    if (uri == null) {
      return null;
    }

    if (uri.host.contains('youtube.com')) {
      if (uri.pathSegments.contains('watch')) {
        return uri.queryParameters['v'];
      } else if (uri.pathSegments.contains('shorts')) {
        return uri.pathSegments.last;
      } else if (uri.pathSegments.contains('live')) {
        return uri.pathSegments.last;
      } else {
        final RegExp regex = RegExp(r'/([a-zA-Z0-9_-]{11})');
        final match = regex.firstMatch(uri.path);
        if (match != null && match.groupCount >= 1) {
          return match.group(1);
        }
      }
    } else if (uri.host.contains('youtu.be')) {
      return uri.pathSegments.first;
    }
    return null;
  }

  static Future<String?> getYoutubeThumbnailUrl(String videoId) async {
    List<String> resolutions = [
      'maxresdefault.jpg',
      'sddefault.jpg',
      'hqdefault.jpg',
      'mqdefault.jpg',
      'default.jpg',
    ];

    if (_thumbnailCache.containsKey(videoId)) {
      return _thumbnailCache[videoId];
    }

    for (String resolution in resolutions) {
      String url = 'http://img.youtube.com/vi/$videoId/$resolution';
      try {
        final response = await http.head(Uri.parse(url));
        if (response.statusCode == 200) {
          _thumbnailCache[videoId] = url;
          return url;
        }
      } catch (e) {
        print('Error checking thumbnail: $e');
      }
    }
    // Return a default placeholder image URL if none are found.
    return 'assets/placeholder_thumbnail.png'; // Or a network URL
  }
}
