// ignore_for_file: depend_on_referenced_packages

import 'package:get/get.dart';

import '../locale/locale_controller.dart';
import '../utils/upload_progress_controller.dart';
import '../../features/splash/controller/splash_controller.dart';

class DependencyInjection {
  static Future<void> init() async {
    Get.put<LocaleController>(LocaleController(), permanent: true);

    // Register the UploadProgressController for tracking upload progress
    Get.put<UploadProgressController>(UploadProgressController(),
        permanent: true);

    // Network Info
    // Get.lazyPut<NetworkInfo>(
    //     () => NetworkInfoImpl(InternetConnectionChecker()));
    Get.lazyPut(() => SplashController(), fenix: true);
  }
}
