import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_color.dart';

class AppTheme {
  static ThemeData theme = ThemeData(
    primaryColor: AppColor.primaryColor,
    fontFamily: GoogleFonts.tajawal().fontFamily,
    scaffoldBackgroundColor: AppColor.scaffoldColor,
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.white,
      titleTextStyle: TextStyle(
        color: AppColor.primaryColor,
        fontWeight: FontWeight.bold,
        fontFamily: GoogleFonts.tajawal().fontFamily,
        fontSize: 20,
      ),
      iconTheme: IconThemeData(
        color: AppColor.primaryColor,
      ),
      actionsIconTheme: IconThemeData(
        color: AppColor.primaryColor,
      ),
    ),
    sliderTheme: SliderThemeData(
      activeTrackColor: AppColor.primaryColor,
      inactiveTrackColor: AppColor.primaryColor.withOpacity(0.5),
      thumbColor: AppColor.primaryColor,
      overlayColor: AppColor.primaryColor.withOpacity(0.2),
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: AppColor.primaryColor,
      foregroundColor: Colors.white,
    ),
    popupMenuTheme: const PopupMenuThemeData(
      color: Colors.white,
      menuPadding: EdgeInsets.all(0),
      elevation: 1,
    ),
    tabBarTheme: TabBarTheme(
      unselectedLabelColor: AppColor.greyColor4,
      unselectedLabelStyle: TextStyle(
        fontFamily: GoogleFonts.tajawal().fontFamily,
        fontWeight: FontWeight.bold,
        color: AppColor.greyColor4,
      ),
      labelColor: AppColor.primaryColor,
      labelStyle: TextStyle(
          fontFamily: GoogleFonts.tajawal().fontFamily,
          fontWeight: FontWeight.bold,
          color: AppColor.primaryColor),
      indicator: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColor.primaryColor, width: 3),
        ),
      ),
      indicatorColor: AppColor.primaryColor,
    ),
  );
}
