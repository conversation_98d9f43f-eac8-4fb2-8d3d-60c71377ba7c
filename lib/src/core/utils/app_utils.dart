import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app_color.dart';

class AppUtils {
  static void showSuccessSnackBar({required String message}) {
    Get.snackbar(
      'Success'.tr,
      message.tr,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
    );
  }

  static void showErrorSnackBar({required String message}) {
    Get.snackbar(
      'Error'.tr,
      message.tr,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
    );
  }

  static void showInfoSnackBar({required String message}) {
    Get.snackbar(
      'Info'.tr,
      message.tr,
      backgroundColor: AppColor.primaryColor,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
    );
  }
}
