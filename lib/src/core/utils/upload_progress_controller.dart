import 'package:get/get.dart';

/// A controller to track and display upload progress
class UploadProgressController extends GetxController {
  static UploadProgressController get instance =>
      Get.find<UploadProgressController>();

  // Observable variables
  final RxDouble compressionProgress = 0.0.obs;
  final RxDouble uploadProgress = 0.0.obs;
  final RxBool isCompressing = false.obs;
  final RxBool isUploading = false.obs;
  final RxString currentOperation = ''.obs;

  /// Start compression tracking
  void startCompression() {
    isCompressing.value = true;
    compressionProgress.value = 0.0;
    currentOperation.value = 'Compressing images...'.tr;
  }

  /// Update compression progress
  void updateCompressionProgress(double progress) {
    compressionProgress.value = progress;
  }

  /// End compression tracking
  void endCompression() {
    isCompressing.value = false;
    compressionProgress.value = 1.0;
  }

  /// Start upload tracking
  void startUpload() {
    isUploading.value = true;
    uploadProgress.value = 0.0;
    currentOperation.value = 'Uploading...'.tr;
  }

  /// Update upload progress
  void updateUploadProgress(double progress) {
    uploadProgress.value = progress;
  }

  /// End upload tracking
  void endUpload() {
    isUploading.value = false;
    uploadProgress.value = 1.0;
    currentOperation.value = '';
  }

  /// Reset all progress
  void reset() {
    isCompressing.value = false;
    isUploading.value = false;
    compressionProgress.value = 0.0;
    uploadProgress.value = 0.0;
    currentOperation.value = '';
  }
}
