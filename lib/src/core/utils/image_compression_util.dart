import 'dart:io';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImageCompressionUtil {
  /// Compresses an image file and returns the compressed file
  /// 
  /// Parameters:
  /// - [file]: The original image file to compress
  /// - [quality]: The quality of the compressed image (0-100)
  /// - [maxWidth]: The maximum width of the compressed image
  /// - [maxHeight]: The maximum height of the compressed image
  /// 
  /// Returns a [Future<File>] containing the compressed image
  static Future<File> compressImage({
    required File file,
    int quality = 70,
    int maxWidth = 1024,
    int maxHeight = 1024,
  }) async {
    // Get file extension
    final fileExtension = path.extension(file.path).toLowerCase();
    
    // Create a temporary directory to store the compressed image
    final dir = await getTemporaryDirectory();
    final targetPath = "${dir.absolute.path}/${DateTime.now().millisecondsSinceEpoch}$fileExtension";
    
    // Check if the file is an image that can be compressed
    if (fileExtension == '.jpg' || fileExtension == '.jpeg' || fileExtension == '.png') {
      try {
        final result = await FlutterImageCompress.compressAndGetFile(
          file.absolute.path,
          targetPath,
          quality: quality,
          minWidth: maxWidth,
          minHeight: maxHeight,
        );
        
        if (result != null) {
          // Check if compression actually reduced the file size
          final originalSize = await file.length();
          final compressedSize = await result.length();
          
          if (compressedSize < originalSize) {
            print('Image compressed: ${(originalSize / 1024).toStringAsFixed(2)}KB -> ${(compressedSize / 1024).toStringAsFixed(2)}KB');
            return File(result.path);
          } else {
            print('Compression did not reduce file size. Using original file.');
            return file;
          }
        }
      } catch (e) {
        print('Error compressing image: $e');
      }
    }
    
    // Return the original file if compression failed or file is not an image
    return file;
  }
  
  /// Checks if a file is an image that can be compressed
  static bool isCompressibleImage(File file) {
    final fileExtension = path.extension(file.path).toLowerCase();
    return fileExtension == '.jpg' || fileExtension == '.jpeg' || fileExtension == '.png';
  }
}
