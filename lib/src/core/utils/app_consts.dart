// ignore_for_file: constant_identifier_names

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../../features/auth/services/auth_service.dart';
import 'package:universal_platform/universal_platform.dart';

// const baseUrl = "https://ryadeapp.technoplus.dev/api/";
const baseUrl = "https://app.rawdaljinan.com/api/";
// const imageBaseUrl = "https://ryadeapp.technoplus.dev/storage/";
const imageBaseUrl = "https://app.rawdaljinan.com/uploads/";

final box = GetStorage();

class Roles {
  static const String Teacher = 'teacher';
  static const String Student = 'student';
  static const String Supervisor = 'supervisor';
  static const String Guardian = 'guardian';
  static const String AdminAssistant = 'admin_assistant';
  static String getPluralLabel(String role) {
    switch (role) {
      case Roles.Student:
        return 'Students'.tr;
      case Roles.Teacher:
        return 'Teachers'.tr;
      case Roles.Supervisor:
        return 'Administration'.tr;
      case Roles.Guardian:
        return 'Guardians'.tr;
      case Roles.AdminAssistant:
        return 'Admin Assistants'.tr;
      default:
        return role;
    }
  }
}

class AppConsts {
  static Dio get unauthenticatedDio {
    var dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        headers: defaultHeaders,
      ),
    );
    dio.interceptors.add(PrettyDioLogger(enabled: kDebugMode));
    return dio;
  }

  static Dio get authenticatedDio {
    var token = box.read('token');
    var additionalHeaders = {};
    if (Get.isRegistered<AuthService>()) {
      token = AuthService.instance.token.value;
      var parentUser = AuthService.instance.parentUser.value;
      if (parentUser != null) {
        additionalHeaders = {
          'parent-id': parentUser.id.toString(),
        };
      }
    }
    var dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        headers: {
          'Authorization': 'Bearer $token',
          ...defaultHeaders,
          ...additionalHeaders
        },
      ),
    );
    dio.interceptors.add(PrettyDioLogger(
        enabled: kDebugMode,
        requestHeader: true,
        logPrint: (obj) => Get.log(obj.toString())));
    return dio;
  }

  static Map<String, String> get defaultHeaders => {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'locale': Get.locale?.languageCode ?? 'ar',
      };
}

/// check if the environment is web
final bool kIsWeb = UniversalPlatform.isWeb;
final bool isIos = UniversalPlatform.isIOS;
final bool isAndroid = UniversalPlatform.isAndroid;
final bool isMacOS = UniversalPlatform.isMacOS;
final bool isWindow = UniversalPlatform.isWindows;
final bool isFuchsia = UniversalPlatform.isFuchsia;
final bool isMobile = UniversalPlatform.isIOS || UniversalPlatform.isAndroid;
final bool isDesktop = UniversalPlatform.isMacOS || UniversalPlatform.isWindows;
