import 'package:flutter/material.dart';

class AppColor {
  static const Color greyColor = Color(0XFFC5C5C5);
  static Color primaryColor = const Color(0XFF157DBC);
  static Color whiteColor = Colors.white;
  static Color greyColor2 = Colors.grey;
  static Color scaffoldColor = const Color(0XFFEEEEEE);
  static Color primaryColor2 = const Color(0XFF308EC1);
  static Color amberColor = const Color(0XFFebdd58);
  static Color redColor = const Color(0XFFFF484C);
  static Color amber2Color = const Color(0XFFEEDF5E);
  static Color greyColor3 = const Color(0XFF7B7B7B);
  static Color commentScaffoldColor = const Color(0XFFFFFBFB);
  static Color offsetPrimary = const Color(0XFFE1EEF6);
  static Color greenColor = const Color(0XFF13D94A);
  static Color green2Color = const Color(0XFF85C54A);
  static Color offsetGreenColor = const Color(0XFFE0FAE8);
  static Color offsetRedColor = const Color(0XFFFFE7E8);
  static Color blackColor = Colors.black;
  static Color orangeColor = const Color(0XFFFF9100);
  static Color greenHeader = const Color(0XFF008B86);
  static Color greyBorder = const Color(0XFFC1C1C1);
  static Color greyColor4 = const Color(0XFF717171);
  static Color greyColor5 = const Color(0XFF545454);
  static Color redColor2 = const Color(0XFFE04D49);
  static Color yelloLight = const Color(0XFFEEDD5E);

  static Color fromHex(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  static List<Color> colorList = [
    const Color(0XFF4B97D0),
    const Color(0XFFE04D49),
    const Color(0XFF73B831),
  ];

  static List<Color> blueGradient = [
    const Color(0XFF5DA4D9),
    const Color(0XFF247CBE),
  ];

  static List<Color> orangeGradient = [
    const Color(0XFFFFDC53),
    const Color(0XFFFF9100),
  ];

  static List<Color> yellowGradient = [
    const Color(0XFFEDDE5D),
    const Color(0XFFD8C62C),
  ];

  static List<Color> skyGradient = [
    const Color(0XFFB6D8FD),
    const Color(0xFF7BB0F6),
  ];

  static List<Color> cyanGradient = [
    const Color(0XFF3CC9CF),
    const Color(0XFF109EA4)
  ];

  static List<Color> purpleGradient = [
    const Color(0XFFBEB9FF),
    const Color(0XFF8B7BEE)
  ];
  static List<Color> redGradient = [
    const Color(0XFFF37F7C),
    const Color(0XFFE04D49)
  ];
  static List<Color> greenGradient = [
    const Color(0XFF85C548),
    const Color(0XFF58B004)
  ];

  // Dark blue gradient
  static List<Color> darkBlueGradient = [
    const Color(0xFF2E86C1), // Blue
    const Color(0xFF1A5276) // Dark Blue
  ];
}
