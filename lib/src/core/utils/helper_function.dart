import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import '../data/providers/api_provider.dart';
import '../router/app_router.dart';
import 'app_color.dart';
import 'app_consts.dart';
import '../../features/comments/presentation/widgets/comments_bottom_sheet.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:intl/intl.dart' as intl;
import 'package:share_plus/share_plus.dart';

class HelperFunction {
  static void openUrl(url) async {
    launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
  }

  static void shareText(String text) {
    final box = Get.context?.findRenderObject() as RenderBox?;
    Share.share(text,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
  }

  static void shareUrl(String url) {
    final box = Get.context?.findRenderObject() as RenderBox?;
    Share.shareUri(Uri.parse(url),
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
  }

  static Future<void> shareImageFormCacheNetwork(String url) async {
    final box = Get.context?.findRenderObject() as RenderBox?;
    var file = await DefaultCacheManager().getSingleFile(url);
    Share.shareXFiles([XFile(file.path)],
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
  }

  static showSuccessMessage(String message) {
    Get.snackbar(
      'Success'.tr,
      message,
      colorText: Colors.white,
      backgroundColor: Colors.green,
      icon: const Icon(Icons.check, color: Colors.white),
    );
  }

  static showErrorMessage(String message) {
    Get.snackbar(
      'Error'.tr,
      message,
      colorText: Colors.white,
      backgroundColor: Colors.red,
      icon: const Icon(Icons.error_outline, color: Colors.white),
    );
  }

  static Size getTextSize({required String text, TextStyle? style}) {
    final TextPainter textPainter = TextPainter(
        text: TextSpan(text: text, style: style),
        maxLines: 1,
        textDirection: TextDirection.ltr)
      ..layout(minWidth: 0, maxWidth: double.infinity);
    return textPainter.size;
  }

  static Future<bool> showTermsDialogIfNeeded() async {
    // box.write('accept_terms', false);
    if (!(box.read<bool>('accept_terms') ?? false)) {
      var result = await Get.dialog<bool>(
        AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'terms_and_conditions'.tr,
            textAlign: TextAlign.center,
            style: Get.textTheme.titleLarge,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: Get.textTheme.bodyMedium?.copyWith(height: 1.5),
                  children: [
                    TextSpan(text: '${'read_terms'.tr} '),
                    TextSpan(
                      text: 'terms_and_conditions'.tr,
                      style: TextStyle(
                        color: Get.theme.primaryColor,
                        decoration: TextDecoration.underline,
                        fontWeight: FontWeight.w600,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () async {
                          final url =
                              Uri.parse('https://app.rawdaljinan.com/terms');
                          if (await canLaunchUrl(url)) {
                            await launchUrl(url,
                                mode: LaunchMode.externalApplication);
                          } else {
                            Get.snackbar('Error', 'error_opening_url'.tr);
                          }
                        },
                    ),
                    TextSpan(text: ' ${'before_proceeding'.tr}'),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        box.write('accept_terms', true);
                        Get.back(result: true);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Text(
                          'agree'.tr,
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Get.back(result: false);
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.red),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Text(
                          'reject'.tr,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        barrierDismissible: false,
      );
      return result ?? false;
    } else {
      return true;
    }
  }

  static Future<void> showCommentsBottomSheet(
      {required int id, required String type}) async {
    var result = await showTermsDialogIfNeeded();
    if (result) {
      Get.bottomSheet(
        CommentsBottomSheet(
          id: id,
          type: type,
        ),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(
              16,
            ),
          ),
        ),
        backgroundColor: Get.theme.cardColor,
        isDismissible: false,
        enableDrag: false,
        isScrollControlled: true,
      );
    }
  }

  static Future<bool> checkConnectivity() async {
    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());

    var hasConnection = !connectivityResult.contains(ConnectivityResult.none);
    return hasConnection;
  }

  static String timeAgo(DateTime date) {
    final time = DateTime.now().difference(date);
    if (time.inHours < 1) {
      return timeago.format(date,
          locale: Get.locale?.languageCode, allowFromNow: true);
    }
    // is today
    else if (date.day == DateTime.now().day) {
      return intl.DateFormat('h:m a', Get.locale?.languageCode).format(date);
    }
    // is same year
    else if (date.year == DateTime.now().year) {
      return intl.DateFormat('dd MMM h:m a', Get.locale?.languageCode)
          .format(date);
    } else {
      return intl.DateFormat('dd MMM yyyy h:m a', Get.locale?.languageCode)
          .format(date);
    }
  }

  static Future<void> navigateToChatDetails({
    int? receiverId,
    int? conversationId,
  }) async {
    bool canPop = false;
    Get.dialog(
      PopScope(
        canPop: canPop,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: CircularProgressIndicator(
              color: AppColor.primaryColor,
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
    var chat = await ApiProvider(AppConsts.authenticatedDio)
        .getChatByReceiverIdOrConversationId(
      receiverId: receiverId,
      conversationId: conversationId,
    );
    canPop = true;
    Get.back();
    Get.toNamed(AppRouter.chatDetails, arguments: chat);
  }
}
