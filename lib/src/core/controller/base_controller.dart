import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../data/enums/page_status.dart';
import '../data/providers/api_provider.dart';
import '../router/app_router.dart';
import '../utils/app_consts.dart';
import '../utils/extensions.dart';
import '../utils/helper_function.dart';

class BaseController extends GetxController {
  Rx<PageStatus> pageStatus = PageStatus.idle.obs;

  Rx<Map<String, dynamic>> validationErrors = Rx<Map<String, dynamic>>({});

  ApiProvider get apiProvider => ApiProvider(AppConsts.authenticatedDio);

  ApiProvider get unauthenticatedApiProvider =>
      ApiProvider(AppConsts.unauthenticatedDio);

  void clearValidationErrors() => validationErrors.value = {};

  void setPageStatus(PageStatus status) => pageStatus.value = status;

  void updateEmptyStatus(List data) {
    if (data.isEmpty) {
      setPageStatus(PageStatus.empty);
    } else {
      setPageStatus(PageStatus.loaded);
    }
  }

  Future<bool> checkConnectivity() async {
    var hasConnection = await HelperFunction.checkConnectivity();
    if (!hasConnection) {
      setPageStatus(PageStatus.noInternet);
    }
    return hasConnection;
  }

  Future<void> callApi(Future<void> Function() callback,
      {bool withLoading = true}) async {
    if (withLoading) setPageStatus(PageStatus.loading);
    if (await checkConnectivity()) {
      try {
        await callback();
        if (withLoading) setPageStatus(PageStatus.loaded);
      } catch (e) {
        handleError(e);
      }
    }
  }

  void handleError(dynamic exception) {
    if (exception is TypeError) {
      // log errors to console
      printLog((exception).stackTrace);
    }
    setPageStatus(PageStatus.noInternet);
    if (exception is DioException) {
      if (exception.response?.statusCode == 401) {
        setPageStatus(PageStatus.serverError);
        box.remove('token');
        Get.offAndToNamed(AppRouter.loginScreen);
      } else if (exception.response?.statusCode == 500) {
        setPageStatus(PageStatus.serverError);
      } else if (exception.response?.statusCode == 422) {
        validationErrors.value = exception.response?.data['errors'] ?? {};
        setPageStatus(PageStatus.validationError);
        HelperFunction.showErrorMessage(exception.response?.data['message']);
      }
    } else {
      setPageStatus(PageStatus.unknownError);
    }
  }
}
