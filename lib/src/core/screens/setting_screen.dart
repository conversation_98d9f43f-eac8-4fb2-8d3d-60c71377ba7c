import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../locale/locale_controller.dart';
import 'layout_screen.dart';
import '../utils/app_color.dart';
import '../utils/app_assets.dart';
import '../widgets/custom_text.dart';

import '../router/app_router.dart';

class SettingSreen extends StatelessWidget {
  const SettingSreen({super.key});

  @override
  Widget build(BuildContext context) {
    final LocaleController localeController = Get.put(LocaleController());

    return LayoutScreen(
      title: 'Settings'.tr,
      body: Padding(
        padding: EdgeInsets.all(15.h),
        child: Column(
          children: [
            25.verticalSpace,
            Row(
              children: [
                Icon(
                  Icons.language,
                  color: AppColor.primaryColor,
                ),
                10.horizontalSpace,
                CustomText(
                    text: 'Language'.tr,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColor.primaryColor),
              ],
            ),
            20.verticalSpace,
            Container(
              width: double.infinity,
              height: 150.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: AppColor.whiteColor,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Obx(
                    () => ListTile(
                      title: CustomText(
                        text: 'Arabic'.tr,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColor.primaryColor,
                      ),
                      trailing: localeController.currentLanguage == 'Arabic'
                          ? SvgPicture.asset(AppAssets.activeLang)
                          : SvgPicture.asset(AppAssets.unActiveLang),
                      onTap: () => localeController.changeLocale('ar'),
                    ),
                  ),
                  Obx(
                    () => ListTile(
                      title: CustomText(
                        text: 'English'.tr,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColor.primaryColor,
                      ),
                      trailing: localeController.currentLanguage == 'English'
                          ? SvgPicture.asset(AppAssets.activeLang)
                          : SvgPicture.asset(AppAssets.unActiveLang),
                      onTap: () => localeController.changeLocale('en'),
                    ),
                  ),
                ],
              ),
            ),
            30.verticalSpace,
            InkWell(
              child: Align(
                alignment: AlignmentDirectional.centerStart,
                child: Row(
                  children: [
                    Icon(Icons.lock, size: 16, color: AppColor.primaryColor),
                    5.horizontalSpace,
                    CustomText(
                        text: 'Change Password'.tr,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColor.primaryColor),
                  ],
                ),
              ),
              onTap: () => Get.toNamed(
                AppRouter.replacePasswordScreen,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
