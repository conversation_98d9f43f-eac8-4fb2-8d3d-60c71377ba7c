import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../controller/base_controller.dart';
import '../data/enums/page_status.dart';
import '../utils/app_assets.dart';
import '../utils/helper_function.dart';
import '../widgets/errors/empty_widget.dart';
import '../widgets/errors/no_internet_error_widget.dart';
import '../widgets/drawer/custom_drawer.dart';
import '../widgets/errors/loading_widget.dart';
import '../../../custom_packages/marquee/marquee.dart';
import '../../features/notification/services/notification_service.dart';
import '../router/app_router.dart';
import '../utils/app_color.dart';
import '../utils/extensions.dart';

class LayoutScreen extends StatefulWidget {
  const LayoutScreen({
    super.key,
    required this.title,
    required this.body,
    this.withBackground = true,
    this.withDrawer = false,
    this.bottom,
    this.floatingActionButton,
    this.controller,
    this.bodyHeader,
    this.hideActionButton,
    this.centerTitle = true,
    this.emptyWidegt,
    this.bodyFooter,
    this.subtitle,
    this.onPopInvokedWithResult, this.backgroundColor,
  });
  final String title;
  final String? subtitle;
  final Widget? bodyHeader;
  final Widget? bodyFooter;
  final Widget body;
  final bool withBackground;
  final bool withDrawer;
  final PreferredSizeWidget? bottom;
  final Widget? floatingActionButton;
  final BaseController? controller;
  final bool? hideActionButton;
  final bool? centerTitle;
  final Widget? emptyWidegt;
  final PopInvokedWithResultCallback? onPopInvokedWithResult;
  final Color? backgroundColor;
  @override
  State<LayoutScreen> createState() => _LayoutScreenState();
}

class _LayoutScreenState extends State<LayoutScreen> {
  String _getNotificationTooltip(BuildContext context) {
    return 'Notification Button'.tr;
  }

  String _getHomeTooltip(BuildContext context) {
    return 'Home'.tr;
  }

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey(debugLabel: 'Home');

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: widget.onPopInvokedWithResult,
      child: Scaffold(
        key: _scaffoldKey,
        backgroundColor: widget.backgroundColor,
        drawer: widget.withDrawer ? const CustomDrawer() : null,
        floatingActionButton: widget.floatingActionButton,
        appBar: AppBar(
          centerTitle: widget.centerTitle,
          title: LayoutBuilder(builder: (context, constraints) {
            bool titleMoving = false;
            titleMoving = (HelperFunction.getTextSize(
                      text: widget.title.tr,
                      style: Get.theme.appBarTheme.titleTextStyle,
                    ).width -
                    16) >
                constraints.maxWidth;
            return titleMoving
                ? Column(
                    crossAxisAlignment: (widget.centerTitle ?? false)
                        ? CrossAxisAlignment.center
                        : CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: kToolbarHeight,
                        child: Marquee(
                          text: widget.title.tr,
                          scrollAxis: Axis.horizontal,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          blankSpace: 30,
                          velocity: 50,
                          startPadding: 20,
                          startAfter: const Duration(seconds: 2),
                          pauseAfterRound: const Duration(seconds: 1),
                          showFadingOnlyWhenScrolling: true,
                          fadingEdgeStartFraction: 0.1,
                          fadingEdgeEndFraction: 0.1,
                          accelerationDuration: const Duration(seconds: 1),
                          accelerationCurve: Curves.linear,
                          decelerationDuration:
                              const Duration(milliseconds: 500),
                          decelerationCurve: Curves.easeOut,
                          textDirection: TextDirection.ltr,
                        ),
                      ),
                      if (widget.subtitle != null &&
                          widget.subtitle!.isNotEmpty)
                        Text(
                          widget.subtitle!,
                          style: Get.theme.textTheme.bodySmall,
                        ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: (widget.centerTitle ?? false)
                        ? CrossAxisAlignment.center
                        : CrossAxisAlignment.start,
                    children: [
                      Text(widget.title.tr),
                      if (widget.subtitle != null &&
                          widget.subtitle!.isNotEmpty)
                        Text(
                          widget.subtitle!,
                          style: Get.theme.textTheme.bodySmall,
                        ),
                    ],
                  );
          }),
          bottom: widget.bottom,
          scrolledUnderElevation: 0.2,
          actions: [
            IconButton(
              padding: EdgeInsets.zero,
              icon: Obx(
                () => Badge.count(
                  count: NotificationService.instance.unreadCount.value,
                  isLabelVisible:
                      NotificationService.instance.unreadCount.value > 0,
                  offset: Offset(-6, -6),
                  child: SmartFingersImage(
                    imageUrl: AppAssets.notification,
                    color: AppColor.primaryColor,
                    height: 24,
                  ),
                ),
              ),
              tooltip: _getNotificationTooltip(context),
              onPressed: () {
                Get.toNamed(
                  AppRouter.notificationScreen,
                );
              },
            ),
          ],
        ),
        // resizeToAvoidBottomInset: false,
        body: Column(
          children: [
            widget.bodyHeader ?? const SizedBox(),
            Expanded(
              child: widget.withBackground
                  ? Stack(
                      children: [
                        _buildBackground(),
                        Column(
                          children: [
                            Expanded(child: _buildBody()),
                            widget.bodyFooter ?? const SizedBox(),
                          ],
                        ),
                      ],
                    )
                  : Column(
                      children: [
                        Expanded(child: _buildBody()),
                        widget.bodyFooter ?? const SizedBox(),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SvgPicture.asset(
        AppAssets.bg,
        fit: BoxFit.cover,
      ),
    );
  }

  Widget _buildBody() {
    return SizedBox(
      width: double.maxFinite,
      height: double.maxFinite,
      child: widget.controller == null
          ? widget.body
          : Obx(
              () {
                switch (widget.controller!.pageStatus.value) {
                  case PageStatus.idle:
                    return const SizedBox();
                  case PageStatus.loading:
                    return const LoadingWidget();
                  case PageStatus.noInternet:
                    return const NoInternetErrorWidget();
                  case PageStatus.empty:
                    return widget.emptyWidegt ?? const EmptyWidget();
                  case PageStatus.loaded:
                    return widget.body;
                  default:
                    return widget.body;
                }
              },
            ),
    );
  }
}
