import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';
import '../utils/app_assets.dart';
import '../utils/app_color.dart';
import '../utils/extensions.dart';
import '../widgets/custom_text.dart';

import '../../features/auth/services/auth_service.dart';
import '../widgets/buttons/submit_button.dart';
import '../widgets/form_fields/custom_text_field.dart';
import 'layout_screen.dart';

class ChangePasswordScreen extends StatelessWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    String currentPassword = '';
    String newPassword = '';
    String confirmPassword = '';

    return LayoutScreen(
      title: 'Change Password'.tr,
      body: Form(
        key: form<PERSON><PERSON>,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 20),
            child: Column(
              children: [
                CircleAvatar(
                  backgroundColor: AppColor.yelloLight.withOpacity(0.3),
                  radius: 60,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: SmartFingersImage(
                      imageUrl: AppAssets.newPassword,
                    ),
                  ),
                ),
                20.verticalSpace,
                CustomText(
                  text: 'Enter your new password'.tr,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                ),
                10.verticalSpace,
                CustomText(
                  text: 'New password Note'.tr,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.greyColor3,
                  maxLine: 2,
                  textAlign: TextAlign.center,
                ),
                30.verticalSpace,
                CustomTextField(
                  title: 'Current password'.tr,
                  hint: '',
                  filled: true,
                  filledColor: AppColor.whiteColor,
                  keyboardType: TextInputType.number,
                  validator: ValidationBuilder(
                    requiredMessage: "This field is required".tr,
                  ).required("This field is required".tr).build(),
                  obscureText: true,
                  onSaved: (value) {
                    currentPassword = value!;
                  },
                ),
                CustomTextField(
                  title: 'New password'.tr,
                  hint: '',
                  filled: true,
                  filledColor: AppColor.whiteColor,
                  keyboardType: TextInputType.number,
                  validator: ValidationBuilder(
                    requiredMessage: "This field is required".tr,
                  ).required("This field is required".tr).build(),
                  obscureText: true,
                  onSaved: (value) {
                    newPassword = value!;
                  },
                ),
                CustomTextField(
                  title: 'New password confirmation'.tr,
                  hint: '',
                  filled: true,
                  filledColor: AppColor.whiteColor,
                  keyboardType: TextInputType.number,
                  validator: ValidationBuilder(
                    requiredMessage: "This field is required".tr,
                  ).required("This field is required".tr).build(),
                  obscureText: true,
                  onSaved: (value) {
                    confirmPassword = value!;
                  },
                ),
                SubmitButton(
                  margin: const EdgeInsets.only(top: 16),
                  text: 'Change Password'.tr,
                  color: AppColor.primaryColor,
                  onSubmit: () async {
                    if (formKey.currentState!.validate()) {
                      formKey.currentState!.save();

                      if (newPassword != confirmPassword) {
                        Get.snackbar('Error'.tr, 'Passwords do not match'.tr);
                        return;
                      }

                      await AuthService.instance.changePassword(
                        currentPassword: currentPassword,
                        newPassword: newPassword,
                        newPasswordConfirmation: confirmPassword,
                      );
                      // try {

                      //   Get.dialog(
                      //       const Center(child: CircularProgressIndicator()),
                      //       barrierDismissible: false);

                      //   Get.back();

                      //   Get.snackbar(
                      //       'Success', 'Password changed successfully');
                      //   Navigator.of(context).pop();
                      // } catch (e) {
                      //   Get.back();
                      //   Get.snackbar('Error', 'Failed to change password: $e');
                      // }
                    }
                  },
                  formKey: formKey,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
