import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/app_consts.dart';
import '../../features/home/<USER>/home_controller.dart';

class LocaleController extends GetxController {
  static LocaleController instance = Get.find();
  Rx<Locale> initialLang =
      (box.read('langCode') == 'en' ? const Locale('en') : const Locale('ar'))
          .obs;

  void changeLocale(String langCode) {
    Locale locale = Locale(langCode);
    box.write('langCode', langCode);
    Get.delete<HomeController>();
    initialLang.value = locale;
    Get.updateLocale(locale);
  }

  String get currentLanguage =>
      initialLang.value.languageCode == 'ar' ? 'Arabic' : 'English';
}
