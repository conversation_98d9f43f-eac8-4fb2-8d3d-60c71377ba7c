// import 'package:drop_down_list/model/selected_list_item.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:rawd_aljenan/src/core/utils/app_color.dart';

// import 'drop_down.dart';
// import 'shimmer.dart';

// class SectionWithSubjectOld extends StatelessWidget {
//   const SectionWithSubjectOld(
//       {super.key,
//       this.isLoading = false,
//       required this.onSelectedSection,
//       this.sections,
//       required this.sectionTextController,
//       required this.sectionHint,
//       this.subjectController,
//       required this.subjects,
//       this.showSubjects = true,
//       this.showSections = true,
//       this.onSubjectTap});
//   final bool isLoading;
//   final dynamic Function(List<SelectedListItem>) onSelectedSection;

//   final List<SelectedListItem>? sections;
//   final TextEditingController sectionTextController;
//   final String sectionHint;
//   final TabController? subjectController;

//   final List<Widget> subjects;

//   final bool showSubjects;
//   final bool showSections;

//   final void Function(int)? onSubjectTap;

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Padding(
//           padding: EdgeInsets.symmetric(horizontal: 10.w),
//           child: Builder(builder: (context) {
//             return isLoading
//                 ? ShimmerWidget(
//                     height: 50.h,
//                   )
//                 : DropDownList(
//                     onSelectedItems: onSelectedSection,
//                     dataList: sections,
//                     textEditingController: sectionTextController,
//                     hint: sectionHint,
//                     isCitySelected: true,
//                   );
//           }),
//         ),
//         TabBar(
//           indicatorColor: AppColor.primaryColor,
//           labelColor: AppColor.primaryColor,
//           controller: subjectController,
//           tabs: subjects,
//           onTap: onSubjectTap,
//         )
//       ],
//     );
//   }
// }
