import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../utils/app_color.dart';
import 'custom_cached_network_image.dart';
import 'custom_text.dart';

class CustomListTile extends StatelessWidget {
  const CustomListTile({
    super.key,
    this.leadingIcon,
    required this.title,
    this.subtitle,
    this.subtitleIcon,
    this.fontSize,
    this.onTap,
    this.startAction,
    this.endAction,
    this.leadingIconColor,
    this.leading,
    this.border,
    this.detail,
    this.maxLine,
    this.leadingIconSize,
    this.trailing,
    this.backgroundColor,
    this.subtitleWidget,
    this.image,
    this.margin,
  });

  final String? image;
  final String? title;
  final String? subtitle;
  final Widget? subtitleWidget;
  final String? leadingIcon;
  final Color? leadingIconColor;
  final Color? backgroundColor;
  final String? subtitleIcon;
  final double? fontSize;
  final VoidCallback? onTap;
  final List<Widget>? startAction;
  final List<Widget>? endAction;
  final Widget? leading;
  final Widget? trailing;
  final Border? border;
  final Widget? detail;
  final int? maxLine;
  final double? leadingIconSize;
  final EdgeInsetsGeometry? margin;
  @override
  Widget build(BuildContext context) {
    return Slidable(
      startActionPane: startAction == null || startAction!.isEmpty
          ? null
          : ActionPane(
              extentRatio: 0.25 * startAction!.length,
              dragDismissible: false,
              motion: const ScrollMotion(),
              children: startAction ?? [],
            ),
      endActionPane: endAction == null || endAction!.isEmpty
          ? null
          : ActionPane(
              extentRatio: 0.28 * endAction!.length,
              motion: const ScrollMotion(),
              children: endAction ?? [],
            ),
      child: Container(
        width: double.maxFinite,
        margin: margin,
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColor.whiteColor,
          borderRadius: BorderRadius.circular(10.r),
          border: border,
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: 7.h,
          ),
          child: ListTile(
            titleAlignment: detail != null
                ? ListTileTitleAlignment.top
                : ListTileTitleAlignment.center,
            isThreeLine: detail != null,
            dense: leading == null,
            contentPadding: leading == null
                ? null
                : const EdgeInsets.symmetric(
                    horizontal: 9,
                  ),
            onTap: onTap,
            leading: leading != null
                ? leading!
                : leadingIcon == null
                    ? null
                    : SvgPicture.asset(
                        leadingIcon ?? "",
                        width: leadingIconSize ?? 50,
                        height: leadingIconSize ?? 50,
                        colorFilter: leadingIconColor == null
                            ? null
                            : ColorFilter.mode(
                                leadingIconColor ?? AppColor.primaryColor,
                                BlendMode.srcIn,
                              ),
                      ),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (image != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 10.0),
                    child: CustomCachedNetworkImage(
                      imageUrl: image ?? "",
                    ),
                  ),
                Text(
                  title ?? '',
                  style: TextStyle(
                    fontSize: fontSize ?? 16.sp,
                    color: AppColor.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            subtitle:
                (subtitle != null || detail != null || subtitleWidget != null
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (subtitleWidget != null) subtitleWidget!,
                          if (subtitle != null)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              verticalDirection: VerticalDirection.up,
                              children: [
                                if (subtitleIcon != null)
                                  Row(children: [
                                    SvgPicture.asset(subtitleIcon ?? ""),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                  ]),
                                if (subtitle != null)
                                  CustomText(
                                    text: subtitle ?? '',
                                    fontSize: (fontSize ?? 16.sp) - 2,
                                    maxLine: 5,
                                    fontWeight: FontWeight.bold,
                                  )
                              ],
                            ),
                          if (detail != null) detail!,
                        ],
                      )
                    : null),
            trailing: trailing,
          ),
        ),
      ),
    );
  }
}
