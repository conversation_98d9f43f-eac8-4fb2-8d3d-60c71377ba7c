import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../utils/app_assets.dart';
import 'custom_error_widget.dart';

class NoInternetErrorWidget extends StatelessWidget {
  const NoInternetErrorWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CustomErrorWidget(
      icon: AppAssets.noConnection,
      message: "No internet connection".tr,
    );
  }
}
