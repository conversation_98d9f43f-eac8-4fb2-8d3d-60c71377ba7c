import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CustomErrorWidget extends StatelessWidget {
  const CustomErrorWidget({
    super.key,
    required this.message,
    this.icon,
    this.iconColor,
    this.iconSize,
  });
  final String message;
  final String? icon;
  final Color? iconColor;
  final double? iconSize;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (icon != null) ...[
          SvgPicture.asset(
            icon!,
            width: iconSize ?? 130,
            height: iconSize ?? 130,
            colorFilter: iconColor != null
                ? ColorFilter.mode(iconColor!, BlendMode.srcIn)
                : null,
          ),
          const SizedBox(
            height: 24,
          )
        ],
        Text(
          message,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
