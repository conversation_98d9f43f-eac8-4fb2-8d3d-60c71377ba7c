import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../utils/app_assets.dart';
import 'custom_error_widget.dart';

class EmptyWidget extends StatelessWidget {
  const EmptyWidget({
    super.key,
    this.message,
    this.icon,
    this.iconColor,
    this.iconSize,
  });
  final String? message;
  final String? icon;
  final Color? iconColor;
  final double? iconSize;
  @override
  Widget build(BuildContext context) {
    return CustomErrorWidget(
      icon: icon ?? AppAssets.noContent,
      iconColor: iconColor,
      message: message ?? 'No data availabe'.tr,
      iconSize: iconSize,
    );
  }
}
