import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../utils/app_assets.dart';
import 'custom_error_widget.dart';

class RetryErrorWidget extends StatelessWidget {
  const RetryErrorWidget({
    super.key,
    this.message,
    this.icon,
    this.iconColor,
    this.iconSize,
    required this.onRetry,
  });
  
  final String? message;
  final String? icon;
  final Color? iconColor;
  final double? iconSize;
  final VoidCallback onRetry;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomErrorWidget(
          icon: icon ?? AppAssets.loading,
          iconColor: iconColor,
          message: message ?? 'Something went wrong'.tr,
          iconSize: iconSize,
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: onRetry,
          child: Text('Retry'.tr),
        ),
      ],
    );
  }
}
