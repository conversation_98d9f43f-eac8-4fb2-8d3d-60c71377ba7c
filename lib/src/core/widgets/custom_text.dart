import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomText extends StatelessWidget {
  final String text;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final int? maxLine;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? height;
  final TextAlign? textAlign;
  final bool? softWrap;
  const CustomText({
    super.key,
    required this.text,
    this.fontSize,
    this.fontWeight,
    this.color,
    this.maxLine = 1,
    this.maxLines,
    this.overflow,
    this.height,
    this.textAlign = TextAlign.center,
    this.softWrap,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      maxLines: maxLines ?? maxLine,
      text.tr,
      textAlign: textAlign,
      softWrap: softWrap,
      overflow: overflow,
      style: TextStyle(
        overflow: overflow,
        height: height,
        fontWeight: fontWeight,
        fontSize: fontSize,
        color: color,
      ),
    );
  }
}
