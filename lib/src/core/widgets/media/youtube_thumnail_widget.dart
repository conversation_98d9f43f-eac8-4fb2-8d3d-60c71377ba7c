import 'package:flutter/material.dart';
import '../../utils/youtube_util.dart';

class YoutubeThumbnailWidget extends StatelessWidget {
  const YoutubeThumbnailWidget({
    super.key,
    required this.youtubeId,
    this.borderRadius,
  });
  final String youtubeId;
  final BorderRadiusGeometry? borderRadius;
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String?>(
        future: YoutubeUtils.getYoutubeThumbnailUrl(youtubeId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: const CircularProgressIndicator());
          }
          var url = snapshot.data;
          return ClipRRect(
            borderRadius: borderRadius ?? BorderRadius.circular(12),
            child: Image.network(
              url ?? '',
              fit: BoxFit.cover,
            ),
          );
        });
  }
}
