import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:get/get.dart';
import '../../data/models/daily_post_media.dart';
import '../../utils/helper_function.dart';
import '../custom_cached_network_image.dart';
import 'video_player_component.dart';
import 'youtube_video_player_component.dart';

class MediaPreview extends StatelessWidget {
  const MediaPreview({
    super.key,
    required this.type,
    this.url,
    this.youtubeId,
  });
  final MediaType type;
  final String? url;
  final String? youtubeId;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 100,
        leading: TextButton.icon(
          label: Text('Close'.tr),
          onPressed: () {
            Get.back();
          },
          icon: const Icon(Icons.close),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share_outlined),
            onPressed: () {
              switch (type) {
                case MediaType.image:
                  HelperFunction.shareImageFormCacheNetwork(url ?? "");
                  break;
                case MediaType.video:
                  HelperFunction.shareUrl(url ?? "");
                  break;
                case MediaType.youtube:
                  HelperFunction.shareUrl(
                    "https://www.youtube.com/watch?v=$youtubeId",
                  );
                  break;
                case MediaType.pdf:
                  HelperFunction.shareUrl(url ?? "");
                  break;
              }
            },
          ),
        ],
      ),
      backgroundColor: Colors.white,
      body: Center(
        child: SizedBox(
          width: double.maxFinite,
          child: _buildMediaContent(),
        ),
      ),
    );
  }

  Widget _buildMediaContent() {
    switch (type) {
      case MediaType.image:
        return CustomCachedNetworkImage(
          imageUrl: url ?? "",
          fit: BoxFit.fitWidth,
          borderRadius: BorderRadius.zero,
        );
      case MediaType.video:
        return VideoPlayerComponent(
          url: url ?? "",
          autoPlay: true,
          control: true,
        );
      case MediaType.youtube:
        return YoutubeVideoPlayerComponent(
          youtubeId: youtubeId ?? "",
        );
      case MediaType.pdf:
        return const PDF().cachedFromUrl(
          url ?? "",
          placeholder: (progress) => Center(
            child: CircularProgressIndicator(
              value: progress,
            ),
          ),
          errorWidget: (error) => Center(
            child: Text("Error loading PDF: $error"),
          ),
        );
    }
  }
}
