import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class YoutubeVideoPlayerComponent extends StatefulWidget {
  const YoutubeVideoPlayerComponent({
    super.key,
    required this.youtubeId,
  });
  final String youtubeId;

  @override
  State<YoutubeVideoPlayerComponent> createState() =>
      _YoutubeVideoPlayerComponentState();
}

class _YoutubeVideoPlayerComponentState
    extends State<YoutubeVideoPlayerComponent> {
  late YoutubePlayerController controller;

  @override
  void initState() {
    controller = YoutubePlayerController(
      initialVideoId: widget.youtubeId,
      flags: YoutubePlayerFlags(
        autoPlay: true,
        mute: true,
      ),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayerBuilder(
      player: YoutubePlayer(
        controller: controller,
      ),
      builder: (context, player) {
        return Center(
          child: player,
        );
      },
    );
  }
}
