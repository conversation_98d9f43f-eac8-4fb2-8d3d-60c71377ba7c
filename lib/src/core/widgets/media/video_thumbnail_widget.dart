import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get_thumbnail_video/index.dart';
import 'package:get_thumbnail_video/video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:video_thumbnail/video_thumbnail.dart';

class VideoThumbnailWidget extends StatefulWidget {
  const VideoThumbnailWidget({
    super.key,
    required this.url,
  });
  final String url;

  @override
  State<VideoThumbnailWidget> createState() => _VideoThumbnailWidgetState();
}

class _VideoThumbnailWidgetState extends State<VideoThumbnailWidget> {
  bool loading = true;
  String thumbFile = "";
  Future<void> createThumbnail() async {
    // get file name from url
    var filename = widget.url.split('/').last;
    var ext = filename.split('.').last;
    filename = filename.replaceAll(ext, "png");
    thumbFile = "${(await getApplicationDocumentsDirectory()).path}/$filename";
    if (File(thumbFile).existsSync()) {
      setState(() {
        thumbFile = thumbFile;
        loading = false;
      });
      return;
    }
    await VideoThumbnail.thumbnailFile(
      video: widget.url,
      imageFormat: ImageFormat.PNG,
      timeMs: 3000,
      thumbnailPath: (await getApplicationDocumentsDirectory()).path,
      maxHeight:
          120, // specify the height of the thumbnail, let the width auto-scaled to keep the source aspect ratio
      quality: 75,
    ).then((value) {
      setState(() {
        thumbFile = value.path;
        loading = false;
      });
    });
  }

  @override
  void initState() {
    createThumbnail();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (loading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return SizedBox(
      height: double.maxFinite,
      width: double.maxFinite,
      child: Image.file(
        File(thumbFile),
        fit: BoxFit.cover,
      ),
    );
  }
}
