import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerComponent extends StatefulWidget {
  const VideoPlayerComponent({
    super.key,
    this.autoPlay = false,
    this.control = false,
    required this.url,
  });

  final bool autoPlay;
  final bool control;
  final String url;
  @override
  State<VideoPlayerComponent> createState() => _VideoPlayerComponentState();
}

class _VideoPlayerComponentState extends State<VideoPlayerComponent> {
  late VideoPlayerController controller;
  @override
  void initState() {
    controller = VideoPlayerController.networkUrl(
      Uri.parse(widget.url),
    )..initialize().then((_) {
        if (widget.autoPlay) {
          controller.play();
        }
        controller.addListener(() {
          setState(() {});
        });
        // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
        setState(() {});
      });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var videoPlayer = VideoPlayer(controller);
    if (controller.value.isInitialized) {
      return (!widget.control)
          ? videoPlayer
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AspectRatio(
                        aspectRatio: controller.value.aspectRatio,
                        child: videoPlayer),
                    if (widget.control)
                      Directionality(
                        textDirection: TextDirection.ltr,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            InkWell(
                              onTap: () {
                                setState(() {
                                  if (controller.value.isPlaying) {
                                    controller.pause();
                                  } else {
                                    controller.play();
                                  }
                                });
                              },
                              child: Icon(
                                controller.value.isPlaying
                                    ? Icons.pause_rounded
                                    : Icons.play_arrow_rounded,
                                size: 35,
                                color: Get.theme.primaryColor,
                              ),
                            ),
                            Expanded(
                              child: Slider(
                                value: controller.value.position.inSeconds
                                    .toDouble(),
                                min: 0,
                                max: controller.value.duration.inSeconds
                                    .toDouble(),
                                onChanged: (double value) {
                                  controller
                                      .seekTo(Duration(seconds: value.toInt()));
                                },
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                setState(() {
                                  if (controller.value.volume != 0) {
                                    controller.setVolume(0);
                                  } else {
                                    controller.setVolume(1);
                                  }
                                });
                              },
                              child: Icon(
                                controller.value.volume == 0
                                    ? Icons.volume_off_rounded
                                    : Icons.volume_up_rounded,
                                size: 35,
                                color: Get.theme.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      )
                  ],
                ),
              ),
            );
    } else {
      return Center(
        child: CircularProgressIndicator(
          color: Get.theme.primaryColor,
        ),
      );
    }
  }

  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }
}
