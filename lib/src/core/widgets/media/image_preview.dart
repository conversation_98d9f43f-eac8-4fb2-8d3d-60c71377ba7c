import 'package:flutter/material.dart';
import '../custom_cached_network_image.dart';

class ImagePreview extends StatelessWidget {
  const ImagePreview({
    super.key,
    required this.url,
  });
  final String url;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      primary: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
      ),
      backgroundColor: Colors.transparent,
      body: Center(
        child: SizedBox(
          width: double.maxFinite,
          child: CustomCachedNetworkImage(
            imageUrl: url,
            fit: BoxFit.cover,
            borderRadius: BorderRadius.zero,
          ),
        ),
      ),
    );
  }
}
