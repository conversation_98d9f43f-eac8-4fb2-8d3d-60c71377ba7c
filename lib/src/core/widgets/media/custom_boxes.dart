import 'package:flutter/material.dart';

import '../../utils/app_color.dart';

class CustomGreyBox extends StatelessWidget {
  final double? height;
  final double? width;
  final EdgeInsetsGeometry? padding;
  final Widget? child;
  final double? borderRadius;
  final Color? color;
  final Color? borderColor;
  final EdgeInsetsGeometry? margin;

  final double? borderWidth;

  const CustomGreyBox({
    super.key,
    this.height,
    this.width,
    this.padding = const EdgeInsets.all(4),
    this.child,
    this.borderRadius,
    this.color,
    this.borderColor,
    this.borderWidth = 0,
    this.margin = const EdgeInsets.all(0),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(borderRadius ?? 6),
        ),
        border: Border.all(
          width: borderWidth != 0 ? borderWidth! : 0.0,
          color: borderColor ?? AppColor.greyColor.withOpacity(0.2),
        ),
        color: color ?? AppColor.greyColor.withOpacity(0.2),
      ),
      child: child,
    );
  }
}

class CustomCard extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  const CustomCard({
    super.key,
    this.child,
    this.padding,
    this.elevation,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: elevation ?? 0.1,
      color: AppColor.whiteColor,
      borderOnForeground: false,
      margin: margin,
      child: Padding(
        padding: padding ?? const EdgeInsets.all(10),
        child: child,
      ),
    );
  }
}
