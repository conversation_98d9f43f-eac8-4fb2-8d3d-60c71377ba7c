import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../utils/app_color.dart';
import '../custom_text.dart';

class DateFormField extends StatelessWidget {
  final String label;
  final String hint;
  final DateTime? value;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final Function(DateTime?) onChanged;
  final String? Function(DateTime?)? validator;
  final EdgeInsetsGeometry? margin;
  final Widget? suffixIcon;

  const DateFormField({
    Key? key,
    required this.label,
    required this.hint,
    this.value,
    this.firstDate,
    this.lastDate,
    required this.onChanged,
    this.validator,
    this.margin,
    this.suffixIcon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          if (label.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(bottom: 8.h, left: 4.w),
              child: CustomText(
                text: label,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),

          // Date picker field
          InkWell(
            onTap: () => _selectDate(context),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  // Date text
                  Expanded(
                    child: CustomText(
                      text: value != null
                          ? DateFormat('yyyy-MM-dd').format(value!)
                          : hint,
                      fontSize: 14.sp,
                      color: value != null ? Colors.black : Colors.grey[500],
                    ),
                  ),

                  // Calendar icon
                  suffixIcon ??
                      Icon(
                        Icons.calendar_today,
                        size: 20.r,
                        color: AppColor.primaryColor,
                      ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime initialDate = value ?? now;
    final DateTime firstPickerDate = firstDate ?? DateTime(now.year - 5);
    final DateTime lastPickerDate = lastDate ?? DateTime(now.year + 5);

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstPickerDate,
      lastDate: lastPickerDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColor.primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      onChanged(picked);
    }
  }
}
