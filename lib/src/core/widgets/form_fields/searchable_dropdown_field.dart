import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import '../../utils/app_color.dart';

class SearchableDropdownField<T> extends StatelessWidget {
  const SearchableDropdownField({
    super.key,
    required this.items,
    this.onChanged,
    this.initialItem,
    this.listItemBuilder,
    this.hintText,
    this.noResultFoundText,
    this.searchHintText,
    this.validator,
    this.padding,
    this.margin,
    this.headerBuilder,
    required this.futureRequest,
    this.hintBuilder,
  });
  final List<T> items;
  final void Function(T? value)? onChanged;
  final T? initialItem;
  final Widget Function(BuildContext context, T item, bool isSelected,
      void Function() onItemSelect)? listItemBuilder;
  final String? hintText;
  final String? noResultFoundText;
  final String? searchHintText;
  final String? Function(T? value)? validator;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Widget Function(BuildContext context, T item, bool isSelected)?
      headerBuilder;
  final Widget Function(BuildContext context, String item, bool isSelected)?
      hintBuilder;
  final Future<List<T>> Function(String? search) futureRequest;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: margin,
      child: CustomDropdown.searchRequest(
        hintText: hintText?.tr ?? "Select".tr,
        headerBuilder: headerBuilder,
        hintBuilder: hintBuilder,
        items: items,
        onChanged: onChanged,
        initialItem: initialItem,
        searchHintText: searchHintText?.tr ?? "Search".tr,
        decoration: CustomDropdownDecoration(
          closedBorder: Border.all(width: 1, color: AppColor.primaryColor),
          closedBorderRadius: BorderRadius.circular(10),
          expandedBorder: Border.all(width: 1, color: AppColor.primaryColor),
        ),
        listItemBuilder: listItemBuilder,
        noResultFoundText: noResultFoundText?.tr ?? "No result found.".tr,
        validator: validator,
        futureRequest: futureRequest,
      ),
    );
  }
}
