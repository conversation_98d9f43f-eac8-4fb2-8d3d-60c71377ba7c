import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../features/daily_post/presentation/widgets/media_picker/media_picker_bottom_sheet.dart';
import '../../utils/app_assets.dart';
import '../../utils/app_color.dart';
import '../custom_cached_network_image.dart';
import '../custom_text.dart';

enum ImageType { file, network }

class ImageFormField extends StatefulWidget {
  final FormFieldValidator<String>? validator;
  final String? initialValue;
  final EdgeInsetsGeometry? margin;
  final String? title;
  final ImageType? imageType;
  final void Function(String?, ImageType?)? onSaved;
  final bool? enabled;

  const ImageFormField({
    super.key,
    this.onSaved,
    this.validator,
    this.initialValue,
    this.margin,
    this.title,
    this.imageType,
    this.enabled,
  });

  @override
  _ImageFormFieldState createState() => _ImageFormFieldState();
}

class _ImageFormFieldState extends State<ImageFormField> {
  late ImageType? imageType;
  @override
  void initState() {
    imageType = widget.imageType;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FormField(
        initialValue: widget.initialValue,
        validator: widget.validator,
        onSaved: (value) {
          if (widget.onSaved != null) {
            widget.onSaved!(value, imageType);
          }
        },
        builder: (state) {
          return Container(
            margin: widget.margin ?? const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                if (widget.title != null)
                  Padding(
                    padding:
                        EdgeInsetsDirectional.only(start: 8, bottom: 8.0.h),
                    child: CustomText(
                      color: AppColor.greyColor5,
                      text: widget.title ?? "",
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                GestureDetector(
                  onTap: () async {
                    if (widget.enabled == false) {
                      return;
                    }
                    var source = await MediaPickerBottomSheet
                        .showSourceTypeBottomSheet();

                    if (source != null) {
                      ImagePicker picker = ImagePicker();
                      var xfile = await picker.pickImage(
                        source: source,
                      );
                      if (xfile != null) {
                        var file = File(xfile.path);
                        state.didChange(file.path);
                        imageType = ImageType.file;
                        // onFileSelected(file, MediaType.video);
                      }
                      // Get.back();
                    }

                    // Get.bottomSheet(

                    //   // MediaPickerBottomSheet(
                    //   //   onFileSelected: (file, type) {
                    //   //     state.didChange(file.path);
                    //   //     imageType = ImageType.file;
                    //   //   },
                    //   // ),
                    // );
                  },
                  child: Container(
                    height: 190,
                    width: double.maxFinite,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.white,
                      border: Border.all(
                        color: Colors.grey.shade300,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10.r),
                      child: Builder(builder: (ctx) {
                        if (state.value == null) {
                          return Center(
                            child: SvgPicture.asset(
                              AppAssets.cameraBlueImage,
                              height: 50,
                              fit: BoxFit.contain,
                            ),
                          );
                        }

                        if (imageType == ImageType.network) {
                          return CustomCachedNetworkImage(
                            imageUrl: state.value!,
                            fit: BoxFit.cover,
                          );
                        }

                        return Image.file(
                          File(state.value!),
                          fit: BoxFit.cover,
                        );
                      }),
                    ),
                  ),
                ),
                if (state.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 5.0),
                    child: Text(
                      state.errorText ?? "",
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
              ],
            ),
          );
        });
  }
}
