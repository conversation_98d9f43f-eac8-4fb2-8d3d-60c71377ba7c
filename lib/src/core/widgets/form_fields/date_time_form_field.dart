import 'package:date_field/date_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../utils/app_color.dart';

class CustomDateTimeFormField extends StatelessWidget {
  const CustomDateTimeFormField(
      {super.key,
      required this.onChanged,
      this.hint,
      this.initialValue,
      this.maxLine,
      this.minLine,
      this.filled,
      this.filledColor,
      this.prefixIcon,
      this.suffix,
      this.controller,
      this.keyboardType,
      this.onSaved,
      this.validator,
      this.hintStyle,
      this.title,
      this.suffixIcon,
      this.suffixIconConstraints,
      this.color,
      this.margin,
      this.firstDate,
      this.lastDate,
      this.initialPickerDateTime});
  final Function(DateTime? value) onChanged;
  final String? hint;
  final DateTime? initialValue;
  final int? maxLine;
  final int? minLine;
  final bool? filled;
  final Color? filledColor;
  final Widget? prefixIcon;
  final Widget? suffix;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final Function(DateTime? value)? onSaved;
  final String? Function(DateTime? value)? validator;
  final TextStyle? hintStyle;
  final String? title;
  final Widget? suffixIcon;
  final BoxConstraints? suffixIconConstraints;
  final Color? color;
  final EdgeInsetsGeometry? margin;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final DateTime? initialPickerDateTime;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Padding(
              padding: EdgeInsetsDirectional.only(start: 8, bottom: 8.0.h),
              child: Text(
                title!,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: color ?? Colors.grey[900],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          DateTimeFormField(
            onChanged: onChanged,
            firstDate: firstDate,
            lastDate: lastDate,
            initialPickerDateTime: initialPickerDateTime,
            pickerPlatform: DateTimeFieldPickerPlatform.material,
            mode: DateTimeFieldPickerMode.date,
            dateFormat: DateFormat.yMMMMEEEEd(),
            validator: validator,
            onSaved: onSaved,
            initialValue: initialValue,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: color ?? Colors.grey[700],
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
            decoration: InputDecoration(
              prefixIcon: prefixIcon == null
                  ? null
                  : Padding(
                      padding: const EdgeInsets.all(6.0),
                      child: prefixIcon,
                    ),
              prefixIconConstraints: const BoxConstraints(
                  maxHeight: 40, maxWidth: 40, minHeight: 35, minWidth: 35),
              suffix: suffix,
              suffixIconConstraints: suffixIconConstraints ??
                  const BoxConstraints(
                      maxHeight: 40, maxWidth: 40, minHeight: 35, minWidth: 35),
              suffixIcon: suffixIcon != null
                  ? Container(
                      margin: const EdgeInsetsDirectional.only(end: 10),
                      child: suffixIcon,
                    )
                  : null,
              suffixIconColor: AppColor.amber2Color,
              isDense: true,
              hintText: hint,
              hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: color ?? AppColor.greyColor2,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
              fillColor: filledColor ?? Colors.white,
              filled: filled ?? true,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.r),
                borderSide: const BorderSide(color: AppColor.greyColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.r),
                borderSide: BorderSide(color: AppColor.primaryColor),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.r),
                borderSide: BorderSide(color: AppColor.redColor),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.r),
                borderSide: BorderSide(color: AppColor.redColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
