import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../utils/app_color.dart';
import '../custom_text.dart';

class DropdownFormField extends StatelessWidget {
  final String label;
  final String hint;
  final String? value;
  final List<DropdownMenuItem<String>> items;
  final Function(String?) onChanged;
  final String? Function(String?)? validator;
  final EdgeInsetsGeometry? margin;
  final bool isExpanded;
  final bool isDense;

  const DropdownFormField({
    Key? key,
    required this.label,
    required this.hint,
    this.value,
    required this.items,
    required this.onChanged,
    this.validator,
    this.margin,
    this.isExpanded = true,
    this.isDense = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          if (label.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(bottom: 8.h, left: 4.w),
              child: CustomText(
                text: label,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),

          // Dropdown field
          FormField<String>(
            initialValue: value,
            validator: validator,
            builder: (FormFieldState<String> state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: state.hasError
                            ? AppColor.redColor
                            : Colors.grey[300]!,
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: value,
                        hint: Text(
                          hint,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[500],
                          ),
                        ),
                        isExpanded: isExpanded,
                        isDense: isDense,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: AppColor.primaryColor,
                        ),
                        items: items,
                        onChanged: (newValue) {
                          onChanged(newValue);
                          state.didChange(newValue);
                        },
                      ),
                    ),
                  ),
                  
                  // Error message
                  if (state.hasError)
                    Padding(
                      padding: EdgeInsets.only(top: 8.h, left: 4.w),
                      child: Text(
                        state.errorText!,
                        style: TextStyle(
                          color: AppColor.redColor,
                          fontSize: 12.sp,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
