import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../utils/app_color.dart';

class CustomDropdownField<T> extends StatelessWidget {
  const CustomDropdownField(
      {super.key,
      required this.items,
      this.onChanged,
      this.initialItem,
      this.listItemBuilder,
      this.hintText,
      this.noResultFoundText,
      this.validator,
      this.padding,
      this.margin,
      this.headerBuilder,
      this.title,
      this.hintBuilder,
      this.enabled});
  final List<T> items;
  final void Function(T? value)? onChanged;
  final T? initialItem;
  final Widget Function(BuildContext context, T item, bool isSelected,
      void Function() onItemSelect)? listItemBuilder;
  final String? hintText;
  final String? noResultFoundText;
  final String? Function(T? value)? validator;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Widget Function(BuildContext context, T item, bool isSelected)?
      headerBuilder;
  final Widget Function(BuildContext context, String item, bool isSelected)?
      hintBuilder;
  final String? title;
  final bool? enabled;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Padding(
              padding: EdgeInsetsDirectional.only(start: 8, bottom: 8.0.h),
              child: Text(
                title!,
                style: TextStyle(
                  color: AppColor.greyColor5,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          CustomDropdown(
            hintText: hintText?.tr ?? "Select".tr,
            headerBuilder: headerBuilder,
            items: items,
            enabled: enabled ?? true,
            onChanged: onChanged,
            hintBuilder: hintBuilder,
            initialItem: initialItem,
            decoration: CustomDropdownDecoration(
              closedBorder: Border.all(width: 1, color: AppColor.primaryColor),
              closedBorderRadius: BorderRadius.circular(10),
              expandedBorder:
                  Border.all(width: 1, color: AppColor.primaryColor),
            ),
            listItemBuilder: listItemBuilder,
            validator: validator,
          ),
        ],
      ),
    );
  }
}
