import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/app_color.dart';
import '../custom_text.dart';

class RadioFormField<T> extends FormField<T> {
  // ignore: use_super_parameters
  RadioFormField({
    super.key,
    required List<T> options,
    FormFieldSetter<T>? onSaved,
    FormFieldValidator<T>? validator,
    T? initialValue,
    EdgeInsetsGeometry? margin,
    final String? title,
    Widget Function(T)? optionTitleBuilder,
    Widget Function(T)? optionSubtitleBuilder,
    bool? enabled,
  }) : super(
          initialValue: initialValue,
          onSaved: onSaved,
          validator: validator,
          builder: (FormFieldState<T> state) {
            return Container(
              margin: margin ?? const EdgeInsets.only(bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title != null)
                    Padding(
                      padding:
                          EdgeInsetsDirectional.only(start: 8, bottom: 8.0.h),
                      child: CustomText(
                        color: AppColor.greyColor5,
                        text: title,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: AppColor.whiteColor,
                    ),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: options.map((option) {
                          return RadioListTile<T>(
                            value: option,
                            fillColor:
                                WidgetStateProperty.all(AppColor.primaryColor),
                            contentPadding: EdgeInsets.zero,
                            subtitle: optionSubtitleBuilder != null
                                ? optionSubtitleBuilder(option)
                                : null,
                            title: optionTitleBuilder != null
                                ? optionTitleBuilder(option)
                                : CustomText(
                                    color: AppColor.greyColor5,
                                    text: option.toString(),
                                    fontSize: 14.sp,
                                    textAlign: TextAlign.start,
                                    fontWeight: FontWeight.bold,
                                  ),
                            groupValue: state.value,
                            onChanged: (value) {
                              if (enabled == false) {
                                return;
                              }
                              state.didChange(value);
                            },
                          );
                        }).toList()),
                  ),
                  if (state.hasError)
                    Padding(
                      padding: const EdgeInsets.only(left: 16.0, top: 4.0),
                      child: Text(
                        state.errorText ?? '',
                        style: TextStyle(color: AppColor.redColor),
                      ),
                    ),
                ],
              ),
            );
          },
        );
}
