import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../utils/app_color.dart';
import '../custom_text.dart';

class CustomTextField extends StatelessWidget {
  final String? hint;
  final String? initialValue;
  final int? maxLine;
  final int? minLine;
  final bool? filled;
  final Color? filledColor;
  final Widget? prefixIcon;
  final Widget? suffix;
  final bool readOnly;
  final Function()? onSuffixTap;
  final void Function()? onTap;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final Function(String value)? onChanged;
  final Function(String? value)? onSaved;
  final String? Function(String? value)? validator;
  final bool obscureText;
  final TextStyle? hintStyle;
  final String? title;
  final Widget? suffixIcon;
  final BoxConstraints? suffixIconConstraints;
  final Color? color;
  final EdgeInsetsGeometry? margin;
  final bool transparentBorder;
  final bool? enabled;

  const CustomTextField({
    super.key,
    this.hint,
    this.readOnly = false,
    this.prefixIcon,
    this.filledColor,
    this.minLine,
    this.onChanged,
    this.maxLine = 1,
    this.filled,
    this.keyboardType,
    this.controller,
    this.onSuffixTap,
    this.onTap,
    this.validator,
    this.suffix,
    this.obscureText = false,
    this.hintStyle,
    this.title,
    this.suffixIcon,
    this.suffixIconConstraints,
    this.color,
    this.margin,
    this.onSaved,
    this.initialValue,
    this.transparentBorder = false,
    this.enabled,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Padding(
              padding: EdgeInsetsDirectional.only(start: 8, bottom: 8.0.h),
              child: CustomText(
                color: color ?? AppColor.greyColor5,
                text: title!,
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          TextFormField(
            enabled: enabled,
            onTap: onTap,
            initialValue: initialValue,
            onChanged: onChanged,
            onSaved: onSaved,
            controller: controller,
            keyboardType: keyboardType,
            maxLines: maxLine,
            minLines: minLine,
            readOnly: readOnly,
            validator: validator,
            obscureText: obscureText,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: color ?? Colors.grey[700],
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
            decoration: InputDecoration(
              prefixIcon: prefixIcon == null
                  ? null
                  : Padding(
                      padding: const EdgeInsets.all(6.0),
                      child: prefixIcon,
                    ),
              prefixIconConstraints: const BoxConstraints(
                  maxHeight: 40, maxWidth: 40, minHeight: 35, minWidth: 35),
              suffix: suffix,
              suffixIconConstraints: suffixIconConstraints ??
                  const BoxConstraints(
                      maxHeight: 40, maxWidth: 40, minHeight: 35, minWidth: 35),
              suffixIcon: suffixIcon != null
                  ? Container(
                      margin: const EdgeInsetsDirectional.only(end: 10),
                      child: suffixIcon,
                    )
                  : null,
              suffixIconColor: AppColor.amber2Color,
              isDense: true,
              hintText: hint,
              hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: color ?? AppColor.greyColor2,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
              fillColor: filledColor ?? Colors.white,
              filled: filled ?? true,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.r),
                borderSide: BorderSide(
                  color: transparentBorder
                      ? Colors.transparent
                      : AppColor.greyColor,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.r),
                borderSide: BorderSide(
                    color: transparentBorder
                        ? Colors.transparent
                        : AppColor.primaryColor),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.r),
                borderSide: BorderSide(color: AppColor.redColor),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.r),
                borderSide: BorderSide(color: AppColor.redColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
