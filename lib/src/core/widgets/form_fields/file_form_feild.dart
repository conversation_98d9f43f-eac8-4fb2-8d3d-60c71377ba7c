import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../data/models/comment.dart';

import '../../../features/daily_post/presentation/widgets/media_picker/media_picker_bottom_sheet.dart';
import '../../utils/app_assets.dart';
import '../../utils/app_color.dart';
import '../../utils/app_consts.dart';
import '../../utils/helper_function.dart';
import '../custom_cached_network_image.dart';
import '../custom_text.dart';

enum FileType { file, network }

class FileFormField extends StatefulWidget {
  final FormFieldValidator<String>? validator;
  final String? initialValue;
  final EdgeInsetsGeometry? margin;
  final String? title;
  final FileType? fileType;
  final void Function(String?, FileType?)? onSaved;

  const FileFormField({
    super.key,
    this.onSaved,
    this.validator,
    this.initialValue,
    this.margin,
    this.title,
    this.fileType,
  });

  @override
  _FileFormFieldState createState() => _FileFormFieldState();
}

class _FileFormFieldState extends State<FileFormField> {
  late FileType? fileType;
  @override
  void initState() {
    fileType = widget.fileType;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FormField(
        initialValue: widget.initialValue,
        validator: widget.validator,
        onSaved: (value) {
          if (widget.onSaved != null) {
            widget.onSaved!(value, fileType);
          }
        },
        builder: (state) {
          return Container(
            margin: widget.margin ?? const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                if (widget.title != null)
                  Padding(
                    padding:
                        EdgeInsetsDirectional.only(start: 8, bottom: 8.0.h),
                    child: CustomText(
                      color: AppColor.greyColor5,
                      text: widget.title ?? "",
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                if (fileType == FileType.network && state.value != null)
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(7.r),
                      color: AppColor.primaryColor.withOpacity(0.1),
                    ),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(7.r),
                      onTap: fileType == FileType.network
                          ? () {
                              HelperFunction.openUrl(
                                  imageBaseUrl + state.value!);
                            }
                          : null,
                      child: Container(
                        padding: EdgeInsets.all(10),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(
                              AppAssets.uploadFile,
                              height: 25,
                              fit: BoxFit.contain,
                            ),
                            SizedBox(
                              width: 7,
                            ),
                            Flexible(
                              child: CustomText(
                                text: state.value!.split('/').last,
                                fontSize: 14,
                                color: AppColor.greyColor5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                else
                  GestureDetector(
                    onTap: () async {
                      ImagePicker picker = ImagePicker();
                      var xfile = await picker.pickMedia();
                      if (xfile != null) {
                        var file = File(xfile.path);
                        state.didChange(file.path);
                        fileType = FileType.file;
                        // onFileSelected(file, MediaType.video);
                      }
                   
                    },
                    child: Container(
                      padding: EdgeInsets.all(10),
                      // height: 100,
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.grey.shade300,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10.r),
                        child: Builder(builder: (ctx) {
                          // if (state.value == null) {
                          return Column(
                            children: [
                              Center(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SvgPicture.asset(
                                      AppAssets.uploadFile,
                                      height: 50,
                                      fit: BoxFit.contain,
                                    ),
                                    SizedBox(height: 5),
                                    CustomText(
                                      text: "Upload file".tr,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppColor.primaryColor,
                                    ),
                                  ],
                                ),
                              ),
                              if (state.value != null)
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7.r),
                                    color:
                                        AppColor.primaryColor.withOpacity(0.1),
                                  ),
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(7.r),
                                    onTap: fileType == FileType.network
                                        ? () {}
                                        : null,
                                    child: Container(
                                      padding: EdgeInsets.all(10),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          SvgPicture.asset(
                                            AppAssets.uploadFile,
                                            height: 25,
                                            fit: BoxFit.contain,
                                          ),
                                          SizedBox(
                                            width: 7,
                                          ),
                                          Flexible(
                                            child: CustomText(
                                              text:
                                                  state.value!.split('/').last,
                                              fontSize: 14,
                                              color: AppColor.greyColor5,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                            
                            
                            
                            ],
                          );
                          // }
                        }),
                      ),
                    ),
                  ),
                if (state.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 5.0),
                    child: Text(
                      state.errorText ?? "",
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
              ],
            ),
          );
        });
  }
}
