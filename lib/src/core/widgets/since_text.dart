import 'dart:async';

import 'package:flutter/material.dart';
import '../utils/helper_function.dart';

class SinceText extends StatefulWidget {
  const SinceText(this.date, {super.key, this.style, this.textAlign});
  final DateTime date;
  final TextStyle? style;
  final TextAlign? textAlign;

  @override
  State<SinceText> createState() => _SinceTextState();
}

class _SinceTextState extends State<SinceText> {
  Timer? timer;
  @override
  void initState() {
    timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (mounted) {
        setState(() {});
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      HelperFunction.timeAgo(widget.date),
      style: widget.style,
      textAlign: widget.textAlign,
    );
  }
}
