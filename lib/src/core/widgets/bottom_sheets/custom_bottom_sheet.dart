import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class CustomBottomSheet extends StatelessWidget {
  final String? title;
  final String? titleIcon;
  final Widget body;
  final bool withExpaned;
  final Color? color;
  const CustomBottomSheet({
    super.key,
    this.title,
    required this.body,
    this.withExpaned = false,
    this.closeAction = false,
    this.titleIcon,
    this.color,
  });
  final bool closeAction;
  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: Get.size.height * 0.8,
        minHeight: 50,
      ),
      decoration: BoxDecoration(
        color: color ?? Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // title
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              width: double.maxFinite,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (titleIcon != null) ...[
                    SvgPicture.asset(
                      titleIcon!,
                      height: 35,
                      colorFilter: ColorFilter.mode(
                          Get.theme.primaryColor, BlendMode.srcIn),
                    ),
                  ],
                  if (title != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      title!,
                      style: TextStyle(
                        fontSize: 18,
                        color: Get.theme.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (closeAction) ...[
                      const Spacer(),
                      IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(
                          Icons.close,
                        ),
                      ),
                    ]
                  ],
                ],
              ),
            ),
            const Divider(
              height: 1,
              indent: 16,
              endIndent: 16,
            ),
            if (withExpaned)
              Expanded(
                child: body,
              )
            else
              body
          ],
        ),
      ),
    );
  }
}
