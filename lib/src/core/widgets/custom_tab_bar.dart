import 'package:flutter/material.dart';

/// A custom tab bar widget that combines a TabBar with a TabBarView.
///
/// This widget simplifies the creation of a tab-based interface by combining
/// the TabBar and TabBarView into a single widget.
class CustomTabBar extends StatefulWidget {
  /// The tabs to display in the tab bar.
  final List<Tab> tabs;

  /// The content to display for each tab.
  final List<Widget> children;

  /// The initial tab index to select.
  final int initialIndex;

  /// The color of the tab indicator.
  final Color? indicatorColor;

  /// The color of the selected tab label.
  final Color? labelColor;

  /// The color of the unselected tab label.
  final Color? unselectedLabelColor;

  /// The background color of the tab bar.
  final Color? tabBarColor;

  /// Creates a CustomTabBar widget.
  const CustomTabBar({
    Key? key,
    required this.tabs,
    required this.children,
    this.initialIndex = 0,
    this.indicatorColor,
    this.labelColor,
    this.unselectedLabelColor,
    this.tabBarColor,
  })  : assert(tabs.length == children.length,
            'The number of tabs must match the number of children'),
        super(key: key);

  @override
  State<CustomTabBar> createState() => _CustomTabBarState();
}

class _CustomTabBarState extends State<CustomTabBar>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.tabs.length,
      vsync: this,
      initialIndex: widget.initialIndex,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Expanded(
      child: Column(
        children: [
          // Tab Bar
          Container(
            color: widget.tabBarColor,
            child: TabBar(
              controller: _tabController,
              tabs: widget.tabs,
              indicatorColor: widget.indicatorColor ?? theme.primaryColor,
              labelColor: widget.labelColor ?? theme.primaryColor,
              unselectedLabelColor: widget.unselectedLabelColor ?? Colors.grey,
            ),
          ),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: widget.children,
            ),
          ),
        ],
      ),
    );
  }
}
