import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../utils/app_assets.dart';

class CustomCachedNetworkImage extends StatelessWidget {
  const CustomCachedNetworkImage(
      {super.key,
      required this.imageUrl,
      this.imageBuilder,
      this.fit,
      this.borderRadius,
      this.height,
      this.width,
      this.border,
      this.zoomable = false,
      this.previewable = false});
  final String imageUrl;
  final Widget Function(BuildContext, ImageProvider<Object>)? imageBuilder;
  final BoxFit? fit;
  final BorderRadiusGeometry? borderRadius;
  final double? height;
  final double? width;
  final Border? border;
  final bool zoomable;
  final bool previewable;
  Widget placeholder(context, url) {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxHeight: 48, maxWidth: 48),
      child: Stack(
        children: [
          SvgPicture.asset(
            AppAssets.greyLogo,
          ),
          Center(
            child: CircularProgressIndicator(
              color: Theme.of(context).primaryColor,
            ),
          )
        ],
      ),
    );
  }

  Widget errorWidget(context, image, error) {
    return ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 48, maxWidth: 48),
        child: SvgPicture.asset(
          AppAssets.greyLogo,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveViewer(
      panEnabled: zoomable ? true : false,
      minScale: zoomable ? 0.5 : 1,
      maxScale: zoomable ? 2.5 : 1,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(8),
          border: border,
        ),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.circular(8),
          child: InkWell(
            splashColor: Colors.transparent,
            hoverColor: Colors.transparent,
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            onTap: !previewable
                ? null
                : () {
                    Get.dialog(AlertDialog(
                        insetPadding: EdgeInsets.all(16),
                        contentPadding: EdgeInsets.all(0),
                        backgroundColor: Colors.transparent,
                        iconPadding: EdgeInsets.all(0),
                        icon: Align(
                          alignment: AlignmentDirectional.centerEnd,
                          child: IconButton(
                              onPressed: () {
                                Get.back();
                              },
                              icon: Icon(
                                Icons.close,
                                color: Colors.white,
                              )),
                        ),
                        content: CustomCachedNetworkImage(
                          imageUrl: imageUrl,
                          width: double.maxFinite,
                          fit: BoxFit.fitWidth,
                          zoomable: true,
                        )));
                  },
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              placeholder: placeholder,
              errorWidget: errorWidget,
              fit: fit,
              imageBuilder: imageBuilder,
              errorListener: (error) {},
            ),
          ),
        ),
      ),
    );
  }
}
