import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/upload_progress_controller.dart';

/// A widget that shows the progress of image compression and upload
class UploadProgressOverlay extends StatelessWidget {
  final Widget child;

  const UploadProgressOverlay({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<UploadProgressController>();

    return Stack(
      children: [
        // The main content
        child,

        // The progress overlay
        Obx(() {
          final isCompressing = controller.isCompressing.value;
          final isUploading = controller.isUploading.value;

          if (!isCompressing && !isUploading) {
            return const SizedBox.shrink();
          }

          return Container(
            color: Colors.black.withOpacity(0.5),
            child: Center(
              child: Card(
                elevation: 8,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        controller.currentOperation.value,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (isCompressing)
                        _buildProgressIndicator(
                          controller.compressionProgress.value,
                          'Compressing'.tr,
                        ),
                      if (isUploading)
                        _buildProgressIndicator(
                          controller.uploadProgress.value,
                          'Uploading'.tr,
                        ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildProgressIndicator(double progress, String label) {
    return Column(
      children: [
        Text(label),
        const SizedBox(height: 8),
        SizedBox(
          width: 200,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(Get.theme.primaryColor),
          ),
        ),
        const SizedBox(height: 4),
        Text('${(progress * 100).toInt()}%'),
        const SizedBox(height: 16),
      ],
    );
  }
}
