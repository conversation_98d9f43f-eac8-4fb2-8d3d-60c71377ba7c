import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../utils/app_color.dart';
import 'custom_text.dart';
import 'form_fields/searchable_dropdown_field.dart';
import '../../features/auth/services/auth_service.dart';
import '../data/models/section_with_subjects.dart';

class SectionWithSubjectsWidget extends StatefulWidget {
  const SectionWithSubjectsWidget({
    super.key,
    this.onSubjectChanged,
    this.onSectionChanged,
    this.showSections = true,
    this.showSubjects = true,
    this.selectedSectionId,
  });

  final void Function(int sectionId, int subjectId)? onSubjectChanged;
  final void Function(int sectionId)? onSectionChanged;
  final bool showSections;
  final bool showSubjects;
  final int? selectedSectionId;

  @override
  State<SectionWithSubjectsWidget> createState() =>
      _SectionWithSubjectsWidgetState();
}

class _SectionWithSubjectsWidgetState extends State<SectionWithSubjectsWidget> {
  List<SectionWithSubjects> sections = AuthService.instance.sectionWithSubjects;
  SectionWithSubjects? selectedSection;
  @override
  void initState() {
    selectedSection = widget.selectedSectionId == null
        ? sections.firstOrNull
        : sections.firstWhereOrNull((el) => el.id == widget.selectedSectionId);

    if (widget.onSectionChanged != null) {
      widget.onSectionChanged!(selectedSection?.id ?? 0);
    }
    if (widget.onSubjectChanged != null) {
      widget.onSubjectChanged!(
        selectedSection?.id ?? 0,
        selectedSection?.subjects[0].id ?? 0,
      );
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          if (widget.showSections)
            SearchableDropdownField<SectionWithSubjects>(
              margin: const EdgeInsets.all(8),
              hintText: "Select the grade".tr,
              initialItem: selectedSection,
              items: sections,
              headerBuilder: (context, item, isSelected) {
                return CustomText(
                  text: item.name,
                  fontSize: 14.sp,
                  color: AppColor.primaryColor,
                  fontWeight: FontWeight.bold,
                );
              },
              listItemBuilder: (context, item, isSelected, onItemSelect) {
                return CustomText(
                  text: item.name,
                  fontSize: 14.sp,
                  color: AppColor.primaryColor,
                  fontWeight: FontWeight.bold,
                );
              },
              onChanged: (value) {
                setState(() {
                  if (widget.onSectionChanged != null) {
                    widget.onSectionChanged!(value?.id ?? 0);
                  }
                  if (widget.onSubjectChanged != null) {
                    widget.onSubjectChanged!(
                      value?.id ?? 0,
                      value?.subjects[0].id ?? 0,
                    );
                  }
                  selectedSection = value;
                });
              },
              futureRequest: (String? search) {
                return Future(() {
                  if (search == null) {
                    return sections;
                  }
                  return sections
                      .where((item) => item.name.contains(search))
                      .toList();
                });
              },
            ),
          if (selectedSection != null && widget.showSubjects)
            DefaultTabController(
              key: Key(selectedSection!.id.toString()),
              length: selectedSection!.subjects.length,
              child: TabBar(
                tabAlignment: TabAlignment.center,
                padding: const EdgeInsets.all(0),
                isScrollable: true,
                onTap: (index) {
                  if (widget.onSubjectChanged != null) {
                    widget.onSubjectChanged!(
                      selectedSection?.id ?? 0,
                      selectedSection?.subjects[index].id ?? 0,
                    );
                  }
                },
                tabs: selectedSection!.subjects.map(
                  (e) {
                    return Tab(
                      text: e.name,
                    );
                  },
                ).toList(),
              ),
            ),
        ],
      ),
    );
  }
}
