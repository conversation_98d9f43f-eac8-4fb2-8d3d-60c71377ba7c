import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import '../../router/app_router.dart';
import '../../utils/app_color.dart';
import '../../utils/app_consts.dart';
import '../../utils/app_assets.dart';
import '../../utils/helper_function.dart';
import '../buttons/custom_button.dart';
import '../custom_text.dart';
import 'drawer_list_title.dart';
import 'drawer_user_card.dart';
import '../../../features/auth/services/auth_service.dart';

import '../../utils/extensions.dart';

class CustomDrawer extends StatelessWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    bool isSupervisor =
        AuthService.instance.user.value?.role == Roles.Supervisor;
    bool isTeacher = AuthService.instance.user.value?.role == Roles.Teacher;

    return Drawer(
      child: ListView(
        padding: const EdgeInsets.only(top: 0.0),
        children: [
          const DrawerUserCard(),
          isSupervisor || AuthService.instance.selectedSon.value != null
              ? DrawerListTile(
                  title: 'Teachers',
                  svgAsset: AppAssets.teacherPoining,
                  onTap: () {
                    Get.toNamed(AppRouter.teachersScreen);
                  })
              : const SizedBox(),
          AuthService.instance.user.value?.role == Roles.Teacher
              ? CustomDrawerItem(
                  onTap: () {
                    Get.toNamed(AppRouter.parentHonorBoardScreen);
                  },
                  title: 'Honor board'.tr,
                  icon: AppAssets.medal,
                )
              : const SizedBox(),
          if (isTeacher)
            CustomDrawerItem(
              title: 'My Students'.tr,
              onTap: () => Get.toNamed(
                AppRouter.myStudentsScreen,
              ),
              icon: AppAssets.students,
            ),
          // if (isTeacher || isStudentOrParent)
          CustomDrawerItem(
            title: 'News'.tr,
            onTap: () =>
                Get.toNamed(AppRouter.advertisementsScreen, arguments: "news"),
            icon: AppAssets.news,
          ),

          // if (isTeacher || isStudentOrParent)
          CustomDrawerItem(
            title: 'Advertisments'.tr,
            onTap: () => Get.toNamed(
              AppRouter.advertisementsScreen,
              arguments: "advertisement",
            ),
            icon: AppAssets.ad,
            color: AppColor.primaryColor,
          ),

          CustomDrawerItem(
            title: 'About Rawd AlJenan'.tr,
            onTap: () => Get.toNamed(AppRouter.aboutJenanScreen),
            icon: AppAssets.aboutImage,
          ),

          CustomDrawerItem(
            title: 'Share app'.tr,
            onTap: () {},
            icon: AppAssets.shareImage,
          ),

          CustomDrawerItem(
            title: 'Contact Us'.tr,
            onTap: () =>
                HelperFunction.openUrl('mailto:<EMAIL>'),
            icon: AppAssets.contactUsImage,
          ),
          CustomDrawerItem(
            title: 'Settings'.tr,
            onTap: () => Get.toNamed(AppRouter.settingsScreen),
            icon: AppAssets.settingsImage,
          ),
          CustomDrawerItem(
            title: 'Log Out'.tr,
            // onTap: () => AuthService.instance.logOut(),
            onTap: () async {
              Get.back();
              await showDialog(
                context: context,
                builder: (BuildContext context) {
                  return Dialog(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(36),
                    ),
                    child: Container(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min, // Add this line
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            icon: SmartFingersImage(
                              imageUrl: AppAssets.logOutImage,
                              height: 79,
                              width: 82,
                            ),
                          ),
                          const SizedBox(height: 20),
                          Column(
                            children: [
                              CustomText(
                                text: 'Log Out'.tr,
                                fontSize: 20.sp,
                                color: AppColor.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                              10.verticalSpace,
                              CustomText(
                                text: 'Are you sure you want to logout?'.tr,
                                fontSize: 16,
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),
                          Row(
                            children: [
                              CustomButton(
                                onTap: () {
                                  AuthService.instance.logOut();
                                },
                                text: 'Log Out'.tr,
                                fontSize: 14,
                                textColor: AppColor.whiteColor,
                                color: AppColor.redColor2,
                                borderRadius: 20,
                                width: 150,
                              ),
                              const Spacer(),
                              CustomButton(
                                onTap: () {
                                  Navigator.of(context).pop(false);
                                },
                                text: 'Cancel'.tr,
                                fontSize: 14,

                                textColor: AppColor.redColor2,
                                border: Border.all(color: AppColor.redColor2),
                                // color: AppColor.whiteColor,
                                borderRadius: 20,
                                width: 100,
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  );
                },
              );
            },
            icon: AppAssets.logOutImage,
            color: AppColor.redColor,
          ),

          // InkWell(
          //   onTap: () {
          //     // Get.toNamed(AppRouter.settingsScreen);
          //   },
          //   child: ListTile(
          //     title: Row(
          //       children: [
          //         SvgPicture.asset(AppAssets.aboutImage),
          //         SizedBox(
          //           width: 5.w,
          //         ),
          //         CustomText(
          //           text: 'About Us'.tr,
          //           color: AppColor.primaryColor,
          //         )
          //       ],
          //     ),
          //     trailing: Icon(
          //       Icons.arrow_forward_ios_rounded,
          //       color: AppColor.primaryColor,
          //       size: 20.sp,
          //     ),
          //   ),
          // ),

          // InkWell(
          //   onTap: () {
          //     localeController.changeLocal('ar');
          //   },
          //   child: ListTile(
          //     title: Row(
          //       children: [
          //         //SvgPicture.asset(AppAssets),
          //         const Icon(Icons.language_outlined),
          //         SizedBox(
          //           width: 5.w,
          //         ),
          //         CustomText(
          //           text: 'Arabic'.tr,
          //           color: AppColor.primaryColor,
          //         )
          //       ],
          //     ),
          //     trailing: Icon(
          //       Icons.arrow_forward_ios_rounded,
          //       color: AppColor.primaryColor,
          //       size: 20.sp,
          //     ),
          //   ),
          // ),
          // InkWell(
          //   onTap: () {
          //     localeController.changeLocal('en');
          //   },
          //   child: ListTile(
          //     title: Row(
          //       children: [
          //         //SvgPicture.asset(AppAssets),
          //         const Icon(Icons.language_outlined),
          //         SizedBox(
          //           width: 5.w,
          //         ),
          //         CustomText(
          //           text: 'English'.tr,
          //           color: AppColor.primaryColor,
          //         )
          //       ],
          //     ),
          //     trailing: Icon(
          //       Icons.arrow_forward_ios_rounded,
          //       color: AppColor.primaryColor,
          //       size: 20.sp,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}

class CustomDrawerItem extends StatelessWidget {
  final Function() onTap;
  final String title;
  final Color? color;
  final String icon;

  const CustomDrawerItem(
      {super.key,
      required this.onTap,
      required this.title,
      required this.icon,
      this.color});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: ListTile(
        title: Row(
          children: [
            SmartFingersImage(
              imageUrl: icon,
              color: color ?? AppColor.primaryColor,
              height: 24,
              width: 24,
            ),
            SizedBox(width: 10.w),
            CustomText(
              text: title,
              color: color ?? AppColor.primaryColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
            )
          ],
        ),
        trailing: Icon(
          Icons.arrow_forward_ios_rounded,
          color: color ?? AppColor.primaryColor,
          size: 20.sp,
        ),
      ),
    );
  }
}
