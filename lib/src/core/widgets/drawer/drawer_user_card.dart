import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../utils/app_assets.dart';
import '../../utils/app_color.dart';
import '../../utils/app_consts.dart';
import '../../utils/extensions.dart';
import '../custom_cached_network_image.dart';
import '../custom_text.dart';
import '../../../features/auth/services/auth_service.dart';

class DrawerUserCard extends StatelessWidget {
  const DrawerUserCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    String subtitle = "";
    if (AuthService.instance.user.value?.role == Roles.Teacher) {
      subtitle = AuthService.instance.user.value?.job ?? "";
    } else if (AuthService.instance.user.value?.role == Roles.Student) {
      subtitle = AuthService.instance.user.value?.studentClass ?? "";
    } else {
      subtitle = AuthService.instance.user.value?.role ?? "";
    }
    return Stack(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsetsDirectional.only(
            start: 20,
            top: 20,
            bottom: 20,
            end: 8,
          ),
          decoration: BoxDecoration(
            color: AppColor.fromHex(
                AuthService.instance.user.value?.themeColor ?? "#58A1D6"),
            image:
                DecorationImage(image: AssetImage(AppAssets.backGroundImage)),
          ),
          child: SafeArea(
            bottom: false,
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: CircleAvatar(
                    maxRadius: 40.r,
                    backgroundColor: AppColor.whiteColor,
                    child: Center(
                      child: CustomCachedNetworkImage(
                        imageUrl: AuthService.instance.user.value?.image ?? "",
                        imageBuilder: (ctx, imageProvider) => CircleAvatar(
                          maxRadius: 35.r,
                          backgroundImage: imageProvider,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 20.w),
                Expanded(
                  flex: 5,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: CustomText(
                          text: AuthService.instance.user.value?.name ?? "",
                          color: AppColor.whiteColor,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (AuthService.instance.user.value?.role ==
                          Roles.Guardian)
                        // replace with drop down
                        CustomText(
                          text: (AuthService.instance.user.value?.role)
                                  ?.upperCaseFirstChar() ??
                              "",
                          color: AppColor.whiteColor,
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        )
                      else
                        CustomText(
                          text: subtitle.upperCaseFirstChar().tr,
                          color: AppColor.whiteColor,
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
