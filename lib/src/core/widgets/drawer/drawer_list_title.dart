import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../utils/app_color.dart';
import '../../utils/extensions.dart';
import '../custom_text.dart';

class DrawerListTile extends StatelessWidget {
  const DrawerListTile({
    super.key,
    required this.title,
    required this.svgAsset,
    required this.onTap,
  });
  final String title;
  final String svgAsset;
  final VoidCallback onTap;
  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {
        Get.back();
        onTap();
      },
      title: Row(
        children: [
          SmartFingersImage(
            imageUrl: svgAsset,
            color: AppColor.primaryColor,
            height: 24,
            width: 24,
          ),
          // SvgPicture.asset(
          //   svgAsset,
          //   width: 25,
          //   height: 25,
          //   colorFilter: ColorFilter.mode(
          //     Get.theme.primaryColor,
          //     BlendMode.srcIn,
          //   ),
          // ),
          SizedBox(width: 10.w),
          CustomText(
            text: title.tr,
            color: AppColor.primaryColor,
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
          )
        ],
      ),
      trailing: Icon(
        Icons.arrow_forward_ios_rounded,
        color: AppColor.primaryColor,
        size: 20.sp,
      ),
    );
  }
}
