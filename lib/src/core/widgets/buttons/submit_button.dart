import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'custom_button.dart';

class SubmitButton extends StatefulWidget {
  final String text;
  final Color? color;
  final Future<void> Function() onSubmit;
  final GlobalKey<FormState> formKey;
  final EdgeInsetsGeometry? margin;

  const SubmitButton({
    super.key,
    required this.text,
    this.color,
    required this.onSubmit,
    required this.formKey,
    this.margin,
  });

  @override
  State<SubmitButton> createState() => _SubmitButtonState();
}

class _SubmitButtonState extends State<SubmitButton> {
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin ?? const EdgeInsets.only(top: 32),
      child: isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: widget.color ?? Get.theme.primaryColor,
              ),
            )
          : CustomButton(
              text: widget.text,
              color: widget.color ?? Get.theme.primaryColor,
              textColor: Colors.white,
              onTap: () async {
                if (widget.formKey.currentState!.validate()) {
                  widget.formKey.currentState!.save();
                  setState(() {
                    isLoading = true;
                  });
                  await widget.onSubmit();
                  setState(() {
                    isLoading = false;
                  });
                }
              }),
    );
  }
}
