import 'package:flutter/material.dart';
import '../custom_text.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final Widget? child;
  final Color? color;
  final Border? border;
  final void Function()? onTap;
  final Color? textColor;
  final double? width;
  final double? fontSize;
  final double? borderRadius;
  final double? height;
  final EdgeInsetsGeometry? padding;

  const CustomButton({
    super.key,
    required this.text,
    this.width,
    this.color,
    this.border,
    required this.onTap,
    this.textColor,
    this.fontSize,
    this.borderRadius,
    this.height,
    this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.sizeOf(context);
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: height ?? size.height * .06,
        width: width,
        padding: padding,
        decoration: BoxDecoration(
            border: border,
            color: color,
            borderRadius: BorderRadius.circular(borderRadius ?? 10)),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: child ??
                CustomText(
                  text: text,
                  fontSize: fontSize ?? 16,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                  textAlign: TextAlign.center,
                ),
          ),
        ),
      ),
    );
  }
}
