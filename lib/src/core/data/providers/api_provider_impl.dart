import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../../utils/upload_progress_controller.dart';
import '../models/responses/daily_post_response.dart';
import 'api_provider.dart';

/// Custom implementation of the ApiProvider to track upload progress
class ApiProviderImpl {
  // We don't need to use the ApiProvider directly, but we keep it for future use
  // ignore: unused_field
  final ApiProvider _apiProvider;
  final String _baseUrl =
      'https://app.rawdaljinan.com/api'; // Use the same base URL as in app_consts.dart
  final _box = GetStorage();

  ApiProviderImpl(this._apiProvider);

  /// Creates a daily update post with progress tracking
  Future<DailyPostResponse> createDailyUpdates(dynamic body) async {
    try {
      // Get the progress controller
      final progressController = Get.find<UploadProgressController>();
      progressController.startUpload();

      // Create a new Dio instance for this request
      final dio = Dio();

      // Configure the onSendProgress callback
      final response = await dio.post(
        '$_baseUrl/v1/dailyUpdatePosts/create',
        data: body,
        options: Options(
          headers: {
            'Authorization': 'Bearer ${_getToken()}',
            'Accept': 'application/json',
          },
        ),
        onSendProgress: (sent, total) {
          progressController.updateUploadProgress(sent / total);
        },
      );

      progressController.endUpload();

      // Parse the response
      return DailyPostResponse.fromJson(response.data);
    } catch (e) {
      // End upload tracking even if there's an error
      final progressController = Get.find<UploadProgressController>();
      progressController.endUpload();

      // Log the error
      Get.log('Error uploading post: $e');

      rethrow;
    }
  }

  /// Updates a daily update post with progress tracking
  Future<DailyPostResponse> updateDailyUpdates(int id, dynamic body) async {
    try {
      // Get the progress controller
      final progressController = Get.find<UploadProgressController>();
      progressController.startUpload();

      // Create a new Dio instance for this request
      final dio = Dio();

      // Configure the onSendProgress callback
      final response = await dio.post(
        '$_baseUrl/v1/dailyUpdatePosts/$id/update',
        data: body,
        options: Options(
          headers: {
            'Authorization': 'Bearer ${_getToken()}',
            'Accept': 'application/json',
          },
        ),
        onSendProgress: (sent, total) {
          progressController.updateUploadProgress(sent / total);
        },
      );

      progressController.endUpload();

      // Parse the response
      return DailyPostResponse.fromJson(response.data);
    } catch (e) {
      // End upload tracking even if there's an error
      final progressController = Get.find<UploadProgressController>();
      progressController.endUpload();

      // Log the error
      Get.log('Error updating post: $e');

      rethrow;
    }
  }

  /// Gets the authentication token from storage
  String _getToken() {
    return _box.read('token') ?? '';
  }
}
