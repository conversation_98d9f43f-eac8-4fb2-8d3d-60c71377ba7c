import 'dart:io';
import 'package:dio/dio.dart';
import '../models/activity.dart';
import '../models/chat_message.dart';
import '../models/class_schedule.dart';
import '../models/homework.dart';
import '../models/honor_board.dart';
import '../models/point_criteria.dart';
import '../models/report_type.dart';
import '../models/responses/comment_response.dart';
import '../models/responses/comments_response.dart';
import '../models/responses/daily_post_response.dart';
import '../models/responses/general_response.dart';
import '../models/responses/grouped_homework_plan.dart';
import '../models/responses/homework_response.dart';
import '../models/responses/honorboard_response.dart';
import '../models/responses/login_response.dart';
import '../models/responses/paginated_daily_update_response.dart';
import '../models/responses/paginated_student_point_response.dart';
import '../models/responses/paginated_week_plan_comment_response.dart';
import '../models/plan_file.dart';
import '../models/responses/point_criteria_response.dart';
import '../models/responses/student_attendance_response.dart';
import '../models/responses/student_point_response.dart';
import '../models/responses/study_resource_response.dart';
import '../models/responses/unread_count_response.dart';
import '../models/responses/user_notification_response.dart';
import '../models/responses/week_plan_file_response.dart';
import '../models/responses/week_plan_lesson_comment_response.dart';
import '../models/responses/week_plan_lesson_response.dart';
import '../models/responses/grouped_week_plan.dart';
import '../models/student_attendance.dart';
import '../models/student_statistics.dart';
import '../models/user.dart';
import '../models/view_models/message_vm.dart';
import '../models/week_plan_file.dart';
import '../models/week_plan_lesson.dart';
import '../models/week_plan_lesson_comment.dart';
import '../../utils/app_consts.dart';
import '../models/about_page.dart';
import '../models/advertisement.dart';
import '../../../features/auth/models/login_model.dart';
import '../models/section_with_subjects.dart';
import 'package:retrofit/retrofit.dart';
import '../models/chat.dart';
import '../models/responses/chat_response.dart';
import '../models/student_report.dart';
import '../models/teachers.dart';
import '../../../features/teacher_achievement_file/models/teacher_achievement_file_response.dart';
import '../../../features/student_dismissal/models/responses/student_dismissal_response.dart';

part 'api_provider.g.dart';

@RestApi(baseUrl: baseUrl)
abstract class ApiProvider {
  factory ApiProvider(Dio dio, {String baseUrl}) = _ApiProvider;
  // auth
  @POST('/v1/auth/login')
  Future<LoginRespose> login(@Body() LoginModel model);

  @GET('/v1/auth/logout')
  Future<GeneralRespose> logout();

  @GET('/v1/auth/profile')
  Future<User> profile();

  @POST('/v1/auth/forgetPassword')
  Future<GeneralRespose> forgetPassword(@Body() Map<String, dynamic> body);

  @POST('/v1/auth/registerDevice')
  Future<GeneralRespose> registerDevice(@Body() Map<String, dynamic> body);

  @POST('/v1/auth/checkResetPasswordCode')
  Future<GeneralRespose> checkResetPasswordCode(
    @Body() Map<String, dynamic> body,
  );
  @POST('/v1/auth/resetPassword')
  Future<GeneralRespose> resetPassword(@Body() Map<String, dynamic> body);
  @POST('/v1/auth/changePassword')
  Future<GeneralRespose> changePassword(@Body() Map<String, dynamic> body);
  // common
  @GET('/v1/sectionsWithSubjects')
  Future<List<SectionWithSubjects>> getSectionsWithSubjects();
  // about
  @GET('/v1/aboutPages')
  Future<List<AboutPage>> getAboutPages();
  @GET('/v1/advertisements')
  Future<List<Advertisement>> getAdvertisements({
    @Query('type') required String type,
  });

  // class schedule
  @GET('/v1/classSchedule')
  Future<List<ClassSchedule>> getClassSchedule();
  // homework
  @GET('/v1/homeworks')
  Future<List<Homework>> getHomeworks({
    @Query('section_id') int? sectionId,
    @Query('subject_id') int? subjectId,
  });

  @GET('/v1/homeworks')
  Future<List<GroupedHomeWork>> getHomeworksForStudent({
    @Query('section_id') int? sectionId,
    @Query('subject_id') int? subjectId,
  });

  @POST('/v1/homeworks/create')
  Future<HomeworkResponse> createHomework(@Body() Homework homework);
  @POST('/v1/homeworks/{id}/update')
  Future<HomeworkResponse> updateHomework(
    @Path('id') int id,
    @Body() Homework homework,
  );
  @POST('/v1/homeworks/{id}/delete')
  Future<GeneralRespose> deleteHomework(@Path('id') int homeworkId);
  // honor board
  @GET('/v1/honorBoards')
  Future<List<HonorBoard>> getHonorBoards(@Query('section_id') int? sectionId);

  @POST('/v1/honorBoards/create')
  Future<HonorBoardResponse> createHonorBoard(@Body() HonorBoard honorBoard);
  @POST('/v1/honorBoards/{id}/update')
  Future<HonorBoardResponse> updateHonorBoard(
    @Path('id') int id,
    @Body() HonorBoard honorBoard,
  );
  @POST('/v1/honorBoards/{id}/delete')
  Future<GeneralRespose> deleteHonorBoard(@Path('id') int honorBoardId);

  @GET('/v1/studentAttendance')
  Future<List<StudentAttendance>> getStudentAttendance({
    @Query('subject_id') int? subjectId,
    @Query('section_id') required int sectionId,
    @Query('date') String? date,
  });

  @POST('/v1/studentAttendance/attend')
  Future<StudentAttendanceResponse> studentAttend(
    @Body() StudentAttendance data,
  );

  // week plan
  @GET('/v2/weekPlans')
  Future<List<WeekPlanLesson>> getWeekPlans([
    @Query('section_id') int? sectionId,
    @Query('subject_id') int? subjectId,
  ]);

  @GET('/v2/weekPlans')
  Future<List<GroupedWeekPlan>> getWeekPlansForStudent([
    @Query('section_id') int? sectionId,
    @Query('subject_id') int? subjectId,
  ]);

  @POST('/v2/weekPlans/create')
  Future<WeekPlanLessonResponse> createWeekPlan(@Body() WeekPlanLesson body);
  @POST('/v2/weekPlans/{id}/update')
  Future<WeekPlanLessonResponse> updateWeekPlan(
    @Path('id') int id,
    @Body() WeekPlanLesson body,
  );
  @POST('/v2/weekPlans/{id}/delete')
  Future<GeneralRespose> deleteWeekPlan(@Path('id') int weekPlanId);

  @POST('/v2/weekPlans/{id}/comment')
  Future<WeekPlanLessonCommentResponse> createWeekPlanComment(
    @Path('id') int weekPlanId,
    @Body() WeekPlanLessonComment body,
  ); // body comment

  @GET('/v2/weekPlans/{id}/comments')
  Future<PaginatedWeekPlanLessonCommentResponse> getWeekPlanComments(
    @Path('id') int weekPlanId,
  );
  // Daily updates post
  @GET('/v1/dailyUpdatePosts')
  Future<PaginatedDailyPostResponse> getDailyUpdates({
    @Query('section_id') int? sectionId,
    @Query('page') int? page,
  });

  @POST('/v1/dailyUpdatePosts/create')
  Future<DailyPostResponse> createDailyUpdates(@Body() FormData body);
  @POST('/v1/dailyUpdatePosts/{id}/update')
  Future<DailyPostResponse> updateDailyUpdates(
    @Path('id') int id,
    @Body() FormData body,
  );
  @POST('/v1/dailyUpdatePosts/{id}/delete')
  Future<GeneralRespose> deleteDailyUpdates(@Path('id') int dailyPostId);

  @POST('/v1/dailyUpdatePosts/{id}/toggleLike')
  Future<GeneralRespose> toggleLike(@Path('id') int dailyPostId);
  // activity
  @GET('/v1/activities')
  Future<List<Activity>> getActivities();
  // section students
  @GET('/v1/sectionStudents')
  Future<List<User>> getSectionStudents({
    @Query('section_id') required int sectionId,
  });
  // comment
  @GET('/v1/comments')
  Future<PaginatedCommentsResponse> getComments({
    @Query('type') required String type,
    @Query('id') required int id,
    @Query('page') int? page,
  });
  @POST('/v1/comments/create')
  Future<CommentResponse> createComment(@Body() FormData body);

  @GET('/v1/chats')
  Future<List<Chat>> getChatList({@Query('role') String? role});

  @GET('/v1/chat/{id}/messages')
  Future<PaginatedChatsResponse> getConversationMessages(
    @Path('id') int id, {
    @Query('page') int? page,
  });

  @GET('/v1/chat')
  Future<Chat> getChatByReceiverIdOrConversationId({
    @Query('id') int? conversationId,
    @Query('receiver_id') int? receiverId,
  });

  @POST('/v1/chat/{id}/message')
  Future<ChatMessage> sendMessage(
    @Path('id') int conversationId,
    @Body() MessageVm message,
  );
  @GET('/v1/sons')
  Future<List<User>> getSons();

  @GET('/v1/teachers')
  Future<List<Teacher>> getTeachers({@Query('section_id') int? sectionId});

  @GET('/v1/supervisors')
  Future<List<User>> getSupervisors();

  @GET('/v1/notifications')
  Future<UserNotifacationResponse> getNotifications({
    @Query('last_id') int? lastId,
    @Query('page') int? page,
  });

  @POST('/v1/notifications/{id}/markAsRead')
  Future<GeneralRespose> markNotificationAsRead(@Path('id') int id);

  @POST('/v1/notifications/{id}/delete')
  Future<GeneralRespose> deleteNotification(@Path('id') int id);

  @POST('/v1/notifications/markAllAsRead')
  Future<GeneralRespose> markAllNotificationsAsRead();

  @POST('/v1/notifications/deleteAll')
  Future<GeneralRespose> deleteAllNotifications();

  @GET('/v1/notifications/unreadCount')
  Future<UnreadCountResponse> getUnreadNotificationCount({
    @Query('last_id') int? lastId,
  });

  @GET('/v2/studyPlan')
  Future<PlanFile> getStudyPlan({@Query('section_id') required int sectionId});
  @GET('/v2/detailedStudyPlan')
  Future<PlanFile> getDetailedStudyPlan({
    @Query('section_id') required int sectionId,
  });

  @GET('/v2/activityPlan')
  Future<PlanFile> getActivityPlan({
    @Query('section_id') required int sectionId,
  });
  // points
  @GET('/v1/points')
  Future<PaginatedStudentPointResponse> getStudentPoints({
    @Query('section_id') int? sectionId,
    @Query('student_id') int? studentId,
    @Query('page') int? page,
  });

  @POST('/v1/points/create')
  Future<StudentPointResponse> createStudentPoint(
    @Body() Map<String, dynamic> body,
  );
  @POST('/v1/points/{id}/delete')
  Future<GeneralRespose> deleteStudentPoint(@Path('id') int id);

  @POST('/v1/points/{id}/update')
  Future<StudentPointResponse> updateStudentPoint(
    @Path('id') int id,
    @Body() Map<String, dynamic> body,
  );
  // point criteria
  @GET('/v1/points/criterias')
  Future<List<PointCriteria>> getPointCriterias();

  @POST('/v1/points/criterias/create')
  Future<PointCriteriaResponse> createPointCriteria(
    @Body() Map<String, dynamic> body,
  );
  @POST('/v1/points/criterias/{id}/update')
  Future<PointCriteriaResponse> updatePointCriteria(
    @Path('id') int id,
    @Body() Map<String, dynamic> body,
  );

  @POST('/v1/points/criterias/{id}/delete')
  Future<GeneralRespose> deletePointCriteria(@Path('id') int id);

  @GET("v1/student/statistics")
  Future<StudentStatistics> getStudentStatistics({
    @Query('student_id') int? studentId,
  });

  @GET('/v1/reports/types')
  Future<List<ReportType>> getReportTypes({
    @Query('subject_id') required int subjcetId,
    @Query('student_id') required int studentId,
  });
  @POST('/v1/reports/create')
  Future<GeneralRespose> addStudentReport(@Body() FormData body);

  @GET('/v1/student-report')
  Future<List<StudentReport>> getStudentReport({
    @Query('subject_id') int? subjectId,
    @Query('student_id') int? studentId,
    @Query('date') String? date,
  });

  // Study Resources
  @GET('/v1/studyResources')
  Future<PaginatedStudyResourceResponse> getStudyResources({
    @Query('section_id') int? sectionId,
    @Query('subject_id') int? subjectId,
    @Query('page') int? page,
  });

  @GET('/v1/studyResources/{id}')
  Future<StudyResourceResponse> getStudyResource(@Path('id') int id);

  @POST('/v1/studyResources')
  Future<StudyResourceResponse> createStudyResource(@Body() FormData body);

  @POST('/v1/studyResources/update')
  Future<StudyResourceResponse> updateStudyResource(@Body() FormData body);

  @DELETE('/v1/studyResources/{id}')
  Future<GeneralRespose> deleteStudyResource(@Path('id') int id);

  // Teacher Achievement Files
  @GET('/v1/teacherAchievementFiles')
  Future<TeacherAchievementFileResponse> getTeacherAchievementFile();

  @POST('/v1/teacherAchievementFiles')
  @MultiPart()
  Future<TeacherAchievementFileUploadResponse> uploadTeacherAchievementFile(
    @Part(name: 'file_path') File file,
  );

  @DELETE('/v1/teacherAchievementFiles/{id}')
  Future<GeneralRespose> deleteTeacherAchievementFile(@Path('id') int id);

  // Week Plan Files
  // For teachers - returns a list of files for a specific section
  @GET('/v1/weekPlanFiles')
  Future<List<WeekPlanFile>> getTeacherWeekPlanFiles({
    @Query('section_id') required int sectionId,
  });

  // For students - returns a single file for the current week
  @GET('/v1/weekPlanFiles')
  Future<WeekPlanFile?> getStudentWeekPlanFile();

  @GET('/v1/weekPlanFiles/{id}')
  Future<WeekPlanFileResponse> getWeekPlanFile(@Path('id') int id);

  @POST('/v1/weekPlanFiles')
  @MultiPart()
  Future<WeekPlanFileResponse> uploadWeekPlanFile(
    @Part(name: 'section_id') int sectionId,
    @Part(name: 'week_start_date') String weekStartDate,
    @Part(name: 'week_end_date') String weekEndDate,
    @Part(name: 'file_path') File file,
  );

  @DELETE('/v1/weekPlanFiles/{id}')
  Future<GeneralRespose> deleteWeekPlanFile(@Path('id') int id);

  // Student Dismissals
  @GET('/v1/studentDismissals')
  Future<PaginatedStudentDismissalResponse> getStudentDismissals({
    @Query('status') String? status,
    @Query('date') String? date,
    @Query('page') int? page,
  });

  @GET('/v1/studentDismissals/{id}')
  Future<StudentDismissalResponse> getStudentDismissal(@Path('id') int id);

  @POST('/v1/studentDismissals')
  Future<StudentDismissalResponse> createStudentDismissal(
    @Body() Map<String, dynamic> body,
  );

  @PUT('/v1/studentDismissals/{id}')
  Future<StudentDismissalResponse> updateStudentDismissal(
    @Path('id') int id,
    @Body() Map<String, dynamic> body,
  );

  @POST('/v1/studentDismissals/{id}/cancel')
  Future<GeneralRespose> cancelStudentDismissal(@Path('id') int id);

  @POST('/v1/studentDismissals/{id}/confirmDelivery')
  Future<GeneralRespose> confirmStudentDismissalDelivery(@Path('id') int id);
}
