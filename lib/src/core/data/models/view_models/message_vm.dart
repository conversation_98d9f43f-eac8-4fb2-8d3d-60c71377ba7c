import '../../enums/message_type.dart';

class MessageVm {
  final String message;
  final MessageType type;

  MessageVm({required this.message, required this.type});

  factory MessageVm.fromJson(Map<String, dynamic> json) {
    return MessageVm(
      message: json['message'],
      type: MessageType.values.firstWhere(
          (element) => element.name == json['type'],
          orElse: () => MessageType.text),
    );
  }

  Map<String, dynamic> toJson() => {
        "message": message,
        "type": type.name,
      };
}
