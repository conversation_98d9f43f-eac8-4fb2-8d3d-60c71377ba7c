import 'parent.dart';

class Student {
  int? id;
  String? name;
  String? image;
  String? token;
  List<Parent> parents;

  Student({
    this.id,
    this.name,
    this.image,
    this.parents = const [],
    this.token,
  });

  factory Student.fromJson(Map<String, dynamic> json) => Student(
        id: json["id"],
        name: json["name"],
        image: json["image"],
        token: json["token"],
        parents: json["parents"] == null
            ? []
            : List<Parent>.from(json["parents"].map((x) => Parent.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "token": token,
      };
}
