import '../honor_board.dart';

class HonorBoardResponse {
  String? message;
  HonorBoard? data;

  HonorBoardResponse({
    this.message,
    this.data,
  });

  factory HonorBoardResponse.fromJson(Map<String, dynamic> json) =>
      HonorBoardResponse(
        message: json["message"],
        data: json["data"] == null ? null : HonorBoard.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}
