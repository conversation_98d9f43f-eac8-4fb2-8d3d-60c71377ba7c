import '../homework.dart';

class GroupedHomeWork {
  String day;
  List<Homework> homeworks;

  GroupedHomeWork({
    required this.day,
    required this.homeworks,
  });

  factory GroupedHomeWork.fromJson(Map<String, dynamic> json) =>
      GroupedHomeWork(
        day: json["day"],
        homeworks:
            List<Homework>.from(json["data"].map((x) => Homework.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "day": day,
        "data": List<dynamic>.from(homeworks.map((x) => x.toJson())),
      };
}
