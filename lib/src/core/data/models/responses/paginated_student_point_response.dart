import '../student_point.dart';

class PaginatedStudentPointResponse {
  int? currentPage;
  int? lastPage;
  int? perPage;
  int? itemsCount;
  int? pointSum;
  List<StudentPoint>? data;

  PaginatedStudentPointResponse({
    this.currentPage,
    this.lastPage,
    this.perPage,
    this.itemsCount,
    this.pointSum,
    this.data,
  });

  factory PaginatedStudentPointResponse.fromJson(Map<String, dynamic> json) =>
      PaginatedStudentPointResponse(
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        itemsCount: json["items_count"],
        pointSum: json["point_sum"] == null ? 0 : int.parse(json["point_sum"]),
        data: json["data"] == null
            ? []
            : List<StudentPoint>.from(
                json["data"]!.map((x) => StudentPoint.fromJson(x)),
              ),
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "items_count": itemsCount,
        "point_sum": pointSum,
        "daily_post": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}
