import '../daily_post.dart';

class PaginatedDailyPostResponse {
  int? currentPage;
  int? lastPage;
  int? perPage;
  int? itemsCount;
  List<DailyPost>? dailyPost;

  PaginatedDailyPostResponse({
    this.currentPage,
    this.lastPage,
    this.perPage,
    this.itemsCount,
    this.dailyPost,
  });

  factory PaginatedDailyPostResponse.fromJson(Map<String, dynamic> json) =>
      PaginatedDailyPostResponse(
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        itemsCount: json["items_count"],
        dailyPost: json["data"] == null
            ? []
            : List<DailyPost>.from(
                json["data"]!.map((x) => DailyPost.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "items_count": itemsCount,
        "daily_post": dailyPost == null
            ? []
            : List<dynamic>.from(dailyPost!.map((x) => x.toJson())),
      };
}
