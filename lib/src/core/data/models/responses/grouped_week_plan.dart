import '../week_plan_lesson.dart';

class GroupedWeekPlan {
  String day;
  String? date; // Added for v2 API
  List<WeekPlanLesson> lesson;

  GroupedWeekPlan({
    required this.day,
    this.date,
    required this.lesson,
  });

  factory GroupedWeekPlan.fromJson(Map<String, dynamic> json) =>
      GroupedWeekPlan(
        day: json["day"],
        date: json["date"], // Added for v2 API
        lesson: List<WeekPlanLesson>.from(
            json["data"].map((x) => WeekPlanLesson.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "day": day,
        "date": date,
        "data": List<dynamic>.from(lesson.map((x) => x.toJson())),
      };
}
