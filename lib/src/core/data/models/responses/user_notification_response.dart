import '../user_notification.dart';

class UserNotifacationResponse {
  int currentPage;
  int lastPage;
  int perPage;
  int itemsCount;
  List<UserNotification> notifications;

  UserNotifacationResponse({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.itemsCount,
    required this.notifications,
  });

  factory UserNotifacationResponse.fromJson(Map<String, dynamic> json) =>
      UserNotifacationResponse(
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        itemsCount: json["items_count"],
        notifications: List<UserNotification>.from(
            json["data"].map((x) => UserNotification.fromJson(x))),
      );
}
