import '../homework.dart';

class HomeworkResponse {
  String? message;
  Homework? data;

  HomeworkResponse({
    this.message,
    this.data,
  });

  factory HomeworkResponse.fromJson(Map<String, dynamic> json) =>
      HomeworkResponse(
        message: json["message"],
        data: json["data"] == null ? null : Homework.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}
