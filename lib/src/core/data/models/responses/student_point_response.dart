import '../student_point.dart';

class StudentPointResponse {
  String? message;
  StudentPoint? data;

  StudentPointResponse({
    this.message,
    this.data,
  });

  factory StudentPointResponse.fromJson(Map<String, dynamic> json) =>
      StudentPointResponse(
        message: json["message"],
        data: json["data"] == null ? null : StudentPoint.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}
