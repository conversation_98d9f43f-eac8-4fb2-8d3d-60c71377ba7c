import '../user.dart';

class LoginRespose {
  User? user;
  String? accessToken;
  String? tokenType;

  LoginRespose({
    this.user,
    this.accessToken,
    this.tokenType,
  });

  factory LoginRespose.fromJson(Map<String, dynamic> json) => LoginRespose(
        user: json["user"] == null ? null : User.from<PERSON><PERSON>(json["user"]),
        accessToken: json["access_token"],
        tokenType: json["token_type"],
      );
}
