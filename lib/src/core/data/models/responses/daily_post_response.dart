import '../daily_post.dart';

class DailyPostResponse {
  String? message;
  DailyPost? data;

  DailyPostResponse({
    this.message,
    this.data,
  });

  factory DailyPostResponse.fromJson(Map<String, dynamic> json) =>
      DailyPostResponse(
        message: json["message"],
        data: json["data"] == null ? null : DailyPost.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}
