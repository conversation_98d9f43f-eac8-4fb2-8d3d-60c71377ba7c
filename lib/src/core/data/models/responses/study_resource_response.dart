import '../../../../features/study_resources/models/study_resource.dart';

class StudyResourceResponse {
  final String message;
  final StudyResource? data;

  StudyResourceResponse({
    required this.message,
    this.data,
  });

  factory StudyResourceResponse.fromJson(Map<String, dynamic> json) {
    return StudyResourceResponse(
      message: json['message'] ?? '',
      data: json['data'] != null ? StudyResource.fromJson(json['data']) : null,
    );
  }
}

class PaginatedStudyResourceResponse {
  final List<StudyResource> data;
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;

  PaginatedStudyResourceResponse({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
  });

  factory PaginatedStudyResourceResponse.fromJson(Map<String, dynamic> json) {
    List<StudyResource> resources = [];
    Map<String, dynamic> paginationData = {};

    // Check if data exists and has the expected structure
    if (json['data'] != null) {
      if (json['data'] is List) {
        // Direct list of resources
        resources = (json['data'] as List)
            .map((item) => StudyResource.fromJson(item))
            .toList();
      } else if (json['data'] is Map && json['data']['data'] != null) {
        // Nested data structure with pagination
        resources = (json['data']['data'] as List)
            .map((item) => StudyResource.fromJson(item))
            .toList();

        // Extract pagination info from data
        paginationData = json['data'] as Map<String, dynamic>;
      }
    }

    // Determine where to get pagination info from
    final paginationSource =
        paginationData.isNotEmpty ? paginationData : (json['meta'] ?? {});

    return PaginatedStudyResourceResponse(
      data: resources,
      currentPage: paginationSource['current_page'] ?? 1,
      lastPage: paginationSource['last_page'] ?? 1,
      perPage: paginationSource['per_page'] ?? 10,
      total: paginationSource['total'] ?? 0,
    );
  }
}
