import '../week_plan_lesson.dart';

class WeekPlanLessonResponse {
  String? message;
  WeekPlanLesson? weekPlanLesson;

  WeekPlanLessonResponse({
    this.message,
    this.weekPlanLesson,
  });

  factory WeekPlanLessonResponse.fromJson(Map<String, dynamic> json) =>
      WeekPlanLessonResponse(
        message: json["message"],
        weekPlanLesson:
            json["data"] == null ? null : WeekPlanLesson.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "WeekPlanLesson": weekPlanLesson?.toJson(),
      };
}
