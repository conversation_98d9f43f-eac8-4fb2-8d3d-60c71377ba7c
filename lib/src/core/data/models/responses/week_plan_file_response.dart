import '../week_plan_file.dart';

class WeekPlanFileResponse {
  String? message;
  WeekPlanFile? data;

  WeekPlanFileResponse({
    this.message,
    this.data,
  });

  factory WeekPlanFileResponse.fromJson(dynamic json) {
    // Check if the response is a direct WeekPlanFile object (new format)
    if (json is Map<String, dynamic> &&
        json.containsKey("id") &&
        json.containsKey("file_url")) {
      return WeekPlanFileResponse(
        data: WeekPlanFile.fromJson(json),
      );
    }

    // Handle the old format with message and data properties
    final mapJson = json as Map<String, dynamic>;
    return WeekPlanFileResponse(
      message: mapJson["message"],
      data: mapJson["data"] == null
          ? null
          : WeekPlanFile.fromJson(mapJson["data"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}

class WeekPlanFilesResponse {
  String? message;
  List<WeekPlanFile>? data;

  WeekPlanFilesResponse({
    this.message,
    this.data,
  });

  factory WeekPlanFilesResponse.fromJson(dynamic json) {
    // Check if the response is a direct array (new format)
    if (json is List) {
      return WeekPlanFilesResponse(
        data: List<WeekPlanFile>.from(
          json.map((x) => WeekPlanFile.fromJson(x as Map<String, dynamic>)),
        ),
      );
    }

    // Handle the old format with message and data properties
    final mapJson = json as Map<String, dynamic>;
    return WeekPlanFilesResponse(
      message: mapJson["message"],
      data: mapJson["data"] == null
          ? []
          : mapJson["data"] is List
              ? List<WeekPlanFile>.from((mapJson["data"] as List)
                  .map((x) => WeekPlanFile.fromJson(x as Map<String, dynamic>)))
              : mapJson["data"]["data"] != null
                  ? List<WeekPlanFile>.from((mapJson["data"]["data"] as List)
                      .map((x) =>
                          WeekPlanFile.fromJson(x as Map<String, dynamic>)))
                  : [],
    );
  }

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}
