import '../chat_message.dart';

class PaginatedChatsResponse {
  int currentPage;
  int lastPage;
  int perPage;
  int itemsCount;
  List<ChatMessage> chats;

  PaginatedChatsResponse({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.itemsCount,
    required this.chats,
  });

  factory PaginatedChatsResponse.fromJson(Map<String, dynamic> json) =>
      PaginatedChatsResponse(
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        itemsCount: json["items_count"],
        chats: json["data"] == null
            ? []
            : List<ChatMessage>.from(
                json["data"]!.map(
                  (x) => ChatMessage.fromJson(x),
                ),
              ),
      );
}
