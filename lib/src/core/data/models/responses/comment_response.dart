import '../comment.dart';

class CommentResponse {
  String? message;
  Comment? data;

  CommentResponse({
    this.message,
    this.data,
  });

  factory CommentResponse.fromJson(Map<String, dynamic> json) =>
      CommentResponse(
        message: json["message"],
        data: json["data"] == null ? null : Comment.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}
