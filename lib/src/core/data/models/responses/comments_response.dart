import '../comment.dart';

class PaginatedCommentsResponse {
  int currentPage;
  int lastPage;
  int perPage;
  int itemsCount;
  List<Comment> comments;

  PaginatedCommentsResponse({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.itemsCount,
    required this.comments,
  });

  factory PaginatedCommentsResponse.fromJson(Map<String, dynamic> json) =>
      PaginatedCommentsResponse(
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        itemsCount: json["items_count"],
        comments: json["data"] == null
            ? []
            : List<Comment>.from(
                json["data"]!.map(
                  (x) => Comment.fromJson(x),
                ),
              ),
      );
}
