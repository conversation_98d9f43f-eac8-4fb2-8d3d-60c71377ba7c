import '../student_attendance.dart';

class StudentAttendanceResponse {
  String? message;
  StudentAttendance? data;

  StudentAttendanceResponse({
    this.message,
    this.data,
  });

  factory StudentAttendanceResponse.fromJson(Map<String, dynamic> json) =>
      StudentAttendanceResponse(
        message: json["message"],
        data: json["data"] == null
            ? null
            : StudentAttendance.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}
