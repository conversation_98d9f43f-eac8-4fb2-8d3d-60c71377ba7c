import '../point_criteria.dart';

class PointCriteriaResponse {
  String? message;
  PointCriteria? data;

  PointCriteriaResponse({
    this.message,
    this.data,
  });

  factory PointCriteriaResponse.fromJson(Map<String, dynamic> json) =>
      PointCriteriaResponse(
        message: json["message"],
        data:
            json["data"] == null ? null : PointCriteria.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data?.toJson(),
      };
}
