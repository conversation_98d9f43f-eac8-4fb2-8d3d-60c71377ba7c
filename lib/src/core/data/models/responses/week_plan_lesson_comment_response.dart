import '../week_plan_lesson_comment.dart';

class WeekPlanLessonCommentResponse {
  String? message;
  WeekPlanLessonComment? weekPlanLessonComment;

  WeekPlanLessonCommentResponse({
    this.message,
    this.weekPlanLessonComment,
  });

  factory WeekPlanLessonCommentResponse.fromJson(Map<String, dynamic> json) =>
      WeekPlanLessonCommentResponse(
        message: json["message"],
        weekPlanLessonComment: json["data"] == null
            ? null
            : WeekPlanLessonComment.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": weekPlanLessonComment?.toJson(),
      };
}
