import '../week_plan_lesson_comment.dart';

class PaginatedWeekPlanLessonCommentResponse {
  int? currentPage;
  int? lastPage;
  int? perPage;
  int? itemsCount;
  List<WeekPlanLessonComment>? weekPlanLessonComments;

  PaginatedWeekPlanLessonCommentResponse({
    this.currentPage,
    this.lastPage,
    this.perPage,
    this.itemsCount,
    this.weekPlanLessonComments,
  });

  factory PaginatedWeekPlanLessonCommentResponse.fromJson(
          Map<String, dynamic> json) =>
      PaginatedWeekPlanLessonCommentResponse(
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        itemsCount: json["items_count"],
        weekPlanLessonComments: json["data"] == null
            ? []
            : List<WeekPlanLessonComment>.from(
                json["data"]!.map((x) => WeekPlanLessonComment.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "items_count": itemsCount,
        "WeekPlanLessonComment": weekPlanLessonComments == null
            ? []
            : List<dynamic>.from(
                weekPlanLessonComments!.map((x) => x.toJson())),
      };
}
