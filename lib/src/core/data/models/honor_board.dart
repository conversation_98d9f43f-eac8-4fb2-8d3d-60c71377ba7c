class HonorBoard {
  int? id;
  String? teacherId;
  String? sectionId;
  String? subjectId;
  String? title;
  List<HonorBoardStudent>? students;
  List<int>? studentIds;

  HonorBoard({
    this.id,
    this.teacherId,
    this.sectionId,
    this.subjectId,
    this.title,
    this.students,
  });

  factory HonorBoard.fromJson(Map<String, dynamic> json) => HonorBoard(
        id: json["id"],
        teacherId: json["teacher_id"].toString(),
        sectionId: json["section_id"].toString(),
        subjectId: json["subject_id"].toString(),
        title: json["title"],
        students: json["students"] == null
            ? []
            : List<HonorBoardStudent>.from(
                json["students"]!.map((x) => HonorBoardStudent.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "section_id": sectionId,
        "subject_id": subjectId,
        "title": title,
        "students_ids": studentIds,
      };

  HonorBoard clone() {
    return HonorBoard(
      id: id,
      teacherId: teacherId,
      sectionId: sectionId,
      subjectId: subjectId,
      title: title,
      students:
          List.generate(students?.length ?? 0, (i) => students![i].clone()),
    );
  }
}

class HonorBoardStudent {
  int? id;
  String? name;
  String? image;

  HonorBoardStudent({
    this.id,
    this.name,
    this.image,
  });

  factory HonorBoardStudent.fromJson(Map<String, dynamic> json) =>
      HonorBoardStudent(
        id: json["id"],
        name: json["name"],
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
      };

  HonorBoardStudent clone() {
    return HonorBoardStudent(
      id: id,
      name: name,
      image: image,
    );
  }
}
