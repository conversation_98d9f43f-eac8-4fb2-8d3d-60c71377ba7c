import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import '../../utils/upload_progress_controller.dart';
import 'daily_post_media.dart';

class DailyPost {
  int? id;
  int? userId;
  String? userName;
  String? userImage;
  int? sectionId;
  String? description;
  List<DailyPostMedia>? dailyPostMedia;
  bool? liked;
  int? likesCount;
  int? commentsCount;
  String? createdAt;

  List<int>? deletedItems;

  DailyPost({
    this.id,
    this.userId,
    this.userName,
    this.userImage,
    this.sectionId,
    this.description,
    this.dailyPostMedia,
    this.liked,
    this.likesCount,
    this.commentsCount,
    this.createdAt,
  });

  factory DailyPost.fromJson(Map<String, dynamic> json) => DailyPost(
        id: json["id"],
        userId: json["user_id"],
        userName: json["user_name"],
        userImage: json["user_image"],
        sectionId: json["section_id"] == null
            ? null
            : int.parse(json["section_id"].toString()),
        description: json["description"],
        dailyPostMedia: json["media"] == null
            ? []
            : List<DailyPostMedia>.from(
                json["media"]!.map((x) => DailyPostMedia.fromJson(x))),
        liked: json["liked"],
        likesCount: json["likes_count"],
        commentsCount: json["comments_count"],
        createdAt: json['created_at'],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "user_name": userName,
        "user_image": userImage,
        "section_id": sectionId,
        "description": description,
        "media": dailyPostMedia == null
            ? []
            : List<dynamic>.from(dailyPostMedia!.map((x) => x.toJson())),
        "liked": liked,
        "likes_count": likesCount,
        "comments_count": commentsCount,
      };

  /// Compresses all image files in this post before creating FormData
  Future<void> compressImages() async {
    if (dailyPostMedia != null && dailyPostMedia!.isNotEmpty) {
      try {
        // Get the progress controller
        final progressController = Get.put(UploadProgressController());
        progressController.startCompression();

        // Count how many images need compression
        final imagesToCompress = dailyPostMedia!
            .where(
                (media) => media.file != null && media.type == MediaType.image)
            .length;

        if (imagesToCompress == 0) {
          progressController.endCompression();
          return;
        }

        int compressedCount = 0;

        // Compress each image and update progress
        for (var media in dailyPostMedia!) {
          await media.compressIfImage();
          if (media.file != null && media.type == MediaType.image) {
            compressedCount++;
            progressController
                .updateCompressionProgress(compressedCount / imagesToCompress);
          }
        }

        progressController.endCompression();
      } catch (e) {
        // Log the error
        Get.log('Error compressing images: $e');
        // Make sure to end compression even if there's an error
        final progressController = Get.find<UploadProgressController>();
        progressController.endCompression();
      }
    }
  }

  /// Creates FormData for uploading to the server
  /// Make sure to call compressImages() before calling this method
  FormData toFormData() {
    var formData = FormData.fromMap({
      "description": description,
      'section_id': sectionId,
    });

    if (dailyPostMedia != null) {
      for (int i = 0; i < dailyPostMedia!.length; i++) {
        if (dailyPostMedia![i].type == MediaType.youtube) {
          if (dailyPostMedia![i].id == null) {
            formData.fields.add(MapEntry(
                "media[$i][youtube_id]", dailyPostMedia![i].youtubeId ?? ""));
            formData.fields.add(
              MapEntry(
                "media[$i][type]",
                dailyPostMedia![i].type?.name ?? "",
              ),
            );
          }
        } else {
          var file = dailyPostMedia![i].formFile;
          if (file != null) {
            formData.files.add(MapEntry("media[$i][file]", file));
            formData.fields.add(
              MapEntry(
                "media[$i][type]",
                dailyPostMedia![i].type?.name ?? "",
              ),
            );
          }
        }
      }
    }
    if (deletedItems != null) {
      for (var item in deletedItems ?? []) {
        formData.fields.add(MapEntry("deleted_media[]", item.toString()));
      }
    }
    return formData;
  }

  DailyPost clone() {
    return DailyPost(
      id: id,
      userId: userId,
      userName: userName,
      userImage: userImage,
      sectionId: sectionId,
      description: description,
      dailyPostMedia: List.of(dailyPostMedia ?? []),
      liked: liked,
      likesCount: likesCount,
      commentsCount: commentsCount,
      createdAt: createdAt,
    );
  }
}
