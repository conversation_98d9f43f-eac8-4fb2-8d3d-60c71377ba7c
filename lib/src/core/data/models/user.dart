import 'parent.dart';

class User {
  int? id;
  String? name;
  String? email;
  String? role;
  String? image;
  String? job;
  String? sectionName;
  int? sectionId;
  int? stageId;
  String? themeColor;
  String? token;
  bool isKindergarten;
  bool canChatWithGuardian = false;
  String get studentClass => '$sectionName';
  List<Parent> parents;

  static const List<String> kindergartenKeywords = ['روضة', 'kindergarten'];

  User({
    this.id,
    this.name,
    this.email,
    this.role,
    this.image,
    this.job,
    this.sectionName,
    this.sectionId,
    this.themeColor,
    this.stageId,
    this.isKindergarten = false,
    this.canChatWithGuardian = false,
    this.token,
    this.parents = const [],
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      role: json['role'],
      image: json['image'],
      job: json['job'],
      sectionName: json['section_name'],
      sectionId: json['section_id'],
      themeColor: json['theme_color'],
      stageId: json['stage_id'],
      token: json['token'],
      isKindergarten: json['is_kindergarten'] ?? false,
      canChatWithGuardian: json['can_chat_with_guardian'] == null
          ? false
          : int.parse(json['can_chat_with_guardian'].toString()) == 1,
      parents: json['parents'] != null
          ? List<Parent>.from(json['parents'].map((x) => Parent.fromJson(x)))
          : [],
    );
  }
}
