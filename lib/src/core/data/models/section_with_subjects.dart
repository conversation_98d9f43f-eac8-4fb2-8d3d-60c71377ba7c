class SectionWithSubjects {
  int id;
  String name;
  List<Subject> subjects;

  SectionWithSubjects({
    required this.id,
    required this.name,
    required this.subjects,
  });

  factory SectionWithSubjects.fromJson(Map<String, dynamic> json) =>
      SectionWithSubjects(
        id: json["id"],
        name: json["name"],
        subjects: List<Subject>.from(
            json["subjects"].map((x) => Subject.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "subjects": List<dynamic>.from(subjects.map((x) => x.toJson())),
      };
}

class Subject {
  int id;
  String name;

  Subject({
    required this.id,
    required this.name,
  });

  factory Subject.fromJson(Map<String, dynamic> json) => Subject(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
