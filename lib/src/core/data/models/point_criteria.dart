class PointCriteria {
  int? id;
  int? teacherId;
  String? name;
  int? points;

  PointCriteria({
    this.id,
    this.teacherId,
    this.name,
    this.points,
  });

  factory PointCriteria.fromJson(Map<String, dynamic> json) => PointCriteria(
        id: json["id"],
        teacherId: json["teacher_id"],
        name: json["name"],
        points: json["points"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "teacher_id": teacherId,
        "name": name,
        "points": points,
      };
}
