class WeekPlanSection {
  int? id;
  String? name;
  String? fullName;

  WeekPlanSection({
    this.id,
    this.name,
    this.fullName,
  });

  factory WeekPlanSection.fromJson(Map<String, dynamic> json) =>
      WeekPlanSection(
        id: json["id"],
        name: json["name"],
        fullName: json["full_name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "full_name": fullName,
      };
}

class WeekPlanLessonDate {
  int? id;
  DateTime? date;
  String? formattedDate;
  bool? hasQuiz;

  WeekPlanLessonDate({
    this.id,
    this.date,
    this.formattedDate,
    this.hasQuiz,
  });

  factory WeekPlanLessonDate.fromJson(Map<String, dynamic> json) =>
      WeekPlanLessonDate(
        id: json["id"],
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        formattedDate: json["formatted_date"],
        hasQuiz: json["has_quiz"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "date": date?.toIso8601String(),
        "formatted_date": formattedDate,
        "has_quiz": hasQuiz,
      };
}

class WeekPlanLesson {
  int? id;
  int? teacherId;
  int? sectionId; // For backward compatibility
  int? subjectId;
  String? title;
  String? subjectName;
  String? detail;
  DateTime? date; // For backward compatibility
  String? formattedDate; // For backward compatibility
  List<WeekPlanSection>? sections;
  List<WeekPlanLessonDate>? lessonDates;

  WeekPlanLesson({
    this.id,
    this.teacherId,
    this.sectionId,
    this.subjectId,
    this.title,
    this.detail,
    this.date,
    this.formattedDate,
    this.subjectName,
    this.sections,
    this.lessonDates,
  });

  factory WeekPlanLesson.fromJson(Map<String, dynamic> json) => WeekPlanLesson(
        id: json["id"],
        teacherId: json["teacher_id"],
        sectionId: json["section_id"],
        subjectId: json["subject_id"],
        subjectName: json["subject_name"],
        title: json["title"],
        detail: json["detail"],
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        formattedDate: json["formatted_date"],
        sections: json["sections"] == null
            ? []
            : List<WeekPlanSection>.from(
                json["sections"].map((x) => WeekPlanSection.fromJson(x))),
        lessonDates: json["lesson_dates"] == null
            ? []
            : List<WeekPlanLessonDate>.from(json["lesson_dates"]
                .map((x) => WeekPlanLessonDate.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "teacher_id": teacherId,
        "subject_id": subjectId,
        "title": title,
        "detail": detail,
        // For v2 API, use section_ids array instead of section_id
        "section_ids":
            sections?.map((section) => section.id).toList() ?? [sectionId],
        // For v2 API, use lesson_dates array
        "lesson_dates": lessonDates
                ?.map((date) => {
                      "date": date.date?.toIso8601String(),
                      "has_quiz": date.hasQuiz,
                    })
                .toList() ??
            [
              {"date": date?.toIso8601String(), "has_quiz": false}
            ],
      };

  WeekPlanLesson clone() {
    return WeekPlanLesson(
      id: id,
      teacherId: teacherId,
      sectionId: sectionId,
      subjectId: subjectId,
      title: title,
      detail: detail,
      date: date,
      formattedDate: formattedDate,
      subjectName: subjectName,
      sections: sections
          ?.map((section) => WeekPlanSection(
                id: section.id,
                name: section.name,
                fullName: section.fullName,
              ))
          .toList(),
      lessonDates: lessonDates
          ?.map((lessonDate) => WeekPlanLessonDate(
                id: lessonDate.id,
                date: lessonDate.date,
                formattedDate: lessonDate.formattedDate,
                hasQuiz: lessonDate.hasQuiz,
              ))
          .toList(),
    );
  }

  @override
  String toString() {
    return title ?? "";
  }
}
