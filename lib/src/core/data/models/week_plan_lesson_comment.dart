class WeekPlanLessonComment {
  int? id;
  int? userId;
  String? userName;
  String? userImage;
  String? userRole;
  int? weekPlanId;
  String? comment;
  DateTime? createdAt;
  String? formattedCreatedAt;

  WeekPlanLessonComment({
    this.id,
    this.userId,
    this.userName,
    this.userImage,
    this.userRole,
    this.weekPlanId,
    this.comment,
    this.createdAt,
    this.formattedCreatedAt,
  });

  factory WeekPlanLessonComment.fromJson(Map<String, dynamic> json) =>
      WeekPlanLessonComment(
        id: json["id"],
        userId: json["user_id"],
        userName: json["user_name"],
        userImage: json["user_image"],
        userRole: json["user_role"],
        weekPlanId: json["week_plan_id"],
        comment: json["comment"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        formattedCreatedAt: json["formatted_created_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "user_name": userName,
        "user_image": userImage,
        "user_role": userRole,
        "week_plan_id": weekPlanId,
        "comment": comment,
        "created_at": createdAt?.toIso8601String(),
        "formatted_created_at": formattedCreatedAt,
      };
}
