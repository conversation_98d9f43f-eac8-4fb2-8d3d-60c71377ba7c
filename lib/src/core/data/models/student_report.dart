import 'report_type.dart';

import 'report_attribute.dart';

class StudentReport {
  int? id;
  int? studentId;
  ReportType? reportGroup;
  List<Record>? records;
  String? createdAt;

  StudentReport(
      {this.id,
      this.studentId,
      this.reportGroup,
      this.records,
      this.createdAt});

  factory StudentReport.fromJson(Map<String, dynamic> json) => StudentReport(
        id: json["id"],
        studentId: json["student_id"],
        createdAt: json["created_at"],
        reportGroup: json["report_group"] == null
            ? null
            : ReportType.fromJson(json["report_group"]),
        records: json["records"] == null
            ? []
            : List<Record>.from(json["records"].map((x) => Record.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "student_id": studentId,
        "report_group": reportGroup?.toJson(),
        "records": records == null
            ? []
            : List<dynamic>.from(records!.map((x) => x.toJson())),
      };
}

class Record {
  int? id;
  String? value;
  ReportAttribute? attribute;

  Record({
    this.id,
    this.value,
    this.attribute,
  });

  factory Record.fromJson(Map<String, dynamic> json) => Record(
        id: json["id"],
        value: json["value"],
        attribute: json["attribute"] == null
            ? null
            : ReportAttribute.fromJson(json["attribute"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "value": value,
        "attribute": attribute?.toJson(),
      };
}
