import 'dart:io';

import 'package:dio/dio.dart';
import '../../../core/utils/image_compression_util.dart';

enum MediaType { image, video, youtube, pdf }

class DailyPostMedia {
  int? id;
  File? file;
  String? fileUrl;
  String? youtubeId;
  MediaType? type;

  MultipartFile? get formFile {
    if (file == null) return null;
    return MultipartFile.fromFileSync(file!.path);
  }

  /// Compresses the image file if this media is an image type
  /// Returns a Future that completes when compression is done
  Future<void> compressIfImage() async {
    if (file != null && type == MediaType.image) {
      file = await ImageCompressionUtil.compressImage(file: file!);
    }
  }

  DailyPostMedia({
    this.id,
    this.file,
    this.fileUrl,
    this.youtubeId,
    this.type,
  });

  factory DailyPostMedia.fromJson(Map<String, dynamic> json) => DailyPostMedia(
        id: json["id"],
        fileUrl: json["file_url"],
        youtubeId: json["youtube_id"],
        type: json["type"] == "image"
            ? MediaType.image
            : json["type"] == "video"
                ? MediaType.video
                : json["type"] == "pdf"
                    ? MediaType.pdf
                    : MediaType.youtube,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "file": file,
        "file_url": fileUrl,
        "youtube_id": youtubeId,
        "type": type?.toString(),
      };

  MultipartFile toFormFile() {
    return MultipartFile.fromFileSync(
      file!.path,
      filename: file!.path.split('/').last,
    );
  }
}
