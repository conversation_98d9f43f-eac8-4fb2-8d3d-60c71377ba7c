class Son {
  Son({
    required this.id,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.gender,
    required this.role,
    required this.job,
    required this.image,
    required this.sectionName,
    required this.themeColor,
    required this.token,
    required this.isKindergarten,
  });

  final int id;
  static const String idKey = "id";

  final String name;
  static const String nameKey = "name";

  final String email;
  static const String emailKey = "email";

  final dynamic phoneNumber;
  static const String phoneNumberKey = "phone_number";

  final int gender;
  static const String genderKey = "gender";

  final String role;
  static const String roleKey = "role";

  final String job;
  static const String jobKey = "job";

  final String image;
  static const String imageKey = "image";

  final String sectionName;
  static const String sectionNameKey = "section_name";

  final String themeColor;
  static const String themeColorKey = "theme_color";

  final bool isKindergarten;

  final String token;
  Son copyWith({
    int? id,
    String? name,
    String? email,
    int? phoneNumber,
    int? gender,
    String? role,
    String? job,
    String? image,
    String? sectionName,
    String? themeColor,
    String? token,
    bool? isKindergarten,
  }) {
    return Son(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      gender: gender ?? this.gender,
      role: role ?? this.role,
      job: job ?? this.job,
      image: image ?? this.image,
      sectionName: sectionName ?? this.sectionName,
      themeColor: themeColor ?? this.themeColor,
      token: token ?? this.token,
      isKindergarten: isKindergarten ?? this.isKindergarten,
    );
  }

  factory Son.fromJson(Map<String, dynamic> json) {
    return Son(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      email: json["email"] ?? "",
      phoneNumber: json["phone_number"] ?? 0,
      gender: json["gender"] ?? 0,
      role: json["role"] ?? "",
      job: json["job"] ?? "",
      image: json["image"] ?? "",
      sectionName: json["section_name"] ?? "",
      themeColor: json["theme_color"] ?? "",
      token: json["token"] ?? "",
      isKindergarten: json["is_kindergarten"] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "phone_number": phoneNumber,
        "gender": gender,
        "role": role,
        "job": job,
        "image": image,
        "section_name": sectionName,
        "theme_color": themeColor,
        "is_kindergarten": isKindergarten,
      };

  @override
  String toString() {
    return "$id, $name, $email, $phoneNumber, $gender, $role, $job, $image, $sectionName, $themeColor, $isKindergarten ";
  }
}
