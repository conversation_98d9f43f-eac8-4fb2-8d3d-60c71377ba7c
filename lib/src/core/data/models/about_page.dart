class AboutPage {
  int? id;
  String? title;
  String? detail;
  List<String>? images;
  List<String>? videos;

  AboutPage({
    this.id,
    this.title,
    this.detail,
    this.images,
    this.videos,
  });

  factory AboutPage.fromJson(Map<String, dynamic> json) => AboutPage(
        id: json["id"],
        title: json["title"],
        detail: json["detail"],
        images: json["images"] == null
            ? []
            : List<String>.from(json["images"]!.map((x) => x)),
        videos: json["videos"] == null
            ? []
            : List<String>.from(json["videos"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "detail": detail,
        "images":
            images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
        "videos":
            videos == null ? [] : List<dynamic>.from(videos!.map((x) => x)),
      };
}
