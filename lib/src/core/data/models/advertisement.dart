class Advertisement {
  int? id;
  String? title;
  String? image;
  List<String>? images;
  List<String>? videos;
  String? description;
  String? type;

  String? date;

  Advertisement({
    this.id,
    this.title,
    this.image,
    this.images,
    this.videos,
    this.description,
    this.type,
    this.date,
  });

  factory Advertisement.fromJson(Map<String, dynamic> json) => Advertisement(
        id: json["id"],
        title: json["title"],
        image: json["image"],
        images: json["images"] == null
            ? []
            : List<String>.from(json["images"]!.map((x) => x)),
        videos: json["videos"] == null
            ? []
            : List<String>.from(json["videos"]!.map((x) => x)),
        description: json["description"],
        type: json["type"],
        date: json["date"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "image": image,
        "images":
            images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
        "videos":
            videos == null ? [] : List<dynamic>.from(videos!.map((x) => x)),
        "description": description,
        "type": type,
        "date": date,
      };
}
