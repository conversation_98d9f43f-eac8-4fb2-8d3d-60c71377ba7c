import 'homework.dart';
import 'report_attribute_option.dart';
import 'week_plan_lesson.dart';

class ReportAttribute {
  int? id;
  String? name;
  String? label;
  bool? hasPoints;
  String? type;
  bool? status;
  List<ReportAttributeOption>? options;
  List<WeekPlanLesson>? weekPlans;
  List<Homework>? homeworks;

  ReportAttribute({
    this.id,
    this.name,
    this.label,
    this.hasPoints,
    this.type,
    this.status,
    this.options,
    this.weekPlans,
    this.homeworks,
  });

  factory ReportAttribute.fromJson(Map<String, dynamic> json) =>
      ReportAttribute(
        id: json["id"],
        name: json["name"],
        label: json["label"],
        hasPoints: json["has_points"],
        type: json["type"],
        status: json["status"],
        options: json["options"] == null
            ? []
            : List<ReportAttributeOption>.from(
                json["options"]!.map((x) => ReportAttributeOption.fromJson(x))),
        weekPlans: json["weekPlans"] == null
            ? []
            : List<WeekPlanLesson>.from(
                json["weekPlans"]!.map((x) => WeekPlanLesson.fromJson(x))),
        homeworks: json["homeworks"] == null
            ? []
            : List<Homework>.from(
                json["homeworks"]!.map((x) => Homework.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "label": label,
        "has_points": hasPoints,
        "type": type,
        "status": status,
        "options": options == null
            ? []
            : List<dynamic>.from(options!.map((x) => x.toJson())),
        "weekPlans": weekPlans == null
            ? []
            : List<dynamic>.from(weekPlans!.map((x) => x.toJson())),
        "homeworks": homeworks == null
            ? []
            : List<dynamic>.from(homeworks!.map((x) => x.toJson())),
      };
}
