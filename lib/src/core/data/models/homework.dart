class Homework {
  int? id;
  int? teacherId;
  int? sectionId;
  int? subjectId;
  String? subjectName;
  String? title;
  String? description;
  DateTime? dueDate;
  String? formattedDueDate;

  Homework(
      {this.id,
      this.teacherId,
      this.sectionId,
      this.subjectId,
      this.title,
      this.description,
      this.dueDate,
      this.formattedDueDate,
      this.subjectName});

  factory Homework.fromJson(Map<String, dynamic> json) => Homework(
      id: json["id"],
      teacherId: json["teacher_id"],
      sectionId: json["section_id"],
      subjectId: json["subject_id"],
      title: json["title"],
      description: json["description"],
      dueDate:
          json["due_date"] == null ? null : DateTime.parse(json["due_date"]),
      formattedDueDate: json["formatted_due_date"],
      subjectName: json['subject_name']);

  Map<String, dynamic> toJson() => {
        "section_id": sectionId,
        "subject_id": subjectId,
        "title": title,
        "description": description,
        "due_date": dueDate?.toIso8601String(),
        'subject_name': subjectName
      };

  Homework clone() {
    return Homework(
        id: id,
        teacherId: teacherId,
        sectionId: sectionId,
        subjectId: subjectId,
        title: title,
        description: description,
        dueDate: dueDate,
        formattedDueDate: formattedDueDate,
        subjectName: subjectName);
  }

  @override
  String toString() {
    return title ?? "";
  }
}
