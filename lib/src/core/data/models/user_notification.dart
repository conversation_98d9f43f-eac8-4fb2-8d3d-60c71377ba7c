import 'package:flutter/material.dart';
import '../../utils/app_color.dart';
import '../../utils/helper_function.dart';

class UserNotification {
  int id;
  String title;
  String content;
  bool unread;
  Map<String, dynamic>? data;
  DateTime createdAt;
  String formattedCreatedAt;

  String get timeAgo => HelperFunction.timeAgo(createdAt);

  Color get statusColor =>
      !unread ? Colors.grey[700]!.withOpacity(0.1) : AppColor.whiteColor;

  UserNotification({
    required this.id,
    required this.title,
    required this.content,
    required this.unread,
    required this.data,
    required this.createdAt,
    required this.formattedCreatedAt,
  });

  factory UserNotification.fromJson(Map<String, dynamic> json) =>
      UserNotification(
        id: json["id"],
        title: json["title"],
        content: json["content"],
        unread: json["unread"] == 1 ? true : false,
        data: json["data"],
        createdAt: DateTime.parse(json["created_at"]),
        formattedCreatedAt: json["formatted_created_at"],
      );
}
