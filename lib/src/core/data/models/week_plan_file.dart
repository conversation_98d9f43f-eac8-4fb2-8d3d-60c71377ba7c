class WeekPlanFile {
  int? id;
  int? teacherId;
  String? teacherName;
  int? sectionId;
  String? sectionName;
  String? sectionFullName;
  String? fileUrl;
  DateTime? weekStartDate;
  DateTime? weekEndDate;
  String? formattedWeekStartDate;
  String? formattedWeekEndDate;
  int? semester;
  String? semesterName;
  DateTime? createdAt;
  DateTime? updatedAt;

  WeekPlanFile({
    this.id,
    this.teacherId,
    this.teacherName,
    this.sectionId,
    this.sectionName,
    this.sectionFullName,
    this.fileUrl,
    this.weekStartDate,
    this.weekEndDate,
    this.formattedWeekStartDate,
    this.formattedWeekEndDate,
    this.semester,
    this.semesterName,
    this.createdAt,
    this.updatedAt,
  });

  factory WeekPlanFile.fromJson(Map<String, dynamic> json) => WeekPlanFile(
        id: json["id"],
        teacherId: json["teacher_id"],
        teacherName: json["teacher_name"],
        sectionId: json["section_id"],
        sectionName: json["section_name"],
        sectionFullName: json["section_full_name"],
        fileUrl: json["file_url"],
        weekStartDate: json["week_start_date"] == null
            ? null
            : DateTime.parse(json["week_start_date"]),
        weekEndDate: json["week_end_date"] == null
            ? null
            : DateTime.parse(json["week_end_date"]),
        formattedWeekStartDate: json["formatted_week_start_date"],
        formattedWeekEndDate: json["formatted_week_end_date"],
        semester: json["semester"],
        semesterName: json["semester_name"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "teacher_id": teacherId,
        "teacher_name": teacherName,
        "section_id": sectionId,
        "section_name": sectionName,
        "section_full_name": sectionFullName,
        "file_url": fileUrl,
        "week_start_date": weekStartDate?.toIso8601String(),
        "week_end_date": weekEndDate?.toIso8601String(),
        "formatted_week_start_date": formattedWeekStartDate,
        "formatted_week_end_date": formattedWeekEndDate,
        "semester": semester,
        "semester_name": semesterName,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
