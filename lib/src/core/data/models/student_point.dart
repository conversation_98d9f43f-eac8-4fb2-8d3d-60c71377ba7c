class StudentPoint {
  int? id;
  int? studentId;
  String? studentName;
  int? pointCriteriaId;
  int? teacherId;
  String? teacherName;
  String? name;
  int? points;
  DateTime? createdAt;
  String? formattedCreatedAt;

  StudentPoint({
    this.id,
    this.studentId,
    this.studentName,
    this.pointCriteriaId,
    this.teacherId,
    this.teacherName,
    this.name,
    this.points,
    this.createdAt,
    this.formattedCreatedAt,
  });

  factory StudentPoint.fromJson(Map<String, dynamic> json) => StudentPoint(
        id: json["id"],
        studentId: json["student_id"],
        studentName: json["student_name"],
        pointCriteriaId: json["point_criteria_id"],
        teacherId: json["teacher_id"],
        teacherName: json["teacher_name"],
        name: json["name"],
        points: json["points"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        formattedCreatedAt: json["formatted_created_at"],
      );

  Map<String, dynamic> toJson() => {
        "student_id": studentId,
        "criteria_id": pointCriteriaId,
      };
}
