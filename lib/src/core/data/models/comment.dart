class Comment {
  int? id;
  String? commentableType;
  int? commentableId;
  int? userId;
  String? userName;
  String? userImage;
  String? userColor;
  String? userRole;
  String? comment;
  String? commentType;
  String? createdAt;

  Comment({
    this.id,
    this.commentableType,
    this.commentableId,
    this.userId,
    this.userName,
    this.userImage,
    this.userColor,
    this.userRole,
    this.comment,
    this.commentType,
    this.createdAt,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
        id: json["id"],
        commentableType: json["commentable_type"],
        commentableId: int.parse(json["commentable_id"].toString()),
        userId: json["user_id"],
        userName: json["user_name"],
        userImage: json["user_image"],
        userColor: json["user_color"],
        userRole: json["user_role"],
        comment: json["comment"],
        commentType: json["comment_type"],
        createdAt: json["created_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "commentable_type": commentableType,
        "commentable_id": commentableId,
        "user_id": userId,
        "user_name": userName,
        "user_image": userImage,
        "user_color": userColor,
        "user_role": userRole,
        "comment": comment,
        "comment_type": commentType,
        "created_at": createdAt,
      };
}
