class StudentStatistics {
  int pointsCount;
  int presentDaysCount;
  int upsentDaysCount;
  int honorBoardsCount;

  StudentStatistics({
    required this.pointsCount,
    required this.presentDaysCount,
    required this.upsentDaysCount,
    required this.honorBoardsCount,
  });

  factory StudentStatistics.fromJson(Map<String, dynamic> json) =>
      StudentStatistics(
        pointsCount: json["points_count"],
        presentDaysCount: json["present_days_count"],
        upsentDaysCount: json["upsent_days_count"],
        honorBoardsCount: json["honor_boards_count"],
      );

  Map<String, dynamic> toJson() => {
        "points_count": pointsCount,
        "present_days_count": presentDaysCount,
        "upsent_days_count": upsentDaysCount,
        "honor_boards_count": honorBoardsCount,
      };
}
