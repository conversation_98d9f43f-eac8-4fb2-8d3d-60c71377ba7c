import '../../../features/auth/services/auth_service.dart';

class ChatMessage {
  int? id;
  int? senderId;
  String? senderName;
  String? senderImageUrl;
  int? conversationId;
  String? message;
  String? type;
  bool? seen;
  DateTime createdAt;
  String? formattedCreatedAt;
  // bool _isSender = false;

  bool get isSender => AuthService.instance.user.value?.id == senderId;

  // set isSending(bool isSending) => _isSender = isSending;

  ChatMessage({
    this.id,
    this.senderId,
    this.senderName,
    this.senderImageUrl,
    this.conversationId,
    this.message,
    this.type,
    this.seen,
    required this.createdAt,
    this.formattedCreatedAt,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) => ChatMessage(
        id: json["id"],
        senderId: json["sender_id"],
        senderName: json["sender_name"],
        senderImageUrl: json["sender_image_url"],
        conversationId: _parseConversationId(json["conversation_id"]),
        message: json["message"],
        type: json["type"],
        seen: _parseSeenField(json["seen"]),
        createdAt: DateTime.parse(json["created_at"]),
        formattedCreatedAt: json["formatted_created_at"],
      );

  static bool _parseSeenField(dynamic value) {
    if (value is String) return value.toLowerCase() == 'true';
    if (value is int) return value != 0;
    if (value is bool) return value;
    throw Exception('Invalid seen value type');
  }

  static int _parseConversationId(dynamic value) {
    if (value is String) return int.parse(value);
    if (value is int) return value;
    throw Exception('Invalid conversation_id value type');
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "sender_id": senderId,
        "sender_name": senderName,
        "sender_image_url": senderImageUrl,
        "conversation_id": conversationId,
        "message": message,
        "type": type,
        "seen": seen,
        "created_at": createdAt.toIso8601String(),
        "formatted_created_at": formattedCreatedAt,
      };
}
