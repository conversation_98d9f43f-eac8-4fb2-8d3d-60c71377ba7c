import '../../../features/auth/services/auth_service.dart';

class ClassSchedule {
  String day;
  List<Lesson> lesson;

  ClassSchedule({
    required this.day,
    required this.lesson,
  });

  factory ClassSchedule.fromJson(Map<String, dynamic> json) => ClassSchedule(
        day: json["day"],
        lesson: List<Lesson>.from(json["data"].map((x) => Lesson.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "day": day,
        "data": List<dynamic>.from(lesson.map((x) => x.toJson())),
      };
}

class Lesson {
  int id;
  String day;
  String? fromTime;
  String? toTime;
  int subjectId;
  String subjectName;
  int? sectionId;
  int? stageId;
  String? sectionName;
  String? className;
  String get title {
    if (AuthService.instance.user.value?.role == "teacher") {
      return "$subjectName - $sectionName";
    } else {
      return subjectName;
    }
  }

  String get time {
    return stageId == 1 ? "$fromTime - $toTime" : (className ?? "");
  }

  Lesson({
    required this.id,
    required this.day,
    required this.fromTime,
    required this.toTime,
    required this.subjectId,
    required this.subjectName,
    required this.stageId,
    required this.sectionId,
    required this.sectionName,
    this.className,
  });

  factory Lesson.fromJson(Map<String, dynamic> json) => Lesson(
        id: json["id"],
        day: json["day"],
        fromTime: json["from_time"],
        toTime: json["to_time"],
        subjectId: json["subject_id"],
        subjectName: json["subject_name"],
        stageId: json["stage_id"],
        sectionId: json["section_id"],
        sectionName: json["section_name"],
        className: json["class"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "day": day,
        "from_time": fromTime,
        "to_time": toTime,
        "subject_id": subjectId,
        "subject_name": subjectName,
        "stage_id": stageId,
        "section_id": sectionId,
        "section_name": sectionName,
        "class": className,
      };
}
