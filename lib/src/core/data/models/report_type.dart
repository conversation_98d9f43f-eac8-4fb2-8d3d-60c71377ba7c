import 'report_attribute.dart';

class ReportType {
  int? id;
  String? name;
  String? displayText;
  String? type;
  int? stageId;
  int? gradeId;
  int? subjectId;
  String? description;
  List<ReportAttribute>? attributes;
  String? createdAt;

  ReportType(
      {this.id,
      this.name,
      this.displayText,
      this.type,
      this.stageId,
      this.gradeId,
      this.subjectId,
      this.description,
      this.attributes,
      this.createdAt});

  factory ReportType.fromJson(Map<String, dynamic> json) => ReportType(
        id: json["id"],
        name: json["name"],
        displayText: json["display_text"],
        type: json["type"],
        stageId: json["stage_id"],
        gradeId: json["grade_id"],
        subjectId: json["subject_id"],
        description: json["description"],
        createdAt: json['created_at'],
        attributes: json["attributes"] == null
            ? []
            : List<ReportAttribute>.from(
                json["attributes"]!.map((x) => ReportAttribute.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "display_text": displayText,
        "type": type,
        "stage_id": stageId,
        "grade_id": gradeId,
        "subject_id": subjectId,
        "description": description,
        "attributes": attributes == null
            ? []
            : List<dynamic>.from(attributes!.map((x) => x.toJson())),
      };
}
