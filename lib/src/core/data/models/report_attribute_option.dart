class ReportAttributeOption {
  int? id;
  String? label;
  String? value;
  int? points;

  ReportAttributeOption({
    this.id,
    this.label,
    this.value,
    this.points,
  });

  factory ReportAttributeOption.fromJson(Map<String, dynamic> json) =>
      ReportAttributeOption(
        id: json["id"],
        label: json["label"],
        value: json["value"],
        points: json["points"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "label": label,
        "value": value,
        "points": points,
      };

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ReportAttributeOption && other.id == id;
  }

  @override
  String toString() {
    return label ?? "";
  }

  @override
  int get hashCode => super.hashCode;
}
