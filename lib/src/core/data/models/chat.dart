import 'chat_message.dart';

class Chat {
  int id;
  int receiverId;
  String receiverName;
  String receiverImage;
  String receiverRole;
  String receiverJob;
  ChatMessage? lastMessage;
  int? unreadCount;

  Chat({
    required this.id,
    required this.receiverId,
    required this.receiverName,
    required this.receiverImage,
    required this.receiverRole,
    this.lastMessage,
    required this.unreadCount,
    required this.receiverJob,
  });

  factory Chat.fromJson(Map<String, dynamic> json) => Chat(
        id: json["id"],
        receiverId: json["receiver_id"],
        receiverName: json["receiver_name"],
        receiverImage: json["receiver_image"],
        receiverRole: json["receiver_role"],
        lastMessage: json["last_message"] != null
            ? ChatMessage.fromJson(json["last_message"])
            : null,
        unreadCount: json["unread_count"],
        receiverJob: json["receiver_job"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "receiver_id": receiverId,
        "receiver_name": receiver<PERSON><PERSON>,
        "receiver_image": receiverImage,
        "receiver_role": receiverRole,
        "last_message": lastMessage?.toJson(),
        "unread_count": unreadCount,
        "receiver_job": receiverJob,
      };
}
