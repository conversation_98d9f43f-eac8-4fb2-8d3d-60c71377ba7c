class Activity {
  int? id;
  int? sectionId;
  String? sectionName;
  String? title;
  String? description;
  String? imageUrl;
  DateTime? date;
  String? formattedDate;
  String? audienceType;
  List<String>? audience;

  Activity({
    this.id,
    this.sectionId,
    this.sectionName,
    this.title,
    this.imageUrl,
    this.description,
    this.date,
    this.formattedDate,
    required this.audienceType,
    this.audience,
  });

  factory Activity.fromJson(Map<String, dynamic> json) => Activity(
        id: json["id"],
        sectionId: json["section_id"],
        sectionName: json["section_name"],
        title: json["title"],
        imageUrl: json["image_url"],
        description: json["description"],
        audienceType: json["audience_type"],
        audience: json["audience"] == null
            ? []
            : List<String>.from(json["audience"].map((x) => x)),
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        formattedDate: json["formatted_date"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "section_id": sectionId,
        "section_name": sectionName,
        "title": title,
        "image_url": imageUrl,
        "description": description,
        "audience_type": audienceType,
        "audience":
            audience == null ? [] : List<dynamic>.from(audience!.map((x) => x)),
        "date": date?.toIso8601String(),
        "formatted_date": formattedDate,
      };
}
