class StudentAttendance {
  int? id;
  String? name;
  String? image;
  String? attendanceStatus;
  String? attendanceStatusLabel;
  String? attendanceStatusColor;
  int? subjectId;
  DateTime? date;

  StudentAttendance({
    this.id,
    this.name,
    this.image,
    this.subjectId,
    this.attendanceStatus,
    this.attendanceStatusLabel,
    this.attendanceStatusColor,
  });

  factory StudentAttendance.fromJson(Map<String, dynamic> json) =>
      StudentAttendance(
        id: json["id"],
        name: json["name"],
        image: json["image"],
        attendanceStatus: json["attendance_status"],
        attendanceStatusLabel: json["attendance_status_label"],
        attendanceStatusColor: json["attendance_status_color"],
      );

  Map<String, dynamic> toJson() => {
        "student_id": id,
        "subject_id": subjectId,
        "status": attendanceStatus,
        "date": date,
      };

  StudentAttendance clone() {
    return StudentAttendance(
      id: id,
      name: name,
      image: image,
      subjectId: subjectId,
      attendanceStatus: attendanceStatus,
      attendanceStatusLabel: attendanceStatusLabel,
      attendanceStatusColor: attendanceStatusColor,
    );
  }
}
