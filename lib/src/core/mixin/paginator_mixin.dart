import 'package:flutter/material.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

mixin PaginatorMixin {
  RxInt currentPage = 1.obs;

  RxInt lastPage = 0.obs;

  RxInt totalItems = 0.obs;

  final ScrollController scrollController = ScrollController();

  void scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void getMore({int id});

  bool canLoadMore() {
    return currentPage.value < lastPage.value;
  }
}
