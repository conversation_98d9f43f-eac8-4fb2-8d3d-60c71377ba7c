import 'package:get/get.dart';
import '../screens/change_password_screen.dart';
import '../screens/setting_screen.dart';
import '../../features/about_page/presentation/screens/about_pages_screen.dart';
import '../../features/about_page/presentation/screens/about_detail_screen.dart';
import '../../features/advertisement/presentation/screens/advertisement_detail_screen.dart';
import '../../features/attendance/presentation/screen/absence_report_screen.dart';
import '../../features/attendance/presentation/screen/student_attendance_screen.dart';
import '../../features/auth/presentation/screens/forget_code_password_screen.dart';
import '../../features/auth/presentation/screens/forget_password_screen.dart';
import '../../features/auth/presentation/screens/login_sceen.dart';
import '../../features/auth/presentation/screens/reset_password_screen.dart';
import '../../features/class_schedule/presentation/screen/class_schedule_screen.dart';
import '../../features/daily_post/presentation/screens/daily_posts_screen.dart';
import '../../features/daily_post/presentation/screens/manage_post_screen.dart';
import '../../features/daily_report/presentation/screens/daily_reports_screen.dart';
import '../../features/daily_report/presentation/screens/student_reports_screen.dart';
import '../../features/home_work/presentation/screens/homeworks_screen.dart';
import '../../features/plan/presentation/screens/plan_detail_screen.dart';
import '../../features/plan/presentation/screens/plans_screen.dart';
import '../../features/son/presentation/screens/sons_screen.dart';
import '../../features/study_resources/presentation/screens/study_resources_screen.dart';
import '../../features/study_resources/presentation/screens/manage_study_resource_screen.dart';
import '../../features/study_resources/presentation/screens/study_resource_detail_screen.dart';
import '../../features/splash/presentation/screens/splash_screen.dart';
import '../../features/advertisement/presentation/screens/advertisements_screen.dart';
import '../../features/honor_board/presentation/screens/honor_board_screen.dart';
import '../../features/honor_board/presentation/screens/honor_board_students_screen.dart';
import '../../features/home/<USER>/screens/home_screen.dart';
import '../../features/activity/presentation/screens/activities_screen.dart';
import '../../features/home_work/presentation/screens/manage_homework_screen.dart';
import '../../features/honor_board/presentation/screens/manage_honor_board_screen.dart';
import '../../features/student/presentation/screen/my_students_screen.dart';
import '../../features/student/presentation/screen/student_statistics_screen.dart';
import '../../features/student_points/presentation/screens/manage_point_criteria_screen.dart';
import '../../features/student_points/presentation/screens/point_criterias_screen.dart';
import '../../features/student_points/presentation/screens/student_points_screen.dart';

import '../../features/week_plan/presentation/screens/manage_week_plan_screen.dart';
import '../../features/chat/presentation/screens/chat_detail_screen.dart';
import '../../features/chat/presentation/screens/chat_list_screen.dart';
import '../../features/week_plan/presentation/screens/week_plans_screen.dart';
import '../../features/week_plan/bindings/week_plan_binding.dart';
import '../../features/week_plan_file/presentation/screens/week_plan_file_screen.dart';
import '../../../soon_screen.dart';

import '../../features/daily_report/presentation/screens/manage_student_report.dart';
import '../../features/teachers/presentation/screens/screens/teacher_achievement_screen.dart';
import '../../features/teachers/presentation/screens/screens/teacher_screen.dart';
import '../../features/notification/presentation/screens/notification_screen.dart';
import '../../features/teacher_achievement_file/presentation/screens/teacher_achievement_screen.dart'
    as teacher_file;
import '../../features/student_dismissal/presentation/screens/dismissals_screen.dart';
import '../../features/student_dismissal/presentation/screens/dismissal_details_screen.dart';
import '../../features/student_dismissal/presentation/screens/create_dismissal_screen.dart';

class AppRouter {
  static const String homeScreen = '/homeScreen';
  static const String soon = '/soon';
  static const String loginScreen = '/loginScreen';
  static const String commentScreen = '/commentScreen';
  static const String postScreen = '/postsScreen';
  static const String manageHomeWork = '/ManageHomeWorkScreen';
  static const String addActivity = '/addActivitiesScreen';
  static const String chooseDate = '/chooseDateScreen';
  static const String managePostScreen = '/managePostScreen';
  static const String homeworksScreen = '/HomeworksScreen';
  static const String activityScreen = '/activityScreen';
  static const String forgetPasswordScreen = '/forgetPasswordScreen';
  static const String forgetCodePasswordScreen = '/forgetCodePasswordScreen';
  static const String resetPasswordScreen = '/resetPasswordScreen';
  static const String replacePasswordScreen = '/replacePasswordScreen';

  // static const String allchatsScreen = '/allchatsScreen';
  static const String teacherWeekPlane = '/teacherWeekPlane';
  // static const String parentChatScreen = '/parentChatScreen';
  static const String chatScreen = '/chatScreen';
  static const String chatDetails = '/chatDetails';
  static const String superVisorPresence = '/superVisorPresence';
  static const String manageWeekPlanScreen = '/manageWeekPlanScreen';
  static const String parentHonorBoardScreen = '/parentHonorBoardScreen';
  static const String honorBoardStudentsScreen = '/honorBoardStudentsScreen';
  static const String sonsScreen = '/sonsScreen';
  static const String manageHonorBoardScreen = '/manageHonorBoardScreen';
  static const String updateHonorBoardScreen = '/updateHonorBoardScreen';
  static const String studentAttendanceScreen = '/studentAttendanceScreen';
  static const String absenceReportScreen = '/absenceReportScreen';
  static const String classScheduleScreen = '/classScheduleScreen';
  static const String addUpdateAdvScreen = '/addUpdateAdvScreen';
  static const String advertisementsScreen = '/advertisementsScreen';
  static const String advertisementDetailScreen = '/advertisementDetailScreen';
  static const String newsScreen = '/newsScreen';
  static const String addRate = '/addRate';
  static const String assignRatescreen = '/assignRatescreen';
  static const String addAssignRatescreen = '/addAssignRatescreen';
  static const String settingsScreen = '/settingsScreen';
  static const String aboutJenanScreen = '/aboutJenanScreen';
  static const String aboutUsScreen = '/aboutUsScreen';
  static const String aboutDetailScreen = '/aboutDetailScreen';
  static const String notificationScreen = '/notificationScreen';
  static const String teachersScreen = '/teachersScreen';
  static const String teacherAchievementScreen = '/teacherAchievementScreen';
  static const String myStudentsScreen = '/myStudentsScreen';

  static const String splashScreen = '/splashScreen';

  static final String plansScreen = '/plansScreen';
  static final String planDetailScreen = '/planDetailScreen';
  static final String studyPlanScreen = '/studyPlanScreen';
  static final String studyResourcesScreen = '/studyResourcesScreen';
  static final String manageStudyResourceScreen = '/manageStudyResourceScreen';
  static final String studyResourceDetailScreen = '/studyResourceDetailScreen';
  static final String studentAchievementScreen = '/studentAchievementScreen';
  static final String teacherAchievementFileScreen =
      '/teacherAchievementFileScreen';
  static final String weekPlanFileScreen = '/weekPlanFileScreen';

  // student points
  static final String studentPointsScreen = '/studentPointsScreen';
  static final String managePointCriteriaScreen = '/managePointCriteriaScreen';
  static final String pointCriteriasScreen = '/pointCriteriasScreen';
  // daily reports
  static const String dailyReportsScreen = '/dailyReportsScreen';
  static final String studentReportsScreen = '/studentReportsScreen';
  // static final String reportsScreen = '/reportsScreen';
  static final String manageStudentReportScreen = '/manageStudentReportScreen';

  // student dismissal
  static final String dismissalsScreen = '/dismissalsScreen';
  static final String dismissalDetailsScreen = '/dismissalDetailsScreen';
  static final String createDismissalScreen = '/createDismissalScreen';

  static final List<GetPage> pages = [
    GetPage(
      name: forgetPasswordScreen,
      page: () => const ForgetPasswordScreen(),
    ),
    GetPage(
      name: forgetCodePasswordScreen,
      page: () => const ForgetCodePasswordScreen(),
    ),
    GetPage(name: resetPasswordScreen, page: () => const ResetPasswordScreen()),
    GetPage(
      name: replacePasswordScreen,
      page: () => const ChangePasswordScreen(),
    ),
    GetPage(name: loginScreen, page: () => const LoginScreen()),
    GetPage(name: homeScreen, page: () => const HomeScreen()),
    GetPage(name: postScreen, page: () => const DailyPosts()),
    GetPage(name: manageHomeWork, page: () => const ManageHomeWorkScreen()),
    GetPage(name: managePostScreen, page: () => const ManagePostScreen()),
    GetPage(name: homeworksScreen, page: () => const HomeworksScreen()),
    GetPage(name: activityScreen, page: () => const ActivitiesScreen()),
    GetPage(
      name: teacherWeekPlane,
      page: () => const WeekPlansScreen(),
      binding: WeekPlanBinding(),
    ),
    GetPage(name: chatScreen, page: () => const ChatListScreen()),
    GetPage(
      name: chatDetails,
      page: () => ChatDetailsScreen(chat: Get.arguments),
    ),

    GetPage(
      name: manageWeekPlanScreen,
      page: () => const ManageWeekPlanScreen(),
      binding: WeekPlanBinding(),
    ),
    GetPage(name: parentHonorBoardScreen, page: () => const HonorBoardScreen()),
    GetPage(
      name: honorBoardStudentsScreen,
      page: () => HonorBoardStudentsScreen(honor: Get.arguments),
    ),
    GetPage(name: sonsScreen, page: () => const SonsScreen()),

    GetPage(
      name: manageHonorBoardScreen,
      page: () => const ManageHonorBoardScreen(),
    ),
    GetPage(
      name: studentAttendanceScreen,
      page: () => const StudentAttendanceScreen(),
    ),
    GetPage(name: absenceReportScreen, page: () => const AbsenceReportScreen()),
    GetPage(name: classScheduleScreen, page: () => const ClassScheduleScreen()),
    GetPage(
      name: advertisementsScreen,
      page: () => const AdvertisementsScreen(),
    ),
    GetPage(
      name: advertisementDetailScreen,
      page: () => const AdvertisementDetailScreen(),
    ),

    GetPage(name: settingsScreen, page: () => const SettingSreen()),
    GetPage(name: splashScreen, page: () => const SplashScreen()),
    GetPage(name: soon, page: () => const SoonScreen()),
    GetPage(name: aboutJenanScreen, page: () => const AboutPagesScreen()),
    GetPage(name: notificationScreen, page: () => const NotificationsScreen()),
    GetPage(name: teachersScreen, page: () => const TeachersScreen()),
    GetPage(
      name: teacherAchievementScreen,
      page: () => TeacherAchievementScreen(teacher: Get.arguments),
    ),
    GetPage(name: myStudentsScreen, page: () => const MyStudentsScreen()),

    GetPage(
      name: aboutDetailScreen,
      page: () => AboutDetailScreen(aboutUs: Get.arguments),
    ),
    GetPage(name: plansScreen, page: () => PlansScreen()),
    GetPage(
      name: planDetailScreen,
      page: () => PlanDetailScreen(planType: Get.arguments),
    ),
    GetPage(name: weekPlanFileScreen, page: () => const WeekPlanFileScreen()),
    GetPage(
      name: studyResourcesScreen,
      page: () => const StudyResourcesScreen(),
    ),
    GetPage(
      name: manageStudyResourceScreen,
      page: () => const ManageStudyResourceScreen(),
    ),
    GetPage(
      name: studyResourceDetailScreen,
      page: () => const StudyResourceDetailScreen(),
    ),
    GetPage(
      name: studentAchievementScreen,
      page: () => StudentStatisticsScreen(student: Get.arguments),
    ),
    GetPage(
      name: teacherAchievementFileScreen,
      page: () => teacher_file.TeacherAchievementScreen(),
    ),
    // student points
    GetPage(name: studentPointsScreen, page: () => const StudentPointsScreen()),
    GetPage(
      name: managePointCriteriaScreen,
      page: () => const ManagePointCriteriaScreen(),
    ),
    GetPage(
      name: pointCriteriasScreen,
      page: () => const PointCriteriasScreen(),
    ),
    // daily reports
    GetPage(name: dailyReportsScreen, page: () => const DailyReportsScreen()),
    GetPage(
      name: studentReportsScreen,
      page:
          () => StudentReportsScreen(
            sectionId: Get.arguments['section_id'],
            student: Get.arguments['student'],
          ),
    ),
    // GetPage(
    //   name: reportsScreen,
    //   page: () => ReportsScreen(
    //       // sectionId: Get.arguments['section_id'],
    //       // student: Get.arguments['student'],
    //       ),
    // ),
    GetPage(
      name: manageStudentReportScreen,
      page:
          () => ManageStudentReportScrren(
            report: Get.arguments['report'],
            studentReport: Get.arguments['student_report'],
            // sectionId: Get.arguments['section_id'],
            studentId: Get.arguments['student_id'],
          ),
    ),

    // Student Dismissal Routes
    GetPage(name: dismissalsScreen, page: () => const DismissalsScreen()),
    GetPage(
      name: dismissalDetailsScreen,
      page: () => const DismissalDetailsScreen(),
    ),
    GetPage(
      name: createDismissalScreen,
      page: () => const CreateDismissalScreen(),
    ),
  ];
}
