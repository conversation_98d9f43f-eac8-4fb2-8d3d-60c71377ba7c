import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/plan_file.dart';
import '../enum/plan_type.dart';

class PlanController extends BaseController {
  static PlanController get instance => Get.find();
  Rx<PlanFile?> planFile = Rx<PlanFile?>(null);

  void getPlan(int sectionId, PlanType planType) {
    callApi(() async {
      planFile.value = planType == PlanType.study
          ? await apiProvider.getStudyPlan(sectionId: sectionId)
          : (planType == PlanType.activity
              ? await apiProvider.getActivityPlan(sectionId: sectionId)
              : await apiProvider.getDetailedStudyPlan(sectionId: sectionId));
      if (planFile.value?.fileUrl == null) {
        pageStatus.value = PageStatus.empty;
      } else {
        pageStatus.value = PageStatus.loaded;
      }
    });
  }
}
