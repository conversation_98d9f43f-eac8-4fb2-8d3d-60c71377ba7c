import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/plan_controller.dart';
import '../../enum/plan_type.dart';

class PlanDetailScreen extends StatelessWidget {
  const PlanDetailScreen({
    super.key,
    required this.planType,
  });
  final PlanType planType;
  @override
  Widget build(BuildContext context) {
    var controller = Get.put(PlanController());
    var userRole = AuthService.instance.user.value?.role;
    var title = "";
    var message = "";
    switch (planType) {
      case PlanType.activity:
        title = "Activity Plan".tr;
        message = "No Activity Plan yet".tr;
        break;
      case PlanType.study:
        title = "Student study plan".tr;
        message = "No Study Plan yet".tr;
        break;
      case PlanType.detailed:
        title = "Detailed Plan".tr;
        message = "No Detailed Plan yet".tr;
        break;
    }
    return LayoutScreen(
      title: title,
      withBackground: false,
      controller: PlanController.instance,
      bodyHeader: SectionWithSubjectsWidget(
        showSections: userRole == Roles.Teacher,
        showSubjects: false,
        // onSubjectChanged: (sectionId, subjectId) {
        //   controller.getPlan(sectionId, planType);
        // },
        onSectionChanged: (sectionId) {
          controller.getPlan(sectionId, planType);
        },
      ),
      body: Obx(
        () {
          return controller.planFile.value?.fileUrl == null
              ? EmptyWidget(
                  message: message,
                )
              : Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: controller.planFile.value!.fileUrl!.endsWith('.pdf')
                      ? PDF(
                          swipeHorizontal: false,
                          enableSwipe: true,
                          pageFling: false,
                        ).cachedFromUrl(
                          controller.planFile.value?.fileUrl ?? "")
                      : CustomCachedNetworkImage(
                          imageUrl: controller.planFile.value?.fileUrl ?? "",
                        ),
                );
        },
      ),
    );
  }
}
