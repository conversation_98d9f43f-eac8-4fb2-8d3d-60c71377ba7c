import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../../auth/services/auth_service.dart';
import '../../enum/plan_type.dart';

class PlansScreen extends StatelessWidget {
  const PlansScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: 'Plans'.tr,
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          CustomListTile(
            title: "Week Plan".tr,
            trailing: const Icon(
              Icons.arrow_forward_ios,
              size: 20,
              color: Colors.grey,
            ),
            onTap: () => Get.toNamed(AppRouter.teacherWeekPlane),
            margin: EdgeInsets.only(bottom: 16),
          ),
          if (!(AuthService.instance.user.value?.isKindergarten ?? false))
            CustomListTile(
              title: "Student study plan".tr,
              trailing: const Icon(
                Icons.arrow_forward_ios,
                size: 20,
                color: Colors.grey,
              ),
              onTap: () => Get.toNamed(
                AppRouter.planDetailScreen,
                arguments: PlanType.study,
              ),
              margin: EdgeInsets.only(bottom: 16),
            ),
          CustomListTile(
            title: "Activity Plan".tr,
            trailing: const Icon(
              Icons.arrow_forward_ios,
              size: 20,
              color: Colors.grey,
            ),
            onTap: () => Get.toNamed(
              AppRouter.planDetailScreen,
              arguments: PlanType.activity,
            ),
            margin: EdgeInsets.only(bottom: 16),
          ),
          // Teacher Achievement File (only for teachers)
          if (AuthService.instance.user.value?.role == 'teacher')
            CustomListTile(
              title: "Teacher Achievements".tr,
              trailing: const Icon(
                Icons.arrow_forward_ios,
                size: 20,
                color: Colors.grey,
              ),
              onTap: () => Get.toNamed(AppRouter.teacherAchievementFileScreen),
              margin: EdgeInsets.only(bottom: 16),
            ),
        ],
      ),
    );
  }
}
