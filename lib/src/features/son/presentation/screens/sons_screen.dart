import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../home/<USER>/son_controller.dart';
import '../../../student/presentation/widgets/student_list_tile.dart';


class SonsScreen extends StatelessWidget {
  const SonsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SonController controller = Get.put(SonController());
    // var userRole = AuthService.instance.user.value?.role;

    return LayoutScreen(
      title: 'My Sons'.tr,
      body: Obx(() => ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: controller.sons.length,
            itemBuilder: (context, index) {
              return StudentListTile(
                student: controller.sons[index],
              );
            },
            separatorBuilder: (BuildContext context, int index) {
              return 16.verticalSpace;
            },
          )),
    );
  }
}
