import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/user.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../../../core/widgets/form_fields/date_time_form_field.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../widgets/reports_bottom_sheet.dart';

import '../../../../core/widgets/custom_list_title.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../controller/daily_reports_controller.dart';
import '../widgets/report_expansion_tile.dart';

class StudentReportsScreen extends StatelessWidget {
  const StudentReportsScreen({
    super.key,
    required this.student,
    required this.sectionId,
  });
  final User student;
  final int? sectionId;
  @override
  Widget build(BuildContext context) {
    DailyReportsController controller = Get.put(DailyReportsController());
    return LayoutScreen(
      emptyWidegt: EmptyWidget(),
      backgroundColor: AuthService.instance.user.value?.role == Roles.Student
          ? Colors.white
          : null,
      title: student.name ?? "",
      bodyHeader: SectionWithSubjectsWidget(
        selectedSectionId: sectionId,
        showSubjects: AuthService.instance.user.value?.role == Roles.Teacher,
        showSections: false,
        onSubjectChanged: (sectionId, subjectId) {
          Get.log("subject id $subjectId");
          controller.selectedSubjetId = subjectId;
          if (AuthService.instance.user.value?.role == Roles.Teacher) {
            controller.getStudentReport(
                subjectId: subjectId, studentId: student.id!);
            controller.getDailyReports(subjectId, student.id ?? 0);
          }
        },
      ),
      body: Obx(
        () => Container(
          color: AuthService.instance.user.value?.role == Roles.Student
              ? Colors.white
              : null,
          child: Column(
            children: [
              if (AuthService.instance.user.value?.role == Roles.Student)
                CustomDateTimeFormField(

                    // hint: "",
                    // initialPickerDateTime: DateTime.now(),

                    initialValue: DateTime.now(),
                    margin: EdgeInsets.only(
                        bottom: 0, top: 16, left: 16, right: 16),
                    onChanged: (value) {
                      if (value != null) {
                        ;
                        controller.getStudentReport(
                            date: "${value.year}-${value.month}-${value.day}");
                      }
                    }),
              Expanded(
                child: ListView.separated(
                  shrinkWrap: true,
                  padding: EdgeInsets.all(16),
                  itemBuilder: (context, index) {
                    return ReportExpansionTile(
                      studentReport: controller.studentReports.value[index],
                    );
                    //             ExpansionTile(
                    //               initiallyExpanded: true,
                    //               collapsedBackgroundColor: Colors.white,
                    //               backgroundColor: Colors.white,
                    //               // showTrailingIcon: false,
                    //               collapsedShape: RoundedRectangleBorder(
                    //                   borderRadius: BorderRadius.circular(10.r)),
                    //               // trailing: Icon(Icons.arrow_downward),
                    //               tilePadding: EdgeInsets.symmetric(horizontal: 8),
                    //               children: [
                    // //  controller.studentReports.value[index].
                    //               ],
                    //               shape: RoundedRectangleBorder(
                    //                   borderRadius: BorderRadius.circular(10.r)),
                    //               title: CustomListTile(
                    //                 backgroundColor: Colors.transparent,
                    //                 title: controller
                    //                         .studentReports.value[index].reportGroup?.displayText ??
                    //                     "",
                    //                 subtitle:
                    //                     controller.studentReports.value[index].createdAt ?? "",
                    //                 // subtitleWidget: CustomText(
                    //                 //   text: controller.studentReports.value[index].createdAt ?? "",
                    //                 //   fontSize: (16.sp) - 2,
                    //                 //   maxLine: 3,
                    //                 //   fontWeight: FontWeight.bold,
                    //                 //   softWrap: true,
                    //                 //   textAlign: TextAlign.start,
                    //                 // ),
                    //                 detail: CustomText(
                    //                   text: controller.studentReports.value[index].reportGroup
                    //                           ?.description ??
                    //                       "",
                    //                   fontSize: (16.sp) - 2,
                    //                   maxLine: 3,
                    //                   fontWeight: FontWeight.bold,
                    //                   softWrap: true,
                    //                   textAlign: TextAlign.start,
                    //                   color: AppColor.greyColor,
                    //                 ),
                    //                 // onTap: () {
                    //                 //   // Get.toNamed(AppRouter.manageStudentReportScreen, arguments: {
                    //                 //   //   'report':
                    //                 //   //       controller.studentReports.value[index].reportGroup,
                    //                 //   //   'student_report': controller.studentReports.value[index],
                    //                 //   // });
                    //                 // },
                    //               ),
                    //             );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16,
                    );
                  },
                  itemCount: controller.studentReports.value.length,
                ),
              ),
            ],
          ),
        ),
      ),
      controller: controller,
      floatingActionButton:
          AuthService.instance.user.value?.role != Roles.Teacher
              ? null
              : FloatingActionButton(
                  onPressed: () {
                    Get.bottomSheet(ReportBottomSheet(
                      studentId: student.id ?? 0,
                    ));
                    // Get.toNamed(AppRouter.reportsScreen);
                  },
                  child: Icon(
                    Icons.add,
                  ),
                ),
    );
  }
}
