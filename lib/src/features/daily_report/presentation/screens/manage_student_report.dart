import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/screens/layout_screen.dart';

import '../../../../core/data/models/report_type.dart';
import '../../../../core/data/models/student_report.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../controller/daily_reports_controller.dart';
import '../widgets/report_attribute_form_field.dart';

class ManageStudentReportScrren extends StatelessWidget {
  const ManageStudentReportScrren(
      {super.key, required this.report, this.studentReport, this.studentId});
  final ReportType report;
  final StudentReport? studentReport;
  final int? studentId;
  @override
  Widget build(BuildContext context) {
    final Map<String, dynamic> data = {};
    var formKey = GlobalKey<FormState>();
    DailyReportsController controller = DailyReportsController.instance;
    return LayoutScreen(
      title: report.displayText ?? "",
      // controller: controller,

      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Form(
          key: formKey,
          child: Column(
            children: [
              ...report.attributes
                      ?.map(
                        (e) => ReportAttributeFormField(
                          attribute: e,
                          currentValue: studentReport?.records
                              ?.firstWhereOrNull(
                                  (element) => element.attribute?.id == e.id)
                              ?.value,
                          onSaved: (value) {
                            if (value != null) {
                              data.addAll({e.id.toString(): value});
                            }
                          },
                        ),
                      )
                      .toList() ??
                  [],
              if (studentReport == null)
                SubmitButton(
                  text: 'Add'.tr,
                  onSubmit: () async {
                    var dataVM = <String, dynamic>{};
                    dataVM["records"] = data.values.toList();

                    dataVM["report_group_id"] = report.id;
                    dataVM["student_id"] = studentId;
                    print(dataVM);
                    await controller.addStuentReport(dataVM);
                  },
                  formKey: formKey,
                )
            ],
          ),
        ),
      ),
    );
  }
}
