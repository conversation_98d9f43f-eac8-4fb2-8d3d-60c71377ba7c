import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/user.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../../student/controller/student_controller.dart';

class DailyReportsScreen extends StatelessWidget {
  const DailyReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    StudentController studentController = Get.put(StudentController());
    int currentSectionId = 0;
    return LayoutScreen(
      title: 'Daily Reports',
      bodyHeader: SectionWithSubjectsWidget(
        showSections: AuthService.instance.user.value?.role == Roles.Teacher,
        showSubjects: false,
        onSectionChanged: (sectionId) {
          currentSectionId = sectionId;
          studentController.getStudents(sectionId);
        },
      ),
      controller: studentController,
      body: Obx(
        () => ListView.separated(
          padding: EdgeInsets.all(16),
          itemBuilder: (context, index) {
            return CustomListTile(
              title: studentController.students[index].name ?? "",
              leading: CustomCachedNetworkImage(
                imageUrl: studentController.students[index].image ?? "",
                border: Border.all(
                  width: 0.5,
                  color: AppColor.primaryColor,
                ),
              ),
              onTap: () {
                User? student = studentController.students[index];
                student.sectionId = currentSectionId;
                Get.toNamed(
                  AppRouter.studentReportsScreen,
                  arguments: {
                    "student": student,
                    "section_id": currentSectionId
                  },
                );
              },
            );
          },
          separatorBuilder: (context, index) {
            return SizedBox(
              height: 16,
            );
          },
          itemCount: studentController.students.length,
        ),
      ),
    );
  }
}
