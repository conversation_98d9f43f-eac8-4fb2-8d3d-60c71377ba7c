import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../controller/daily_reports_controller.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/errors/loading_widget.dart';

class ReportBottomSheet extends StatelessWidget {
  const ReportBottomSheet({super.key, required this.studentId});
  final int studentId;
  @override
  Widget build(BuildContext context) {
    DailyReportsController controller = Get.put(DailyReportsController());
    return CustomBottomSheet(
      title: 'Daily Reports'.tr,
      color: AppColor.scaffoldColor,
      body: Builder(builder: (_) {
        return FutureBuilder(
            future: controller.reports.value.isEmpty
                ? controller.getDailyReports(
                    controller.selectedSubjetId ?? 0, studentId)
                : null,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: const LoadingWidget(),
                );
              }
              if (controller.reports.value.isEmpty) {
                return Container(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: CustomText(
                    text: "No daily reports".tr,
                    color: AppColor.greyColor3,
                  ),
                );
              }

              return reportsList(controller);
            });
      }),
    );
  }

  ListView reportsList(DailyReportsController controller) {
    return ListView.separated(
      shrinkWrap: true,
      padding: EdgeInsets.all(16),
      itemBuilder: (context, index) {
        return CustomListTile(
          title: controller.reports.value[index].displayText,
          subtitleWidget: CustomText(
            text: controller.reports.value[index].description ?? "",
            fontSize: (16.sp) - 2,
            maxLine: 3,
            fontWeight: FontWeight.bold,
            softWrap: true,
            textAlign: TextAlign.start,
          ),
          onTap: () async {
            Get.back();
            await Get.toNamed(AppRouter.manageStudentReportScreen, arguments: {
              "student_id": studentId,
              'report': controller.reports.value[index]
            });
          },
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 16,
        );
      },
      itemCount: controller.reports.value.length,
    );
  }
}
