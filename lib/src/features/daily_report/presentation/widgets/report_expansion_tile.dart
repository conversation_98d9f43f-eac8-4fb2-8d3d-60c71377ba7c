import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../core/data/models/student_report.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/utils/helper_function.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';

import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../auth/services/auth_service.dart';

class ReportExpansionTile extends StatelessWidget {
  const ReportExpansionTile({super.key, required this.studentReport});

  final StudentReport studentReport;

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      initiallyExpanded: true,
      collapsedBackgroundColor:
          AuthService.instance.user.value?.role == Roles.Student
              ? null
              : Colors.white,
      backgroundColor: AuthService.instance.user.value?.role == Roles.Student
          ? null
          : Colors.white,
      // showTrailingIcon: false,
      collapsedShape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.r)),
      // trailing: Icon(Icons.arrow_downward),
      tilePadding: EdgeInsets.symmetric(horizontal: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.r)),
      title: CustomListTile(
        backgroundColor: Colors.transparent,
        title: studentReport.reportGroup?.displayText ?? "",
        subtitle: studentReport.createdAt ?? "",

        detail: CustomText(
          text: studentReport.reportGroup?.description ?? "",
          fontSize: (16.sp) - 2,
          maxLine: 3,
          fontWeight: FontWeight.bold,
          softWrap: true,
          textAlign: TextAlign.start,
          color: AppColor.greyColor,
        ),
        // onTap: () {
        //   // Get.toNamed(AppRouter.manageStudentReportScreen, arguments: {
        //   //   'report':
        //   //       controller.studentReports.value[index].reportGroup,
        //   //   'student_report': controller.studentReports.value[index],
        //   // });
        // },
      ),
      children: [
        ...studentReport.records
                ?.map((record) => Container(
                      width: double.maxFinite,
                      padding: EdgeInsetsDirectional.only(
                          start: 16, end: 16, bottom: 8),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding:
                                const EdgeInsetsDirectional.only(start: 0.0),
                            child: CustomText(
                              text: record.attribute?.label ?? "",
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              textAlign: TextAlign.start,
                            ),
                          ),
                          if (record.attribute?.type == "text_input")
                            Text(record.value ?? "")
                          else if (record.attribute?.type == "text_select")
                            Text(record.attribute?.options
                                    ?.firstWhere((e) =>
                                        e.id ==
                                        int.tryParse(record.value ?? ""))
                                    .label ??
                                "")
                          else if (record.attribute?.type == "emoji_select")
                            Text(
                                "${record.attribute?.options?.firstWhere((e) => e.id == int.tryParse(record.value ?? "")).value ?? ""} (${record.attribute?.options?.firstWhere((e) => e.id == int.tryParse(record.value ?? "")).label ?? ""})")
                          else if (record.attribute?.type == "image_select")
                            CustomCachedNetworkImage(
                                height: 30,
                                width: 30,
                                imageUrl: record.attribute?.options
                                            ?.firstWhere((e) =>
                                                e.id ==
                                                int.tryParse(
                                                    record.value ?? ""))
                                            .value ==
                                        null
                                    ? ""
                                    : "$imageBaseUrl${record.attribute?.options?.firstWhere((e) => e.id == int.tryParse(record.value ?? "")).value}")
                          else if (record.attribute?.type == "image_upload")
                            Center(
                              child: CustomCachedNetworkImage(
                                  height: 200,
                                  width: 200,
                                  previewable: true,
                                  imageUrl: record.value == null
                                      ? ""
                                      : "$imageBaseUrl${record.value}"),
                            )
                          else if (record.attribute?.type == "file_upload")
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(7.r),
                                color: AppColor.primaryColor
                                    .withValues(alpha: 0.1),
                              ),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(7.r),
                                onTap: () {
                                  HelperFunction.openUrl(
                                      "$imageBaseUrl${record.value}");
                                },
                                child: Container(
                                  padding: EdgeInsets.all(10),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SvgPicture.asset(
                                        AppAssets.uploadFile,
                                        height: 25,
                                        fit: BoxFit.contain,
                                      ),
                                      SizedBox(
                                        width: 7,
                                      ),
                                      Flexible(
                                        child: CustomText(
                                          text: record.value ?? "",
                                          fontSize: 14,
                                          color: AppColor.greyColor5,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          else if (record.attribute?.type == "week_plan_select")
                            Text(record
                                    .attribute?.weekPlans?.firstOrNull?.title ??
                                "")
                          else if (record.attribute?.type == "homework_select")
                            Text(record
                                    .attribute?.homeworks?.firstOrNull?.title ??
                                ""),
                          if (record.id != studentReport.records?.last.id)
                            Divider(
                              // height: 0.1,
                              color:
                                  AppColor.primaryColor.withValues(alpha: 0.1),
                            )
                        ],
                      ),
                    ))
                .toList() ??
            []
      ],
    );
  }
}
