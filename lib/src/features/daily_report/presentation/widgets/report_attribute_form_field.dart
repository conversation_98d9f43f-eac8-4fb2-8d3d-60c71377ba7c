import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide MultipartFile;

import '../../../../core/widgets/form_fields/custom_text_field.dart';

import '../../../../core/data/models/report_attribute.dart';
import '../../../../core/data/models/report_attribute_option.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/form_fields/custom_dropdown_field.dart';
import '../../../../core/widgets/form_fields/file_form_feild.dart';
import '../../../../core/widgets/form_fields/image_form_feild.dart';
import '../../../../core/widgets/form_fields/radio_form_field.dart';
import 'image_select_field.dart';

class ReportAttributeFormField extends StatelessWidget {
  const ReportAttributeFormField(
      {super.key,
      required this.attribute,
      required this.onSaved,
      this.currentValue});
  final ReportAttribute attribute;
  final String? currentValue;
  final void Function(Map<String, dynamic>?) onSaved;

  @override
  Widget build(BuildContext context) {
    print("init value $currentValue");
    if (attribute.type == "text_input") {
      return CustomTextField(
        hint: attribute.label,
        title: attribute.label,
        initialValue: currentValue,
        enabled: currentValue == null,
        minLine: 3,
        maxLine: 10,
        onSaved: (value) {
          onSaved({
            "attribute_id": attribute.id,
            "value": value,
          });
        },
        validator: (value) {
          if (value?.isEmpty ?? true) {
            return "This field is required".tr;
          }
        },
      );
    } else if (attribute.type == "text_select") {
      return RadioFormField<ReportAttributeOption>(
        title: attribute.label,
        options: attribute.options ?? [],
        enabled: currentValue == null,
        initialValue: ReportAttributeOption(
          id: int.tryParse(currentValue ?? "0"),
        ),
        optionSubtitleBuilder: attribute.hasPoints != true
            ? null
            : (option) => CustomText(
                  color: AppColor.greyColor,
                  text: "${option.points?.toString() ?? ""} ${"Point".tr}",
                  fontSize: 14.sp,
                  textAlign: TextAlign.start,
                  fontWeight: FontWeight.bold,
                ),
        onSaved: (value) {
          onSaved({
            "attribute_id": attribute.id,
            "value": value!.id,
          });
        },
        validator: (value) {
          if (value == null) {
            return "This field is required".tr;
          }
        },
      );
    } else if (attribute.type == "emoji_select") {
      return RadioFormField<ReportAttributeOption>(
        title: attribute.label,
        enabled: currentValue == null,
        initialValue: ReportAttributeOption(
          id: int.tryParse(currentValue ?? "0"),
        ),
        optionTitleBuilder: (option) => CustomText(
          color: AppColor.greyColor5,
          text: "${option.value ?? ''} (${option.label})",
          fontSize: 14.sp,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.bold,
        ),
        optionSubtitleBuilder: attribute.hasPoints != true
            ? null
            : (option) => CustomText(
                  color: AppColor.greyColor,
                  text: "${option.points?.toString() ?? ""} ${"Point".tr}",
                  fontSize: 14.sp,
                  textAlign: TextAlign.start,
                  fontWeight: FontWeight.bold,
                ),
        options: attribute.options ?? [],
        onSaved: (value) {
          onSaved({
            "attribute_id": attribute.id,
            "value": value!.id,
          });
        },
        validator: (value) {
          if (value == null) {
            return "This field is required".tr;
          }
        },
      );
    } else if (attribute.type == "image_select") {
      return ImageSelectField(
        title: attribute.label,
        enabled: currentValue == null,
        initialValue:
            ReportAttributeOption(id: int.tryParse(currentValue ?? "0")),
        options: attribute.options ?? [],
        onSaved: (value) {
          onSaved({
            "attribute_id": attribute.id,
            "value": value?.id,
          });
        },
        validator: (value) {
          if (value == null) {
            return "This field is required".tr;
          }
        },
      );
    } else if (attribute.type == "image_upload") {
      return ImageFormField(
          title: attribute.label,
          imageType: ImageType.network,
          enabled: currentValue == null,
          initialValue:
              currentValue == null ? null : (imageBaseUrl + currentValue!),
          validator: (value) {
            if (value == null) {
              return "This field is required".tr;
            }
          },
          onSaved: (value, imageType) {
            if (value != null) {
              onSaved({
                "attribute_id": attribute.id,
                "value": MultipartFile.fromFileSync(value),
              });
            }
          });
    } else if (attribute.type == "file_upload") {
      return FileFormField(
          title: attribute.label,
          fileType: FileType.network,
          initialValue: currentValue,
          validator: (value) {
            if (value == null) {
              return "This field is required".tr;
            }
          },
          onSaved: (value, fileType) async {
            if (value != null) {
              onSaved({
                "attribute_id": attribute.id,
                "value": MultipartFile.fromFileSync(value),
              });
            }
          });
    } else if (attribute.type == "week_plan_select") {
      return CustomDropdownField(
          title: attribute.label,
          items: attribute.weekPlans ?? [],
          enabled: currentValue == null,
          initialItem: currentValue == null
              ? null
              : attribute.weekPlans
                  ?.firstWhere((e) => e.id == int.tryParse(currentValue ?? "")),
          validator: (value) {
            if (value == null) {
              return "This field is required".tr;
            }
          },
          onChanged: (value) async {
            if (value != null) {
              onSaved({
                "attribute_id": attribute.id,
                "value": (value.id),
              });
            }
          });
    } else if (attribute.type == "homework_select") {
      return CustomDropdownField(
          title: attribute.label,
          items: attribute.homeworks ?? [],
          enabled: currentValue == null,
          initialItem: currentValue == null
              ? null
              : attribute.homeworks
                  ?.firstWhere((e) => e.id == int.tryParse(currentValue ?? "")),
          validator: (value) {
            if (value == null) {
              return "This field is required".tr;
            }
          },
          onChanged: (value) async {
            if (value != null) {
              onSaved({
                "attribute_id": attribute.id,
                "value": (value.id),
              });
            }
          });
    }
    return SizedBox.shrink();
  }
}
