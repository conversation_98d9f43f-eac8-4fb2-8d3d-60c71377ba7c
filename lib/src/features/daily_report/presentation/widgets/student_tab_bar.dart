import 'package:flutter/material.dart';
import '../../../../core/data/models/user.dart';

class StudentTabBar extends StatelessWidget {
  const StudentTabBar(
      {super.key, required this.students, required this.onStudentChanged});
  final List<User> students;
  final Function(User student) onStudentChanged;
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: DefaultTabController(
        length: students.length,
        child: TabBar(
          tabAlignment: TabAlignment.center,
          padding: const EdgeInsets.all(0),
          isScrollable: true,
          onTap: (index) {
            onStudentChanged(students[index]);
          },
          tabs: students.map(
            (e) {
              return Tab(
                text: e.name,
              );
            },
          ).toList(),
        ),
      ),
    );
  }
}
