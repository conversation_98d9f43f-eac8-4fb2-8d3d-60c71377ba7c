import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/data/models/report_attribute_option.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_text.dart';

class ImageSelectField extends StatelessWidget {
  const ImageSelectField(
      {super.key,
      required this.options,
      this.title,
      this.onSaved,
      this.margin,
      this.validator,
      this.enabled,
      this.initialValue});

  final List<ReportAttributeOption> options;

  final String? title;

  final void Function(ReportAttributeOption?)? onSaved;
  final String? Function(ReportAttributeOption?)? validator;

  final EdgeInsetsGeometry? margin;
  final bool? enabled;
  final ReportAttributeOption? initialValue;
  @override
  Widget build(BuildContext context) {
    return FormField<ReportAttributeOption>(
        validator: validator,
        onSaved: onSaved,
        initialValue: initialValue,
        builder: (state) {
          print("state $state.value");
          return Container(
            margin: margin ?? const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null)
                  Padding(
                    padding:
                        EdgeInsetsDirectional.only(start: 8, bottom: 8.0.h),
                    child: CustomText(
                      color: AppColor.greyColor5,
                      text: title ?? "",
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                SingleChildScrollView(
                  child: Row(
                      children: options
                          .map(
                            (e) => Container(
                              margin: EdgeInsetsDirectional.only(end: 10),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(10),
                                onTap: () {
                                  if (enabled == false) {
                                    return;
                                  }
                                  state.didChange(e);
                                },
                                child: Container(
                                  height: 50,
                                  width: 50,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(
                                        color: state.value == e
                                            ? AppColor.primaryColor
                                            : AppColor.greyColor),
                                  ),
                                  child: CustomCachedNetworkImage(
                                    imageUrl: imageBaseUrl + (e.value ?? ""),
                                    fit: BoxFit.cover,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ),
                            ),
                          )
                          .toList()),
                ),
                if (state.hasError)
                  Padding(
                    padding: const EdgeInsets.only(left: 16.0, top: 4.0),
                    child: Text(
                      state.errorText ?? '',
                      style: TextStyle(color: AppColor.redColor),
                    ),
                  ),
              ],
            ),
          );
        });
  }
}
