import 'package:get/get.dart' hide FormData;

import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/report_type.dart';
import 'package:dio/dio.dart';

import '../../../core/data/models/student_report.dart';
import '../../../core/utils/app_consts.dart';
import '../../../core/utils/helper_function.dart';
import '../../auth/services/auth_service.dart';

class DailyReportsController extends BaseController {
  static DailyReportsController get instance => Get.find();

  final Rx<List<ReportType>> reports = Rx<List<ReportType>>([]);
  final Rx<List<StudentReport>> studentReports = Rx<List<StudentReport>>([]);
  int? selectedSubjetId;

  Future<void> getDailyReports(int subjcetId, int studentId) async {
    await callApi(() async {
      reports.value = [];

      final response = await apiProvider.getReportTypes(
          subjcetId: subjcetId, studentId: studentId);

      reports.value = response;
    }, withLoading: false);
  }

  Future<void> addStuentReport(Map<String, dynamic> data) async {
    await callApi(() async {
      var response = await apiProvider.addStudentReport(FormData.fromMap(data));
      Get.back();
      Get.back();
      setPageStatus(PageStatus.loaded);
      HelperFunction.showSuccessMessage(response.message ?? "");
    }, withLoading: false);
  }

  Future<void> getStudentReport(
      {int? subjectId, int? studentId, String? date}) async {
    await callApi(() async {
      final response = await apiProvider.getStudentReport(
          studentId: studentId, subjectId: subjectId, date: date);

      studentReports.value = response;
      if (studentReports.value.isEmpty) {
        setPageStatus(PageStatus.empty);
      }
      setPageStatus(PageStatus.loaded);
    });
  }

  @override
  void onInit() {
    if (AuthService.instance.user.value?.role == Roles.Student) {
      getStudentReport();
    }

    super.onInit();
  }
}
