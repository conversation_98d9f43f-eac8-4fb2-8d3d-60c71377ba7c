import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/utils/helper_function.dart';
import '../models/dismissal_status.dart';
import '../models/student_dismissal.dart';

// For better code organization, we could move these to a separate file
extension HelperFunctionExtension on HelperFunction {
  static void showSuccessToast(String message) {
    HelperFunction.showSuccessMessage(message);
  }

  static void showErrorToast(String message) {
    HelperFunction.showErrorMessage(message);
  }
}

class StudentDismissalController extends BaseController {
  static StudentDismissalController get instance => Get.find();

  // Reactive state variables
  RxList<StudentDismissal> dismissals = RxList<StudentDismissal>([]);
  RxInt page = 1.obs;
  RxInt lastPage = 1.obs;
  Rx<DismissalStatus?> selectedStatus = Rx<DismissalStatus?>(null);
  Rx<DateTime> selectedDate = DateTime.now().obs;
  RxBool isRefreshing = false.obs;
  RxBool isLoadingMore = false.obs;

  // Get dismissals with optional filtering
  void getDismissals({
    DismissalStatus? status,
    DateTime? date,
    int page = 1,
    bool showLoading = true,
  }) {
    callApi(() async {
      if (showLoading && page == 1) {
        setPageStatus(PageStatus.loading);
      }

      this.page.value = page;
      selectedStatus.value = status;
      if (date != null) {
        selectedDate.value = date;
      }

      // Format the date for API request
      String formattedDate =
          DateFormat('yyyy-MM-dd').format(selectedDate.value);

      var response = await apiProvider.getStudentDismissals(
        status: status?.value,
        date: formattedDate,
        page: page,
      );

      if (page == 1) {
        dismissals.value = response.dismissals;
        lastPage.value = response.lastPage;

        if (dismissals.isEmpty) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      } else {
        dismissals.addAll(response.dismissals);
      }

      dismissals.refresh();
      isRefreshing.value = false;
      isLoadingMore.value = false;
    }, withLoading: showLoading);
  }

  // Get a specific dismissal by ID
  Future<StudentDismissal?> getDismissal(int id) async {
    try {
      setPageStatus(PageStatus.loading);
      var response = await apiProvider.getStudentDismissal(id);
      setPageStatus(PageStatus.loaded);
      return response.data;
    } catch (e) {
      setPageStatus(PageStatus.unknownError);
      HelperFunctionExtension.showErrorToast(e.toString());
      return null;
    }
  }

  // Create a new dismissal request
  Future<bool> createDismissal({
    required int studentId,
    required String dismissalDate,
    required String dismissalTime,
    String? pickupPerson,
    String? pickupPersonPhone,
    String? pickupPersonRelationship,
    String? notes,
  }) async {
    try {
      // setPageStatus(PageStatus.loading);

      final body = {
        'student_id': studentId,
        'pickup_person': pickupPerson,
        'pickup_person_phone': pickupPersonPhone,
        'pickup_person_relationship': pickupPersonRelationship,
        'notes': notes,
      };

      final response = await apiProvider.createStudentDismissal(body);
      // setPageStatus(PageStatus.loaded);

      final message =
          response.message ?? 'Dismissal request created successfully'.tr;
      Get.back();
      HelperFunction.showSuccessMessage(message);
      return true;
    } catch (e) {
      setPageStatus(PageStatus.unknownError);
      HelperFunction.showErrorMessage(e.toString());
      return false;
    }
  }

  // Update an existing dismissal request
  Future<bool> updateDismissal({
    required int id,
    required String dismissalDate,
    required String dismissalTime,
    String? pickupPerson,
    String? pickupPersonPhone,
    String? pickupPersonRelationship,
    String? notes,
  }) async {
    try {
      setPageStatus(PageStatus.loading);

      final body = {
        'pickup_person': pickupPerson,
        'pickup_person_phone': pickupPersonPhone,
        'pickup_person_relationship': pickupPersonRelationship,
        'notes': notes,
      };

      final response = await apiProvider.updateStudentDismissal(id, body);
      setPageStatus(PageStatus.loaded);

      final message =
          response.message ?? 'Dismissal request updated successfully'.tr;
      HelperFunctionExtension.showSuccessToast(message);
      return true;
    } catch (e) {
      setPageStatus(PageStatus.unknownError);
      HelperFunctionExtension.showErrorToast(e.toString());
      return false;
    }
  }

  // Cancel a dismissal request
  Future<bool> cancelDismissal(int id) async {
    try {
      setPageStatus(PageStatus.loading);
      final response = await apiProvider.cancelStudentDismissal(id);
      setPageStatus(PageStatus.loaded);

      // Use the response message if available
      final message =
          response.message ?? 'Dismissal request cancelled successfully'.tr;
      HelperFunctionExtension.showSuccessToast(message);
      return true;
    } catch (e) {
      setPageStatus(PageStatus.unknownError);
      HelperFunctionExtension.showErrorToast(e.toString());
      return false;
    }
  }

  // Confirm delivery of a student
  Future<bool> confirmDelivery(int id) async {
    try {
      setPageStatus(PageStatus.loading);
      final response = await apiProvider.confirmStudentDismissalDelivery(id);
      setPageStatus(PageStatus.loaded);

      // Use the response message if available
      final message = response.message ?? 'Delivery confirmed successfully'.tr;
      HelperFunctionExtension.showSuccessToast(message);
      return true;
    } catch (e) {
      setPageStatus(PageStatus.unknownError);
      HelperFunctionExtension.showErrorToast(e.toString());
      return false;
    }
  }

  // Update dismissal status (for admin_assistant role)
  Future<bool> updateDismissalStatus(int id, DismissalStatus status) async {
    try {
      setPageStatus(PageStatus.loading);

      final body = {
        'status': status.value,
      };

      final response = await apiProvider.updateStudentDismissal(id, body);
      setPageStatus(PageStatus.loaded);

      // Use the response message if available
      String successMessage;
      if (status == DismissalStatus.getReady) {
        successMessage = 'Student is ready for dismissal'.tr;
      } else if (status == DismissalStatus.delivered) {
        successMessage = 'Student has been delivered successfully'.tr;
      } else {
        successMessage = 'Dismissal status updated successfully'.tr;
      }

      final message = response.message ?? successMessage;
      HelperFunctionExtension.showSuccessToast(message);
      return true;
    } catch (e) {
      setPageStatus(PageStatus.unknownError);
      HelperFunctionExtension.showErrorToast(e.toString());
      return false;
    }
  }

  // Load more dismissals (pagination)
  void loadMore() {
    // Prevent multiple simultaneous loadMore calls
    if (isLoadingMore.value) return;

    if (page.value < lastPage.value) {
      isLoadingMore.value = true;
      getDismissals(
        status: selectedStatus.value,
        page: page.value + 1,
        showLoading: false,
      );

      // Reset the loading flag after a delay to prevent rapid consecutive calls
      Future.delayed(const Duration(milliseconds: 500), () {
        isLoadingMore.value = false;
      });
    }
  }

  // Refresh dismissals
  @override
  void refresh() {
    isRefreshing.value = true;
    getDismissals(
      status: selectedStatus.value,
      page: 1,
      showLoading: false,
    );
  }

  // Check if more dismissals can be loaded
  bool canLoadMore() {
    return page.value < lastPage.value;
  }
}
