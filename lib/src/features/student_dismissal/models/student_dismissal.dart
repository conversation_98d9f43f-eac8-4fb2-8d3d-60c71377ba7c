import '../../../core/data/models/user.dart';
import 'dismissal_status.dart';

/// Model representing a student dismissal request
class StudentDismissal {
  final int? id;
  final User? student;
  final User? guardian;
  final String? pickupPerson;
  final String? pickupPersonPhone;
  final String? pickupPersonRelationship;
  final String? dismissalDate;
  final String? dismissalTime;
  final String? formattedDismissalTime;
  final DismissalStatus status;
  final String? notes;
  final User? getReadyBy;
  final User? deliveredBy;
  final String? getReadyAt;
  final String? deliveredAt;
  final bool parentConfirmed;
  final String? parentConfirmedAt;

  StudentDismissal({
    this.id,
    this.student,
    this.guardian,
    this.pickupPerson,
    this.pickupPersonPhone,
    this.pickupPersonRelationship,
    this.dismissalDate,
    this.dismissalTime,
    this.formattedDismissalTime,
    this.status = DismissalStatus.pending,
    this.notes,
    this.getReadyBy,
    this.deliveredBy,
    this.getReadyAt,
    this.deliveredAt,
    this.parentConfirmed = false,
    this.parentConfirmedAt,
  });

  /// Factory constructor to create a StudentDismissal from JSON
  factory StudentDismissal.fromJson(Map<String, dynamic> json) {
    return StudentDismissal(
      id: json['id'],
      student: json['student'] != null ? User.fromJson(json['student']) : null,
      guardian:
          json['guardian'] != null ? User.fromJson(json['guardian']) : null,
      pickupPerson: json['pickup_person'],
      pickupPersonPhone: json['pickup_person_phone'],
      pickupPersonRelationship: json['pickup_person_relationship'],
      dismissalDate: json['dismissal_date'],
      dismissalTime: json['dismissal_time'],
      formattedDismissalTime: json['formatted_dismissal_time'],
      status: DismissalStatus.fromString(json['status'] ?? 'pending'),
      notes: json['notes'],
      getReadyBy: json['get_ready_by'] != null
          ? User.fromJson(json['get_ready_by'])
          : null,
      deliveredBy: json['delivered_by'] != null
          ? User.fromJson(json['delivered_by'])
          : null,
      getReadyAt: json['get_ready_at'],
      deliveredAt: json['delivered_at'],
      parentConfirmed: json['parent_confirmed'] ?? false,
      parentConfirmedAt: json['parent_confirmed_at'],
    );
  }

  /// Convert the model to JSON
  ///
  /// This method is used when sending data to the API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'student_id': student?.id,
      'dismissal_date': dismissalDate,
      'dismissal_time': dismissalTime,
      'pickup_person': pickupPerson,
      'pickup_person_phone': pickupPersonPhone,
      'pickup_person_relationship': pickupPersonRelationship,
      'notes': notes,
      'status': status.toJson(), // Use the toJson method from DismissalStatus
    };
  }

  /// Create a copy of this dismissal with modified fields
  StudentDismissal copyWith({
    int? id,
    User? student,
    User? guardian,
    String? pickupPerson,
    String? pickupPersonPhone,
    String? pickupPersonRelationship,
    String? dismissalDate,
    String? dismissalTime,
    String? formattedDismissalTime,
    DismissalStatus? status,
    String? notes,
    User? getReadyBy,
    User? deliveredBy,
    String? getReadyAt,
    String? deliveredAt,
    bool? parentConfirmed,
    String? parentConfirmedAt,
  }) {
    return StudentDismissal(
      id: id ?? this.id,
      student: student ?? this.student,
      guardian: guardian ?? this.guardian,
      pickupPerson: pickupPerson ?? this.pickupPerson,
      pickupPersonPhone: pickupPersonPhone ?? this.pickupPersonPhone,
      pickupPersonRelationship:
          pickupPersonRelationship ?? this.pickupPersonRelationship,
      dismissalDate: dismissalDate ?? this.dismissalDate,
      dismissalTime: dismissalTime ?? this.dismissalTime,
      formattedDismissalTime:
          formattedDismissalTime ?? this.formattedDismissalTime,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      getReadyBy: getReadyBy ?? this.getReadyBy,
      deliveredBy: deliveredBy ?? this.deliveredBy,
      getReadyAt: getReadyAt ?? this.getReadyAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      parentConfirmed: parentConfirmed ?? this.parentConfirmed,
      parentConfirmedAt: parentConfirmedAt ?? this.parentConfirmedAt,
    );
  }
}
