import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/app_color.dart';

/// Enum representing the status of a student dismissal request
///
/// This enum matches the backend's DismissalStatus enum and provides
/// methods for serialization, deserialization, and UI presentation.
enum DismissalStatus {
  pending('pending'),
  getReady('get_ready'),
  delivered('delivered'),
  cancelled('cancelled');

  final String value;
  const DismissalStatus(this.value);

  /// Factory constructor to create a DismissalStatus from a string value
  ///
  /// Returns [DismissalStatus.pending] if the value doesn't match any status
  factory DismissalStatus.fromString(String value) {
    return DismissalStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => DismissalStatus.pending,
    );
  }

  /// Get the localized label for the status
  ///
  /// Uses the Get package's translation system
  String getLabel() {
    switch (this) {
      case DismissalStatus.pending:
        return 'Pending'.tr;
      case DismissalStatus.getReady:
        return 'Get Ready'.tr;
      case DismissalStatus.delivered:
        return 'Delivered'.tr;
      case DismissalStatus.cancelled:
        return 'Cancelled'.tr;
    }
  }

  /// Get the color associated with the status
  Color getColor() {
    switch (this) {
      case DismissalStatus.pending:
        return AppColor.orangeColor; // Warning color
      case DismissalStatus.getReady:
        return AppColor.primaryColor; // Info color
      case DismissalStatus.delivered:
        return AppColor.greenColor; // Success color
      case DismissalStatus.cancelled:
        return AppColor.redColor;
    }
  }

  /// Get the icon associated with the status
  IconData getIcon() {
    switch (this) {
      case DismissalStatus.pending:
        return Icons.schedule;
      case DismissalStatus.getReady:
        return Icons.check_circle_outline;
      case DismissalStatus.delivered:
        return Icons.check_circle;
      case DismissalStatus.cancelled:
        return Icons.cancel_outlined;
    }
  }

  /// Convert the enum to a string for API requests
  ///
  /// This is used when sending the status to the backend
  String toJson() => value;

  /// String representation of the enum
  @override
  String toString() => value;
}
