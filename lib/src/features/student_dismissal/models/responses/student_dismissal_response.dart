import '../student_dismissal.dart';

/// Response model for a single student dismissal
class StudentDismissalResponse {
  final String? message;
  final StudentDismissal? data;

  StudentDismissalResponse({
    this.message,
    this.data,
  });

  factory StudentDismissalResponse.fromJson(Map<String, dynamic> json) {
    return StudentDismissalResponse(
      message: json['message'],
      data: json['data'] != null ? StudentDismissal.fromJson(json['data']) : null,
    );
  }
}

/// Response model for paginated student dismissals
class PaginatedStudentDismissalResponse {
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int itemsCount;
  final List<StudentDismissal> dismissals;

  PaginatedStudentDismissalResponse({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.itemsCount,
    required this.dismissals,
  });

  factory PaginatedStudentDismissalResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedStudentDismissalResponse(
      currentPage: json['current_page'] ?? 1,
      lastPage: json['last_page'] ?? 1,
      perPage: json['per_page'] ?? 10,
      itemsCount: json['items_count'] ?? 0,
      dismissals: json['data'] != null
          ? List<StudentDismissal>.from(
              json['data'].map((x) => StudentDismissal.fromJson(x)))
          : [],
    );
  }
}
