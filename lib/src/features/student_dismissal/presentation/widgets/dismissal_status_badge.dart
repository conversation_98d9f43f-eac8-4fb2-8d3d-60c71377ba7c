import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../models/dismissal_status.dart';

class DismissalStatusBadge extends StatelessWidget {
  final DismissalStatus status;
  final double? fontSize;

  const DismissalStatusBadge({
    super.key,
    required this.status,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: status.getColor().withAlpha(38),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: status.getColor().withAlpha(38),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            status.getIcon(),
            size: 14.r,
            color: status.getColor(),
          ),
          SizedBox(width: 4.w),
          Text(
            status.getLabel().tr,
            style: TextStyle(
              color: status.getColor(),
              fontSize: fontSize ?? 12.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
