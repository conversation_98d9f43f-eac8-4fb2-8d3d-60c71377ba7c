import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';

import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/student_dismissal_controller.dart';
import '../../models/student_dismissal.dart';
import '../../models/dismissal_status.dart';
import 'dismissal_status_badge.dart';

class DismissalCard extends StatelessWidget {
  final StudentDismissal dismissal;
  final bool showActions;

  const DismissalCard({
    super.key,
    required this.dismissal,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    final currentUserRole = AuthService.instance.user.value?.role;
    final isGuardian = currentUserRole == 'guardian';
    final isAdminAssistant = currentUserRole == Roles.AdminAssistant;

    final canCancelDismissal = isGuardian &&
        (dismissal.status == DismissalStatus.pending ||
            dismissal.status == DismissalStatus.getReady);

    final canChangeStatus = isAdminAssistant &&
        (dismissal.status == DismissalStatus.pending ||
            dismissal.status == DismissalStatus.getReady);

    final enableSlidable = showActions &&
        dismissal.status != DismissalStatus.delivered &&
        dismissal.status != DismissalStatus.cancelled &&
        (canCancelDismissal || canChangeStatus);

    return Slidable(
      enabled: enableSlidable,
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          // Guardian cancel action
          if (canCancelDismissal)
            SlidableAction(
              onPressed: (_) {
                _showCancelConfirmation(context);
              },
              backgroundColor: AppColor.redColor,
              foregroundColor: Colors.white,
              icon: Icons.cancel,
              label: 'Cancel'.tr,
              borderRadius:
                  BorderRadius.horizontal(right: Radius.circular(12.r)),
            ),

          // Admin assistant status change actions
          if (canChangeStatus && dismissal.status == DismissalStatus.pending)
            SlidableAction(
              onPressed: (_) {
                _updateDismissalStatus(DismissalStatus.getReady);
              },
              backgroundColor: AppColor.primaryColor,
              foregroundColor: Colors.white,
              icon: Icons.check_circle_outline,
              label: 'Mark as Ready'.tr,
              borderRadius:
                  BorderRadius.horizontal(right: Radius.circular(12.r)),
            ),

          if (canChangeStatus && dismissal.status == DismissalStatus.getReady)
            SlidableAction(
              onPressed: (_) {
                _updateDismissalStatus(DismissalStatus.delivered);
              },
              backgroundColor: AppColor.greenColor,
              foregroundColor: Colors.white,
              icon: Icons.check_circle,
              label: 'Mark as Delivered'.tr,
              borderRadius:
                  BorderRadius.horizontal(right: Radius.circular(12.r)),
            ),
        ],
      ),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(12.r),
          child: InkWell(
            borderRadius: BorderRadius.circular(12.r),
            onTap: () {
              Get.toNamed(
                AppRouter.dismissalDetailsScreen,
                arguments: {'dismissalId': dismissal.id},
              );
            },
            child: Padding(
              padding: EdgeInsets.all(12.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildLeadingWidget(),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    dismissal.student?.name ?? '',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColor.primaryColor,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                SizedBox(width: 8.w),
                                DismissalStatusBadge(status: dismissal.status),
                              ],
                            ),
                            if (dismissal.student?.sectionName != null &&
                                dismissal.student!.sectionName!.isNotEmpty) ...[
                              SizedBox(height: 4.h),
                              Row(
                                children: [
                                  Icon(
                                    Icons.school_outlined,
                                    size: 14.r,
                                    color: AppColor.greyColor3,
                                  ),
                                  SizedBox(width: 6.w),
                                  Expanded(
                                    child: Text(
                                      dismissal.student!.sectionName!,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: AppColor.greyColor3,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            SizedBox(
                                height:
                                    dismissal.student?.sectionName != null &&
                                            dismissal.student!.sectionName!
                                                .isNotEmpty
                                        ? 6.h
                                        : 4.h),
                            _buildPickupInfo(),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),
                  _buildFooterInfo(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLeadingWidget() {
    final hasImage = dismissal.student?.image != null &&
        dismissal.student!.image!.isNotEmpty;

    return Container(
      width: 56.w,
      height: 56.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border:
            Border.all(color: AppColor.primaryColor.withAlpha(50), width: 2),
        color: AppColor.primaryColor.withAlpha(15),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10.r),
        child: hasImage
            ? CustomCachedNetworkImage(
                imageUrl: dismissal.student!.image!,
                fit: BoxFit.cover,
              )
            : Center(
                child: Icon(
                  Icons.person,
                  color: AppColor.primaryColor,
                  size: 32.r,
                ),
              ),
      ),
    );
  }

  Widget _buildPickupInfo() {
    final pickupPerson = dismissal.pickupPerson?.isNotEmpty == true
        ? dismissal.pickupPerson
        : 'Guardian'.tr;

    return Row(
      children: [
        Icon(
          Icons.person_outline,
          size: 16.r,
          color: AppColor.greyColor3,
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: Text(
            '${'Pickup'.tr}: $pickupPerson',
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColor.greyColor3,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildFooterInfo() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
      decoration: BoxDecoration(
        color: AppColor.scaffoldColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Date info
          Row(
            children: [
              Icon(
                Icons.calendar_today_outlined,
                size: 16.r,
                color: AppColor.greyColor3,
              ),
              SizedBox(width: 6.w),
              Text(
                dismissal.dismissalDate ?? '',
                style: TextStyle(
                  fontSize: 13.sp,
                  color: AppColor.greyColor3,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Time info
          Row(
            children: [
              Icon(
                Icons.access_time_outlined,
                size: 16.r,
                color: AppColor.greyColor3,
              ),
              SizedBox(width: 6.w),
              Text(
                dismissal.formattedDismissalTime ??
                    dismissal.dismissalTime ??
                    '',
                style: TextStyle(
                  fontSize: 13.sp,
                  color: AppColor.greyColor3,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showCancelConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Text(
          'Cancel Dismissal'.tr,
          style: TextStyle(
            color: AppColor.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to cancel this dismissal request?'.tr,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.greyColor5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: AppColor.greyColor3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: Text(
              'No'.tr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _cancelDismissal();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.redColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: Text(
              'Yes, Cancel'.tr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _cancelDismissal() async {
    if (dismissal.id != null) {
      final success = await StudentDismissalController.instance
          .cancelDismissal(dismissal.id!);
      if (success) {
        StudentDismissalController.instance.refresh();
      }
    }
  }

  void _updateDismissalStatus(DismissalStatus newStatus) async {
    if (dismissal.id != null) {
      final success = await StudentDismissalController.instance
          .updateDismissalStatus(dismissal.id!, newStatus);
      if (success) {
        StudentDismissalController.instance.refresh();
      }
    }
  }
}
