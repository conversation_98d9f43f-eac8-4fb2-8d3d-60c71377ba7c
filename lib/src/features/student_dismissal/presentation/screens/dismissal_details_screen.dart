import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/errors/retry_error_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/student_dismissal_controller.dart';
import '../../models/dismissal_status.dart';
import '../../models/student_dismissal.dart';

class DismissalDetailsScreen extends StatefulWidget {
  const DismissalDetailsScreen({super.key});

  @override
  State<DismissalDetailsScreen> createState() => _DismissalDetailsScreenState();
}

class _DismissalDetailsScreenState extends State<DismissalDetailsScreen> {
  final controller = Get.find<StudentDismissalController>();
  late int dismissalId;
  StudentDismissal? dismissal;
  bool isLoading = true;
  bool hasError = false;

  @override
  void initState() {
    super.initState();
    dismissalId = Get.arguments['dismissalId'];
  }

  Future<void> _loadDismissal() async {
    Get.log(
      'Loading dismissal details for ID: $dismissalId',
    );

    isLoading = true;
    hasError = false;

    try {
      final result = await controller.getDismissal(dismissalId);
      if (mounted) {
        setState(() {
          dismissal = result;
          isLoading = false;
        });
      }
    } catch (e) {
      Get.log(e.toString());
      isLoading = false;
      hasError = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isLoading) {
        _loadDismissal();
      }
    });

    return LayoutScreen(
      title: 'Dismissal Details'.tr,
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : hasError
              ? RetryErrorWidget(
                  onRetry: _loadDismissal,
                  message: 'Failed to load dismissal details'.tr,
                )
              : dismissal == null
                  ? Center(
                      child: Text(
                        'Dismissal not found'.tr,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColor.greyColor3,
                        ),
                      ),
                    )
                  : _buildDismissalDetails(),
    );
  }

  Widget _buildDismissalDetails() {
    final currentUserRole = AuthService.instance.user.value?.role;
    final isGuardian = currentUserRole == 'guardian';
    final isAdminAssistant = currentUserRole == Roles.AdminAssistant;

    final canConfirmDelivery = isGuardian &&
        dismissal!.status == DismissalStatus.delivered &&
        !dismissal!.parentConfirmed;

    final canCancelDismissal = isGuardian &&
        (dismissal!.status == DismissalStatus.pending ||
            dismissal!.status == DismissalStatus.getReady);

    final canChangeStatus = isAdminAssistant &&
        (dismissal!.status == DismissalStatus.pending ||
            dismissal!.status == DismissalStatus.getReady);

    final showBottomActions =
        canConfirmDelivery || canCancelDismissal || canChangeStatus;

    return Stack(
      children: [
        // Main content
        SingleChildScrollView(
          padding: EdgeInsets.only(
              left: 16.r,
              right: 16.r,
              top: 16.r,
              bottom: showBottomActions ? 90.h : 16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusHeader(),
              SizedBox(height: 20.h),
              _buildStudentInfo(),
              SizedBox(height: 20.h),
              _buildDismissalInfo(),
              SizedBox(height: 20.h),
              _buildPickupInfo(),
              if (dismissal!.notes?.isNotEmpty == true) ...[
                SizedBox(height: 20.h),
                _buildNotes(),
              ],
            ],
          ),
        ),

        // Action buttons at the bottom
        if (showBottomActions)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: _buildActionButtons(
                canConfirmDelivery: canConfirmDelivery,
                canCancelDismissal: canCancelDismissal,
                canChangeStatus: canChangeStatus,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons({
    required bool canConfirmDelivery,
    required bool canCancelDismissal,
    required bool canChangeStatus,
  }) {
    if (canConfirmDelivery) {
      return _buildConfirmDeliveryButton();
    } else if (canCancelDismissal) {
      return _buildCancelButton();
    } else if (canChangeStatus) {
      return _buildStatusChangeButtons();
    }

    return const SizedBox.shrink();
  }

  Widget _buildStatusChangeButtons() {
    if (dismissal!.status == DismissalStatus.pending) {
      // Show "Mark as Ready" button
      return SizedBox(
        width: double.infinity,
        height: 50.h,
        child: ElevatedButton.icon(
          onPressed: () =>
              _showStatusChangeConfirmation(DismissalStatus.getReady),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColor.primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            elevation: 2,
          ),
          icon: Icon(
            Icons.check_circle_outline,
            size: 22.r,
          ),
          label: Text(
            'Mark as Ready'.tr,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    } else if (dismissal!.status == DismissalStatus.getReady) {
      // Show "Mark as Delivered" button
      return SizedBox(
        width: double.infinity,
        height: 50.h,
        child: ElevatedButton.icon(
          onPressed: () =>
              _showStatusChangeConfirmation(DismissalStatus.delivered),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColor.greenColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            elevation: 2,
          ),
          icon: Icon(
            Icons.check_circle,
            size: 22.r,
          ),
          label: Text(
            'Mark as Delivered'.tr,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildStatusHeader() {
    final statusColor = dismissal!.status.getColor();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Status header with gradient background
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.r),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  statusColor.withAlpha(30),
                  statusColor.withAlpha(10),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
            ),
            child: Column(
              children: [
                // Status icon
                Icon(
                  dismissal!.status.getIcon(),
                  size: 40.r,
                  color: statusColor,
                ),
                SizedBox(height: 8.h),

                // Status text
                Text(
                  dismissal!.status.getLabel().tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
              ],
            ),
          ),

          // Parent confirmation status (if delivered)
          if (dismissal!.status == DismissalStatus.delivered)
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: dismissal!.parentConfirmed
                    ? AppColor.greenColor.withAlpha(15)
                    : AppColor.orangeColor.withAlpha(15),
                borderRadius:
                    BorderRadius.vertical(bottom: Radius.circular(16.r)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    dismissal!.parentConfirmed
                        ? Icons.check_circle
                        : Icons.pending_actions,
                    size: 20.r,
                    color: dismissal!.parentConfirmed
                        ? AppColor.greenColor
                        : AppColor.orangeColor,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    dismissal!.parentConfirmed
                        ? 'Parent confirmed delivery'.tr
                        : 'Waiting for parent confirmation'.tr,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: dismissal!.parentConfirmed
                          ? AppColor.greenColor
                          : AppColor.orangeColor,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStudentInfo() {
    final hasImage = dismissal!.student?.image != null &&
        dismissal!.student!.image!.isNotEmpty;

    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Icons.person_outline,
                size: 18.r,
                color: AppColor.primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                'Student Information'.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Student info with avatar
          Row(
            children: [
              // Student avatar
              Container(
                width: 70.w,
                height: 70.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(
                    color: AppColor.primaryColor.withAlpha(50),
                    width: 2,
                  ),
                  color: AppColor.primaryColor.withAlpha(15),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(14.r),
                  child: hasImage
                      ? CustomCachedNetworkImage(
                          imageUrl: dismissal!.student!.image!,
                          fit: BoxFit.cover,
                        )
                      : Center(
                          child: Icon(
                            Icons.person,
                            color: AppColor.primaryColor,
                            size: 40.r,
                          ),
                        ),
                ),
              ),

              SizedBox(width: 16.w),

              // Student details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      dismissal!.student?.name ?? '',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColor.blackColor,
                      ),
                    ),
                    SizedBox(height: 6.h),
                    Row(
                      children: [
                        Icon(
                          Icons.school_outlined,
                          size: 16.r,
                          color: AppColor.greyColor3,
                        ),
                        SizedBox(width: 6.w),
                        Expanded(
                          child: Text(
                            dismissal!.student?.sectionName ??
                                'No section assigned'.tr,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColor.greyColor3,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDismissalInfo() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Icons.event_note_outlined,
                size: 18.r,
                color: AppColor.primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                'Dismissal Information'.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Date and time cards
          Row(
            children: [
              // Date card
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: AppColor.scaffoldColor,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 24.r,
                        color: AppColor.primaryColor,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'Date'.tr,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColor.greyColor3,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        dismissal!.dismissalDate ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColor.blackColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(width: 12.w),

              // Time card
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: AppColor.scaffoldColor,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.access_time_outlined,
                        size: 24.r,
                        color: AppColor.primaryColor,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'Time'.tr,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColor.greyColor3,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        dismissal!.formattedDismissalTime ??
                            dismissal!.dismissalTime ??
                            '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColor.blackColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPickupInfo() {
    final hasPhone = dismissal!.pickupPersonPhone?.isNotEmpty == true;
    final hasRelationship =
        dismissal!.pickupPersonRelationship?.isNotEmpty == true;
    final pickupPerson = dismissal!.pickupPerson?.isNotEmpty == true
        ? dismissal!.pickupPerson!
        : 'Guardian'.tr;

    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Icons.directions_walk_outlined,
                size: 18.r,
                color: AppColor.primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                'Pickup Information'.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Pickup person card
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: AppColor.scaffoldColor,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              children: [
                // Pickup person
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(10.r),
                      decoration: BoxDecoration(
                        color: AppColor.primaryColor.withAlpha(20),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.person_outline,
                        size: 24.r,
                        color: AppColor.primaryColor,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Pickup Person'.tr,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColor.greyColor3,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            pickupPerson,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColor.blackColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Phone number
                if (hasPhone) ...[
                  SizedBox(height: 16.h),
                  Divider(height: 1, color: AppColor.greyColor.withAlpha(100)),
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(10.r),
                        decoration: BoxDecoration(
                          color: AppColor.primaryColor.withAlpha(20),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.phone_outlined,
                          size: 24.r,
                          color: AppColor.primaryColor,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Phone'.tr,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppColor.greyColor3,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              dismissal!.pickupPersonPhone!,
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColor.blackColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],

                // Relationship
                if (hasRelationship) ...[
                  SizedBox(height: 16.h),
                  Divider(height: 1, color: AppColor.greyColor.withAlpha(100)),
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(10.r),
                        decoration: BoxDecoration(
                          color: AppColor.primaryColor.withAlpha(20),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.family_restroom_outlined,
                          size: 24.r,
                          color: AppColor.primaryColor,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Relationship'.tr,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppColor.greyColor3,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              dismissal!.pickupPersonRelationship!,
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColor.blackColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotes() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Icon(
                Icons.note_alt_outlined,
                size: 18.r,
                color: AppColor.primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                'Notes'.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Notes content
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: AppColor.scaffoldColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: AppColor.greyColor.withAlpha(50),
                width: 1,
              ),
            ),
            child: Text(
              dismissal!.notes ?? '',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.greyColor5,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmDeliveryButton() {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton.icon(
        onPressed: _confirmDelivery,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColor.greenColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 2,
        ),
        icon: Icon(
          Icons.check_circle_outline,
          size: 22.r,
        ),
        label: Text(
          'Confirm Delivery'.tr,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildCancelButton() {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton.icon(
        onPressed: _cancelDismissal,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColor.redColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 2,
        ),
        icon: Icon(
          Icons.cancel_outlined,
          size: 22.r,
        ),
        label: Text(
          'Cancel Dismissal'.tr,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _confirmDelivery() async {
    if (dismissal?.id != null) {
      final success = await controller.confirmDelivery(dismissal!.id!);
      if (success) {
        _loadDismissal();
        controller.refresh();
      }
    }
  }

  void _cancelDismissal() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Text(
          'Cancel Dismissal'.tr,
          style: TextStyle(
            color: AppColor.primaryColor,
            fontWeight: FontWeight.bold,
            fontSize: 18.sp,
          ),
        ),
        content: Text(
          'Are you sure you want to cancel this dismissal request?'.tr,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.greyColor5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: AppColor.greyColor3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: Text(
              'No'.tr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performCancel();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.redColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: Text(
              'Yes, Cancel'.tr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _performCancel() async {
    if (dismissal?.id != null) {
      final success = await controller.cancelDismissal(dismissal!.id!);
      if (success) {
        _loadDismissal();
        controller.refresh();
      }
    }
  }

  void _showStatusChangeConfirmation(DismissalStatus newStatus) {
    String title;
    String message;
    String confirmButtonText;
    Color confirmButtonColor;

    if (newStatus == DismissalStatus.getReady) {
      title = 'Mark as Ready'.tr;
      message =
          'Are you sure you want to mark this student as ready for dismissal?'
              .tr;
      confirmButtonText = 'Yes, Mark as Ready'.tr;
      confirmButtonColor = AppColor.primaryColor;
    } else if (newStatus == DismissalStatus.delivered) {
      title = 'Mark as Delivered'.tr;
      message = 'Are you sure you want to mark this student as delivered?'.tr;
      confirmButtonText = 'Yes, Mark as Delivered'.tr;
      confirmButtonColor = AppColor.greenColor;
    } else {
      return; // Unsupported status change
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: AppColor.primaryColor,
            fontWeight: FontWeight.bold,
            fontSize: 18.sp,
          ),
        ),
        content: Text(
          message,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColor.greyColor5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: AppColor.greyColor3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: Text(
              'Cancel'.tr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateDismissalStatus(newStatus);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmButtonColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: Text(
              confirmButtonText,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _updateDismissalStatus(DismissalStatus newStatus) async {
    if (dismissal?.id != null) {
      final success =
          await controller.updateDismissalStatus(dismissal!.id!, newStatus);
      if (success) {
        _loadDismissal();
        controller.refresh();
      }
    }
  }
}
