import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/student_dismissal_controller.dart';
import '../../models/dismissal_status.dart';
import '../widgets/dismissal_card.dart';

class DismissalsScreen extends StatefulWidget {
  const DismissalsScreen({super.key});

  @override
  State<DismissalsScreen> createState() => _DismissalsScreenState();
}

class _DismissalsScreenState extends State<DismissalsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final controller = Get.put(StudentDismissalController());

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _tabController.addListener(_handleTabChange);

    // Initial data load
    controller.getDismissals();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      return;
    }

    DismissalStatus? status;
    switch (_tabController.index) {
      case 0: // All
        status = null;
        break;
      case 1: // Pending
        status = DismissalStatus.pending;
        break;
      case 2: // Get Ready
        status = DismissalStatus.getReady;
        break;
      case 3: // Delivered
        status = DismissalStatus.delivered;
        break;
      case 4: // Cancelled
        status = DismissalStatus.cancelled;
        break;
    }

    controller.getDismissals(status: status);
  }

  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: 'Student Dismissal'.tr,
      controller: controller,
      floatingActionButton: AuthService.instance.user.value?.role == 'guardian'
          ? FloatingActionButton(
              onPressed: () => Get.toNamed(AppRouter.createDismissalScreen),
              child: const Icon(Icons.add),
            )
          : null,
      emptyWidegt: EmptyWidget(
        icon: AppAssets.noContent,
        iconColor: Get.theme.primaryColor,
        message: 'No dismissal requests found'.tr,
      ),
      bodyHeader: Column(
        children: [
          _buildDateSelector(),
          _buildTabBar(),
        ],
      ),
      body: Obx(() {
        return RefreshIndicator(
          onRefresh: () async {
            controller.refresh();
            return Future.delayed(const Duration(milliseconds: 500));
          },
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(vertical: 16),
            itemCount: controller.dismissals.length + 1,
            itemBuilder: (context, index) {
              if (index == controller.dismissals.length) {
                if (controller.canLoadMore()) {
                  // Schedule loadMore() to be called after the current build is complete
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    controller.loadMore();
                  });
                  return SizedBox(
                    height: 50,
                    child: Center(
                      child: Text('Loading more...'.tr),
                    ),
                  );
                } else {
                  return const SizedBox.shrink();
                }
              }

              return DismissalCard(
                dismissal: controller.dismissals[index],
              );
            },
            separatorBuilder: (context, index) => const SizedBox(height: 8),
          ),
        );
      }),
    );
  }

  Widget _buildDateSelector() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Obx(() {
        final dateFormat = DateFormat.yMMMd();
        final selectedDate = controller.selectedDate.value;

        return InkWell(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColor.primaryColor.withAlpha(128)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  dateFormat.format(selectedDate),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColor.primaryColor,
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: AppColor.primaryColor,
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      labelColor: AppColor.primaryColor,
      unselectedLabelColor: AppColor.greyColor3,
      indicatorColor: AppColor.primaryColor,
      tabAlignment: TabAlignment.start,
      tabs: [
        Tab(text: 'All'.tr),
        Tab(text: DismissalStatus.pending.getLabel()),
        Tab(text: DismissalStatus.getReady.getLabel()),
        Tab(text: DismissalStatus.delivered.getLabel()),
        Tab(text: DismissalStatus.cancelled.getLabel()),
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.selectedDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && picked != controller.selectedDate.value) {
      controller.getDismissals(date: picked);
    }
  }
}
