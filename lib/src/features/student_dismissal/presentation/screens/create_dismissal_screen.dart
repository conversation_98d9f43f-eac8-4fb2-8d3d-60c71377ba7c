import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../../home/<USER>/son_controller.dart';
import '../../controller/student_dismissal_controller.dart';

class CreateDismissalScreen extends StatefulWidget {
  const CreateDismissalScreen({super.key});

  @override
  State<CreateDismissalScreen> createState() => _CreateDismissalScreenState();
}

class _CreateDismissalScreenState extends State<CreateDismissalScreen> {
  final _formKey = GlobalKey<FormState>();
  final _dismissalController = Get.find<StudentDismissalController>();
  final _sonController = Get.find<SonController>();

  int? _selectedStudentId;
  final _dateController = TextEditingController();
  final _timeController = TextEditingController();
  final _pickupPersonController = TextEditingController();
  final _pickupPhoneController = TextEditingController();
  final _relationshipController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();

  // Loading state
  final RxBool _isLoading = false.obs;

  // Form validation
  final RegExp _phoneRegex = RegExp(r'^[0-9]{9,15}$');

  @override
  void initState() {
    super.initState();
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate);
    _timeController.text = _formatTimeOfDay(_selectedTime);

    // If there's only one son, select it automatically
    if (_sonController.sons.length == 1) {
      _selectedStudentId = _sonController.sons.first.id;
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    _timeController.dispose();
    _pickupPersonController.dispose();
    _pickupPhoneController.dispose();
    _relationshipController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: 'Create Dismissal Request'.tr,
      body: Obx(() => Stack(
            children: [
              SingleChildScrollView(
                padding: EdgeInsets.all(16.r),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStudentSelector(),
                      SizedBox(height: 20.h),
                      _buildPickupPersonFields(),
                      SizedBox(height: 20.h),
                      _buildNotesField(),
                      SizedBox(height: 30.h),
                      CustomButton(
                        text: 'Submit Request'.tr,
                        onPressed:
                            _isLoading.value ? () {} : _showConfirmationDialog,
                        width: double.infinity,
                        height: 50.h,
                      ),
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),

              // Loading overlay
              if (_isLoading.value)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: Center(
                    child: Container(
                      padding: EdgeInsets.all(20.r),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            color: AppColor.primaryColor,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'Creating dismissal request...'.tr,
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          )),
    );
  }

  Widget _buildStudentSelector() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                Icons.person_outline,
                size: 18.r,
                color: AppColor.primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                'Student Information'.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Student selector
          Obx(() {
            return DropdownButtonFormField<int>(
              value: _selectedStudentId,
              decoration: InputDecoration(
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(color: AppColor.greyColor2),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(color: AppColor.greyColor2),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(color: AppColor.primaryColor),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(color: AppColor.redColor),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(color: AppColor.redColor),
                ),
                filled: true,
                fillColor: Colors.white,
                hintText: 'Select a student'.tr,
                prefixIcon: Icon(
                  Icons.school_outlined,
                  color: AppColor.primaryColor,
                ),
              ),
              items: _sonController.sons.map((son) {
                return DropdownMenuItem<int>(
                  value: son.id,
                  child: Text(
                    son.name ?? '',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStudentId = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'Please select a student'.tr;
                }
                return null;
              },
              isExpanded: true,
              icon: Icon(
                Icons.arrow_drop_down,
                color: AppColor.primaryColor,
              ),
              dropdownColor: Colors.white,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPickupPersonFields() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                Icons.directions_walk_outlined,
                size: 18.r,
                color: AppColor.primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                'Pickup Information'.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),

          // Helper text
          Container(
            padding: EdgeInsets.all(12.r),
            decoration: BoxDecoration(
              color: AppColor.primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: AppColor.primaryColor.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16.r,
                  color: AppColor.primaryColor,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    'Leave blank if guardian will pick up the student'.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColor.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16.h),

          // Pickup person name
          CustomTextField(
            controller: _pickupPersonController,
            hint: 'Pickup Person Name'.tr,
            title: 'Pickup Person'.tr,
            prefixIcon: Icon(
              Icons.person,
              color: AppColor.primaryColor,
            ),
          ),
          SizedBox(height: 16.h),

          // Pickup person phone
          CustomTextField(
            controller: _pickupPhoneController,
            hint: 'Pickup Person Phone'.tr,
            title: 'Phone'.tr,
            keyboardType: TextInputType.phone,
            prefixIcon: Icon(
              Icons.phone,
              color: AppColor.primaryColor,
            ),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (!_phoneRegex.hasMatch(value)) {
                  return 'Please enter a valid phone number'.tr;
                }
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),

          // Relationship to student
          CustomTextField(
            controller: _relationshipController,
            hint: 'Relationship to Student'.tr,
            title: 'Relationship'.tr,
            prefixIcon: Icon(
              Icons.family_restroom,
              color: AppColor.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesField() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                Icons.note_alt_outlined,
                size: 18.r,
                color: AppColor.primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                'Notes'.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColor.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Notes field
          CustomTextField(
            controller: _notesController,
            hint: 'Any special instructions or notes'.tr,
            title: 'Additional Notes'.tr,
            maxLine: 4,
            minLine: 4,
            prefixIcon: Icon(
              Icons.description_outlined,
              color: AppColor.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime now = DateTime.now();
    final DateTime initialDate = _selectedDate;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now, // Can't select dates in the past
      lastDate: DateTime(now.year + 1, now.month, now.day),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColor.primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  Future<void> _selectTime() async {
    final TimeOfDay initialTime = _selectedTime;

    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColor.primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
        _timeController.text = _formatTimeOfDay(picked);
      });
    }
  }

  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final now = DateTime.now();
    final dateTime = DateTime(
      now.year,
      now.month,
      now.day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
    return DateFormat('HH:mm', Get.locale?.languageCode ?? "ar")
        .format(dateTime);
  }

  void _showConfirmationDialog() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Text(
          'Confirm Dismissal Request'.tr,
          style: TextStyle(
            color: AppColor.primaryColor,
            fontWeight: FontWeight.bold,
            fontSize: 18.sp,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to submit this dismissal request?'.tr,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.greyColor5,
              ),
            ),
            SizedBox(height: 16.h),

            // Student info
            _buildConfirmationItem(
              'Student'.tr,
              _sonController.sons
                      .firstWhere((s) => s.id == _selectedStudentId)
                      .name ??
                  '',
            ),

            // Pickup info (if provided)
            if (_pickupPersonController.text.isNotEmpty)
              _buildConfirmationItem(
                'Pickup Person'.tr,
                _pickupPersonController.text,
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: AppColor.greyColor3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: Text(
              'Cancel'.tr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _submitDismissalRequest();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: Text(
              'Submit'.tr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmationItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: AppColor.greyColor5,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.blackColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _submitDismissalRequest() async {
    _isLoading.value = true;

    try {
      final success = await _dismissalController.createDismissal(
        studentId: _selectedStudentId!,
        dismissalDate: _dateController.text,
        dismissalTime: _timeController.text,
        pickupPerson: _pickupPersonController.text,
        pickupPersonPhone: _pickupPhoneController.text,
        pickupPersonRelationship: _relationshipController.text,
        notes: _notesController.text,
      );

      if (success) {
        _dismissalController.refresh();
      }
    } finally {
      _isLoading.value = false;
    }
  }
}
