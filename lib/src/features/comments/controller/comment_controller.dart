import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide FormData;
import 'package:rawd_aljenan/src/core/controller/base_controller.dart';
import 'package:rawd_aljenan/src/core/data/enums/page_status.dart';
import 'package:rawd_aljenan/src/core/data/models/comment.dart';
import 'package:rawd_aljenan/src/core/utils/helper_function.dart';

class CommentController extends BaseController {
  static CommentController get instance => Get.find();

  TextEditingController commentController = TextEditingController();

  final int id;
  final String type;
  CommentController({
    required this.id,
    required this.type,
  });

  final ScrollController scrollController = ScrollController();

  RxList<Comment> comments = <Comment>[].obs;

  RxInt currentPage = 1.obs;

  RxInt lastPage = 0.obs;

  RxInt totalItems = 0.obs;

  void scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void onInit() {
    getComments(
      page: 1,
    );
    super.onInit();
  }

  void getComments({
    required int page,
  }) async {
    callApi(() async {
      if (page == 1) {
        setPageStatus(PageStatus.loading);
      }
      currentPage.value = page;
      final response =
          await apiProvider.getComments(type: type, id: id, page: page);
      if (page == 1) {
        comments.value = response.comments;
        comments.refresh();
        lastPage.value = response.lastPage;
        totalItems.value = response.itemsCount;
        if (totalItems.value == 0) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      } else {
        comments.addAll(response.comments);
        comments.refresh();
      }
    }, withLoading: false);
  }

  void getMoreComments() async {
    getComments(
      page: currentPage.value + 1,
    );
  }

  bool canLoadMore() {
    return currentPage.value < lastPage.value;
  }

  Future<void> addComment() async {
    final response = await apiProvider.createComment(FormData.fromMap({
      'comment': commentController.text,
      'type': type,
      'id': id,
    }));
    comments.insert(0, response.data!);
    comments.refresh();
    HelperFunction.showSuccessMessage(response.message ?? "");
    scrollToBottom();
    commentController.clear();
  }
}
