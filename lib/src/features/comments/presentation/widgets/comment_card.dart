// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import '../../../../core/data/models/comment.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_text.dart';

class CommentCard extends StatelessWidget {
  final Comment comment;
  const CommentCard({
    required this.comment,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CustomCachedNetworkImage(
              imageUrl: comment.userImage ?? "",
              borderRadius: BorderRadius.circular(100),
              border: Border.all(
                width: 2,
                color: AppColor.fromHex(comment.userColor ?? "#ffffff"),
              ),
              fit: BoxFit.cover,
              width: 47,
              height: 47,
            ),
            const SizedBox(
              width: 8,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomText(
                    text: comment.userName ?? "",
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    height: 1,
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  CustomText(
                    text: comment.createdAt ?? "",
                    fontSize: 14,
                    height: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
        Container(
          margin: const EdgeInsetsDirectional.only(
            top: 8,
            start: 55,
          ),
          width: double.maxFinite,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.05),
                  spreadRadius: 7,
                  blurRadius: 7,
                  offset: const Offset(0, 2),
                ),
              ]),
          child: CustomText(
            text: comment.comment ?? "",
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
