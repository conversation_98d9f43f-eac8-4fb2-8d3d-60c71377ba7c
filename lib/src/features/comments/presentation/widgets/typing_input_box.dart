import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';

class TypingInputBox extends StatefulWidget {
  const TypingInputBox({
    super.key,
    required this.onTap,
    this.textEditingController,
    this.hint,
  });

  final Future<void> Function(String) onTap;
  final TextEditingController? textEditingController;
  final String? hint;

  @override
  State<TypingInputBox> createState() => _TypingInputBoxState();
}

class _TypingInputBoxState extends State<TypingInputBox> {
  bool isLoading = false;
  String message = "";
  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 5,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: CustomTextField(
              controller: widget.textEditingController,
              hint: widget.hint,
              margin: EdgeInsets.zero,
              transparentBorder: true,
              maxLine: 2,
              minLine: 1,
              onChanged: (value) {
                message = value;
                setState(() {});
              },
              keyboardType: TextInputType.multiline,
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Material(
            borderRadius: BorderRadius.circular(16),
            color: Get.theme.primaryColor,
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: message.isEmpty || isLoading
                  ? null
                  : () async {
                      setState(() {
                        isLoading = true;
                      });
                      await widget.onTap.call(message);
                      setState(() {
                        isLoading = false;
                      });
                    },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: isLoading
                    ? const SizedBox(
                        width: 35,
                        height: 35,
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                      )
                    : SvgPicture.asset(
                        AppAssets.send,
                        colorFilter: const ColorFilter.mode(
                            Colors.white, BlendMode.srcIn),
                        width: 35,
                        matchTextDirection: true,
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
