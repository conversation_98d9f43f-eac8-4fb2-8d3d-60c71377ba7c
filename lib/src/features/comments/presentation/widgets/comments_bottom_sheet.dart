import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../core/data/enums/page_status.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../../../core/widgets/errors/no_internet_error_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/comment_controller.dart';
import 'comment_card.dart';
import 'typing_input_box.dart';

class CommentsBottomSheet extends StatelessWidget {
  const CommentsBottomSheet({
    super.key,
    required this.type,
    required this.id,
  });
  final String type;
  final int id;
  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: Get.height * 0.9,
        minHeight: Get.height * 0.3,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            width: double.maxFinite,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  AppAssets.comment,
                  height: 35,
                  colorFilter:
                      ColorFilter.mode(Get.theme.primaryColor, BlendMode.srcIn),
                ),
                const SizedBox(width: 8),
                Text(
                  "Comments".tr,
                  style: TextStyle(
                    fontSize: 18,
                    color: Get.theme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(
                    Icons.close,
                  ),
                ),
              ],
            ),
          ),
          const Divider(
            height: 0.3,
            indent: 16,
            endIndent: 16,
          ),
          Expanded(
            child: CommentListView(
              type: type,
              id: id,
            ),
          )
        ],
      ),
    );
  }
}

class CommentListView extends StatelessWidget {
  const CommentListView({
    super.key,
    required this.type,
    required this.id,
  });
  final String type;
  final int id;
  @override
  Widget build(BuildContext context) {
    var controller = Get.put(
      CommentController(
        type: type,
        id: id,
      ),
    );
    return SizedBox(
      width: double.maxFinite,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Obx(() {
            if (controller.pageStatus.value == PageStatus.loading) {
              return Center(
                child: CircularProgressIndicator(
                  color: Get.theme.primaryColor,
                ),
              );
            }

            if (controller.pageStatus.value == PageStatus.noInternet) {
              return const NoInternetErrorWidget();
            }

            if (controller.pageStatus.value == PageStatus.empty) {
              return EmptyWidget(
                icon: AppAssets.comment,
                iconColor: Get.theme.primaryColor,
                message: "No comments yet".tr,
              );
            }
            return ListView.separated(
              reverse: true,
              padding: const EdgeInsets.only(
                right: 16,
                left: 16,
                top: 16,
                bottom: 100,
              ),
              controller: controller.scrollController,
              itemCount: controller.comments.length + 1,
              itemBuilder: (BuildContext context, int index) {
                if (index == controller.comments.length) {
                  if (controller.canLoadMore()) {
                    controller.getMoreComments();
                    return SizedBox(
                      height: 100,
                      width: double.maxFinite,
                      child: Center(
                        child: Text('loading more ...'.tr),
                      ),
                    );
                  } else {
                    return const SizedBox.shrink();
                  }
                }
                return CommentCard(
                  comment: controller.comments[index],
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(
                  height: 16,
                );
              },
            );
          }),
          if ((AuthService.instance.parentUser.value != null &&
                  AuthService.instance.user.value?.role == Roles.Guardian) ||
              AuthService.instance.user.value?.role == Roles.Teacher)
            Positioned(
              bottom: 16,
              right: 16,
              left: 16,
              child: TypingInputBox(
                hint: "Write a comment ..".tr,
                textEditingController:
                    CommentController.instance.commentController,
                onTap: (value) async {
                  await CommentController.instance.addComment();
                },
              ),
            )
        ],
      ),
    );
  }
}
