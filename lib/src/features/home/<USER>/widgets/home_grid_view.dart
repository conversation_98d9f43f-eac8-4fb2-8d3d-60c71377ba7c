import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controller/home_controller.dart';

import '../../../auth/services/auth_service.dart';

class HomeGridView extends StatelessWidget {
  const HomeGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      //to refresh this widget when role change
      AuthService.instance.user.value?.role;

      return GridView(
        padding: const EdgeInsets.only(
          top: 10,
          right: 20,
          left: 20,
          bottom: 20,
        ),
        shrinkWrap: true,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1 / 0.7,
        ),
        children: HomeController.instance.homeGridTile,
      );
    });
  }
}
