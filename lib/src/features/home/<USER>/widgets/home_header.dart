import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/extensions.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../auth/services/auth_service.dart';

import '../../controller/son_controller.dart';

import '../../controller/teacher_controller.dart';

import '../../../class_schedule/presentation/widgets/choose_son_bottom_sheet.dart';

class HomeHeader extends StatelessWidget {
  const HomeHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    var user = AuthService.instance.user;

    /// AuthService loginController = Get.find();

    SonController sonController = Get.put(SonController());

    // to get and update the teachers for AdminAssistant adminAssistantTiles
    Get.put(TeacherController());

    return Obx(() {
      String subtitle = "";
      if (user.value?.role == Roles.Teacher ||
          user.value?.role == Roles.Supervisor) {
        subtitle = (user.value?.job?.isNotEmpty ?? true)
            ? user.value?.job ?? ""
            : user.value?.role ?? "";
      } else if (user.value?.role == Roles.Student) {
        subtitle = user.value?.studentClass ?? "";
      } else {
        subtitle = user.value?.role ?? "";
      }

      return Stack(
        children: [
          Container(
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30.r),
                  bottomRight: Radius.circular(30.r)),
              color: AppColor.whiteColor,
            ),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 40.h, horizontal: 20.w),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 20.w,
              right: 20.w,
              bottom: 10,
            ),
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color:
                        AppColor.fromHex(user.value?.themeColor ?? "#58A1D6"),
                    boxShadow: [
                      BoxShadow(
                          color: AppColor.greyColor,
                          offset: const Offset(5, 5),
                          blurRadius: 10.r)
                    ],
                  ),
                  width: double.infinity,
                  height: 125.h,
                  child: Image.asset(
                    AppAssets.backGroundImage,
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  width: double.infinity,
                  height: 100.h,
                  child: Row(
                    children: [
                      SizedBox(width: 20.w),
                      CircleAvatar(
                        maxRadius: 40.r,
                        backgroundColor: AppColor.whiteColor,
                        child: Center(
                          child: CustomCachedNetworkImage(
                            imageUrl: user.value?.image ?? "",
                            imageBuilder: (ctx, provider) => CircleAvatar(
                              maxRadius: 35.r,
                              backgroundImage: provider,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: CustomText(
                                    text: user.value?.name ?? "",
                                    color: AppColor.whiteColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                                if (user.value?.role == Roles.Guardian ||
                                    AuthService.instance.selectedSon.value !=
                                        null)
                                  Transform.rotate(
                                    angle: 90 * 3.14 / 180,
                                    child: IconButton(
                                      onPressed: () {
                                        Get.bottomSheet(
                                          ChooseSonBottomSheet(
                                            sons: sonController.sons,
                                            selectedChildId: AuthService
                                                .instance.selectedSon.value?.id,
                                          ),
                                          isScrollControlled: true,
                                        );
                                      },
                                      icon: const Icon(
                                        Icons.chevron_left,
                                        size: 30,
                                      ),
                                      color: AppColor.whiteColor,
                                    ),
                                  )
                              ],
                            ),
                            5.verticalSpace,
                            if (user.value?.role == Roles.Guardian)
                              CustomText(
                                text: user.value?.job ?? "",
                                color: AppColor.whiteColor,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              )
                            else
                              CustomText(
                                text: subtitle.upperCaseFirstChar().tr,
                                color: AppColor.whiteColor,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                          ],
                        ),
                      ),
                      SizedBox(width: 10.w),
                    ],
                  ),
                ),
                PositionedDirectional(
                  end: 0,
                  bottom: 16,
                  child: Container(
                    margin: const EdgeInsetsDirectional.only(end: 20),
                    padding:
                        const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(24)),
                    child: CustomText(
                        color: Get.theme.primaryColor,
                        fontWeight: FontWeight.bold,
                        text: user.value?.role?.upperCaseFirstChar().tr ?? ""),
                  ),
                )
              ],
            ),
          ),
        ],
      );
    });
  }
}
