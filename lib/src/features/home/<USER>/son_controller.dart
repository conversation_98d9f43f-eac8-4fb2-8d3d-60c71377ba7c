import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/user.dart';

class SonController extends BaseController {
  static SonController instance = Get.find();

  RxList<User> sons = RxList<User>([]);

  @override
  void onReady() {
    getSons();
    super.onReady();
  }

  void getSons() {
    callApi(() async {
      var response = await apiProvider.getSons();
      sons.value = response;
      if (sons.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else {
        setPageStatus(PageStatus.loaded);
      }
    });
  }
}
