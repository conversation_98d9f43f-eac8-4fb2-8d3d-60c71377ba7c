import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';

import '../../../core/data/models/teachers.dart';

class TeacherController extends BaseController {
  static TeacherController get instance => Get.find();
  RxList<Teacher> teachers = RxList<Teacher>([]);

  void getTeachers() {
    callApi(
      () async {
        var response = await apiProvider.getTeachers();
        teachers.value = response;
        updateEmptyStatus(teachers);
        teachers.refresh();
      },
    );
  }
}
