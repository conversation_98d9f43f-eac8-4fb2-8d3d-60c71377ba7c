import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../controller/home_controller.dart';
import '../widgets/home_grid_view.dart';
import '../widgets/home_header.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(HomeController());
    return LayoutScreen(
      title: 'Home'.tr,
      withDrawer: true,
      body: const Column(
        children: [
          HomeHeader(),
          Expanded(child: HomeGridView()),
        ],
      ),
    );
  }
}
