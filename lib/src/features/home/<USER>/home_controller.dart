import 'package:get/get.dart';
import '../../../core/router/app_router.dart';
import '../../../core/utils/app_color.dart';
import '../../../core/utils/app_assets.dart';
import '../../auth/services/auth_service.dart';
import '../presentation/widgets/home_grid_tile.dart';

import '../../../core/utils/app_consts.dart';

class HomeController extends GetxController {
  static HomeController instance = Get.find();

  List<HomeGridTile> get studentTiles => [
        HomeGridTile(
          label: 'Class Schedule',
          gradientColors: AppColor.redGradient,
          svgIcon: AppAssets.classTableImage,
          onTap: () {
            Get.toNamed(AppRouter.classScheduleScreen);
          },
        ),

        HomeGridTile(
          label: 'Plans',
          gradientColors: AppColor.greenGradient,
          svgIcon: AppAssets.weekPlaneImage,
          onTap: () {
            Get.toNamed(AppRouter.plansScreen);
          },
        ),
        HomeGridTile(
          label: 'Daily Coverage',
          gradientColors: AppColor.skyGradient,
          svgIcon: AppAssets.dailyCoverImage,
          onTap: () {
            Get.toNamed(AppRouter.postScreen);
          },
        ),
        if (!(AuthService.instance.user.value?.isKindergarten ?? false))
          HomeGridTile(
            label: 'Chats',
            gradientColors: AppColor.orangeGradient,
            svgIcon: AppAssets.mmessageImage,
            onTap: () {
              Get.toNamed(AppRouter.chatScreen);
            },
          ),
        HomeGridTile(
          label: 'Activities',
          gradientColors: AppColor.yellowGradient,
          svgIcon: AppAssets.activity,
          onTap: () {
            Get.toNamed(AppRouter.activityScreen);
          },
        ),
        // if (!AuthService.instance.user.value!.isKindergarten)
        HomeGridTile(
          label: 'Home Work',
          gradientColors: AppColor.blueGradient,
          svgIcon: AppAssets.date3Image,
          onTap: () {
            Get.toNamed(AppRouter.homeworksScreen);
          },
        ),
        // if (!AuthService.instance.user.value!.isKindergarten)
        HomeGridTile(
          label: 'Honor board',
          gradientColors: AppColor.purpleGradient,
          svgIcon: AppAssets.medal,
          onTap: () {
            Get.toNamed(AppRouter.parentHonorBoardScreen);
          },
        ),
        // if (!AuthService.instance.user.value!.isKindergarten)
        HomeGridTile(
          label: 'Continuous Rating',
          gradientColors: AppColor.cyanGradient,
          svgIcon: AppAssets.chart,
          onTap: () {
            Get.toNamed(
              AppRouter.studentReportsScreen,
              arguments: {
                "section_id": AuthService.instance.user.value?.sectionId,
                "student": AuthService.instance.user.value,
              },
            );
          },
        ),
        // student points
        HomeGridTile(
          label: 'My Points',
          gradientColors: AppColor.orangeGradient,
          svgIcon: AppAssets.cup,
          onTap: () => Get.toNamed(AppRouter.studentPointsScreen),
        ),
        HomeGridTile(
          label: 'Study Resources'.tr,
          gradientColors: AppColor.darkBlueGradient,
          svgIcon: AppAssets.studyPlan,
          onTap: () {
            Get.toNamed(AppRouter.studyResourcesScreen);
          },
        ),
      ];

  List<HomeGridTile> get teacherTiles => [
        HomeGridTile(
          label: 'Class Schedule',
          gradientColors: AppColor.redGradient,
          svgIcon: AppAssets.classTableImage,
          onTap: () => Get.toNamed(AppRouter.classScheduleScreen),
        ),

        if (!(AuthService.instance.user.value?.isKindergarten ?? false))
          HomeGridTile(
            label: 'Student Attendance',
            gradientColors: AppColor.purpleGradient,
            svgIcon: AppAssets.attendance,
            onTap: () => Get.toNamed(AppRouter.studentAttendanceScreen),
          ),
        HomeGridTile(
          label: 'Home Work',
          gradientColors: AppColor.blueGradient,
          svgIcon: AppAssets.date3Image,
          onTap: () => Get.toNamed(AppRouter.homeworksScreen),
        ),
        // activities
        HomeGridTile(
          label: 'Activities',
          gradientColors: AppColor.yellowGradient,
          svgIcon: AppAssets.activity,
          onTap: () => Get.toNamed(AppRouter.activityScreen),
        ),
        // chats
        HomeGridTile(
          label: 'Chats',
          gradientColors: AppColor.orangeGradient,
          svgIcon: AppAssets.mmessageImage,
          onTap: () => Get.toNamed(AppRouter.chatScreen),
        ),
        // daily coverage
        HomeGridTile(
          label: 'Daily Coverage',
          gradientColors: AppColor.skyGradient,
          svgIcon: AppAssets.dailyCoverImage,
          onTap: () => Get.toNamed(AppRouter.postScreen),
        ),
        // week plan
        HomeGridTile(
          label: 'Plans',
          gradientColors: AppColor.greenGradient,
          svgIcon: AppAssets.weekPlaneImage,
          onTap: () => Get.toNamed(AppRouter.plansScreen),
        ),
        // continuous rating
        HomeGridTile(
          label: 'Continuous Rating',
          gradientColors: AppColor.cyanGradient,
          svgIcon: AppAssets.chart,
          onTap: () => Get.toNamed(AppRouter.dailyReportsScreen),
          // onTap: () => Get.toNamed(AppRouter.soon),
        ),
        // student points
        HomeGridTile(
          label: 'Motivational Points',
          gradientColors: AppColor.purpleGradient,
          svgIcon: AppAssets.cup,
          onTap: () => Get.toNamed(AppRouter.studentPointsScreen),
        ),
        HomeGridTile(
          label: 'Study Resources'.tr,
          gradientColors: AppColor.darkBlueGradient,
          svgIcon: AppAssets.studyPlan,
          onTap: () => Get.toNamed(AppRouter.studyResourcesScreen),
        ),
      ];

  final List<HomeGridTile> guardianTiles = [
    HomeGridTile(
      label: 'My children',
      gradientColors: AppColor.redGradient,
      svgIcon: AppAssets.myChildrenImage,
      onTap: () => Get.toNamed(AppRouter.sonsScreen),
    ),
    HomeGridTile(
      label: 'Honor board',
      gradientColors: AppColor.purpleGradient,
      svgIcon: AppAssets.medal,
      onTap: () => Get.toNamed(AppRouter.parentHonorBoardScreen),
    ),
    HomeGridTile(
      label: 'Chats',
      gradientColors: AppColor.yellowGradient,
      svgIcon: AppAssets.mmessageImage,
      onTap: () => Get.toNamed(AppRouter.chatScreen),
    ),
    HomeGridTile(
      label: 'Daily Coverage',
      gradientColors: AppColor.blueGradient,
      svgIcon: AppAssets.dailyCoverImage,
      onTap: () => Get.toNamed(AppRouter.postScreen),
    ),
    HomeGridTile(
      label: 'Student Dismissal',
      gradientColors: AppColor.greenGradient,
      svgIcon: AppAssets.megaphone,
      svgIconColor: AppColor.whiteColor,
      onTap: () => Get.toNamed(AppRouter.dismissalsScreen),
    ),
  ];

  List<HomeGridTile> get supervisorTiles => [
        if (AuthService.instance.user.value!.canChatWithGuardian)
          HomeGridTile(
            label: 'Chats',
            gradientColors: AppColor.redGradient,
            svgIcon: AppAssets.mmessageImage,
            onTap: () => Get.toNamed(AppRouter.chatScreen),
          ),
        // HomeGridTile(
        //   label: 'Student Attendance',
        //   gradientColors: AppColor.blueGradient,
        //   svgIcon: AppAssets.attendance,
        //   onTap: () => Get.toNamed(AppRouter.studentAttendanceScreen),
        // ),
        HomeGridTile(
          label: 'Activities',
          gradientColors: AppColor.orangeGradient,
          svgIcon: AppAssets.activity,
          onTap: () => Get.toNamed(AppRouter.activityScreen),
        ),
        HomeGridTile(
          label: 'Daily Coverage',
          gradientColors: AppColor.skyGradient,
          svgIcon: AppAssets.dailyCoverImage,
          onTap: () => Get.toNamed(AppRouter.postScreen),
        ),
        HomeGridTile(
          label: 'Advertisements',
          gradientColors: AppColor.cyanGradient,
          svgIcon: AppAssets.ad,
          onTap: () => Get.toNamed(
            AppRouter.advertisementsScreen,
            arguments: "advertisement",
          ),
        ),
        HomeGridTile(
          label: 'News',
          gradientColors: AppColor.greenGradient,
          svgIcon: AppAssets.news,
          onTap: () =>
              Get.toNamed(AppRouter.advertisementsScreen, arguments: "news"),
        ),
      ];

  final List<HomeGridTile> adminAssistantTiles = [
    HomeGridTile(
      label: 'Student Attendance',
      gradientColors: AppColor.blueGradient,
      svgIcon: AppAssets.attendance,
      onTap: () => Get.toNamed(AppRouter.studentAttendanceScreen),
    ),
    HomeGridTile(
      label: 'Teacher Attendance',
      gradientColors: AppColor.redGradient,
      svgIcon: AppAssets.teacherBlueImage,
      svgIconColor: AppColor.whiteColor,
      onTap: () => Get.toNamed(AppRouter.soon),
    ),
    HomeGridTile(
      label: 'Absence Report',
      gradientColors: AppColor.orangeGradient,
      svgIcon: AppAssets.chart,
      svgIconColor: AppColor.whiteColor,
      onTap: () => Get.toNamed(AppRouter.absenceReportScreen),
    ),
    HomeGridTile(
      label: 'Chats',
      gradientColors: AppColor.skyGradient,
      svgIcon: AppAssets.mmessageImage,
      onTap: () => Get.toNamed(AppRouter.chatScreen),
    ),
    HomeGridTile(
      label: 'Student Dismissal',
      gradientColors: AppColor.greenGradient,
      svgIcon: AppAssets.megaphone,
      svgIconColor: AppColor.whiteColor,
      onTap: () => Get.toNamed(AppRouter.dismissalsScreen),
    ),
  ];

  List<HomeGridTile> get homeGridTile {
    String userType = AuthService.instance.user.value?.role ?? "";
    switch (userType) {
      case Roles.Student:
        return studentTiles;
      case Roles.Teacher:
        return teacherTiles;
      case Roles.Guardian:
        return guardianTiles;
      case Roles.Supervisor:
        return supervisorTiles;
      case Roles.AdminAssistant:
        return adminAssistantTiles;
      default:
    }
    return [];
  }
}
