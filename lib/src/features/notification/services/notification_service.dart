import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mobile_device_identifier/mobile_device_identifier.dart';
import '../../../core/data/models/responses/user_notification_response.dart';
import '../../../core/data/models/user_notification.dart';
import '../../../core/data/providers/api_provider.dart';
import '../../../core/router/app_router.dart';
import '../../../core/utils/app_assets.dart';
import '../../../core/utils/app_color.dart';
import '../../../core/utils/app_consts.dart';
import '../../../core/utils/extensions.dart';
import '../../../core/utils/helper_function.dart';

class NotificationService extends GetxService {
  static NotificationService get instance => Get.find();
  Rx<String?> fcmToken = Rx<String?>(null);
  Rx<int> unreadCount = Rx<int>(0);
  final FirebaseMessaging fcm = FirebaseMessaging.instance;
  ApiProvider get apiProvider => ApiProvider(AppConsts.authenticatedDio);
  RxList<UserNotification> notifications = RxList<UserNotification>([]);
  Future<NotificationService> init() async {
    NotificationSettings settings = await fcm.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint(
        'User granted notifications permission: ${settings.authorizationStatus}');

    // Retrieving the FCM token
    fcm.getToken().then((token) {
      fcmToken.value = token;
      registerDevice();
    });
    fcm.onTokenRefresh.listen((token) {
      debugPrint('refresh fcmToken: $fcmToken');
      fcmToken.value = token;
      registerDevice();
    });
    box.listenKey('token', (value) {
      registerDevice();
    });
    // Handling the initial message received when the app is launched from dead (killed state)
    // When the app is killed and a new notification arrives when user clicks on it
    // It gets the data to which screen to open
    FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message != null) {
        handleNotificationClick(message.data);
      }
    });
    // Handling background messages using the specified handler
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // Listening for incoming messages while the app is in the foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Got a message whilst in the foreground!');
      if (message.notification != null) {
        if (message.notification!.title != null &&
            message.notification!.body != null) {
          final notificationData = message.data;
          final screen = notificationData['screen'];
          print("screen: $screen");

          Get.snackbar(
            message.notification!.title ?? "",
            message.notification!.body ?? "",
            icon: SvgPicture.asset(
              AppAssets.notification,
              color: Colors.white,
            ),
            colorText: Colors.white,
            backgroundColor: AppColor.primaryColor,
            isDismissible: true,
            duration: Duration(seconds: 60),
            shouldIconPulse: true,
          );

          getUnreadNotificationCount();

          // Showing an alert dialog when a notification is received (Foreground state)
        }
      }
    });
    // Handling a notification click event when the app is in the background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint(
          'onMessageOpenedApp: ${message.notification!.title.toString()}');
      handleNotificationClick(message.data);
    });
    return this;
  }

  void handleNotificationClick(Map<String, dynamic>? data) {
    if (data == null) return;
    if (data.containsKey('screen')) {
      String screen = data['screen'];
      int? id = int.parse(data['id'].toString());
      switch (screen) {
        case "chat_details":
          HelperFunction.navigateToChatDetails(conversationId: id);
          break;
        case "activity_details":
        case "activities":
          Get.toNamed(AppRouter.activityScreen);
          break;
        default:
      }
    }
  }

  Future<void> registerDevice() async {
    if (box.read('token') == null) return;
    var apiProvider = ApiProvider(AppConsts.authenticatedDio);
    String? fcmToken = await FirebaseMessaging.instance.getToken();
    String osType = Platform.isAndroid ? "android" : "ios";
    final deviceId = await MobileDeviceIdentifier().getDeviceId();
    try {
      apiProvider.registerDevice({
        "device_id": deviceId,
        "fcm_token": fcmToken,
        "os_type": osType,
        'locale': Get.locale?.languageCode,
      });
    } catch (e) {
      printLog(e);
    }
  }

  Future<void> getUnreadNotificationCount() async {
    bool hasConnection = await HelperFunction.checkConnectivity();
    if (!hasConnection) return;
    try {
      var response = await apiProvider.getUnreadNotificationCount(
        lastId: getLastId(),
      );
      unreadCount.value = response.unreadCount;
      if (response.hasNewNotifications) {
        var lastId = getLastId();
        var response = await getNotifications(lastId: lastId);
        if (response != null) {
          for (var page = 2; page <= response!.lastPage; page++) {
            response = await getNotifications(page: page, lastId: lastId);
          }
        }
      }
    } catch (e) {
      printLog(e);
    }
  }

  Future<UserNotifacationResponse?> getNotifications({
    int page = 1,
    int? lastId,
  }) async {
    print("last id: $lastId");
    bool hasConnection = await HelperFunction.checkConnectivity();
    if (!hasConnection) return null;
    try {
      var response = await apiProvider.getNotifications(
        lastId: lastId,
        page: page,
      );
      if (lastId == null) {
        notifications.value = response.notifications;
      } else {
        notifications.insertAll(0, response.notifications);
      }
      notifications.refresh();
      return response;
    } catch (e) {
      printLog(e);
      return null;
    }
  }

  Future<void> markAsRead(int id) async {
    bool hasConnection = await HelperFunction.checkConnectivity();
    if (!hasConnection) return;
    try {
      var elIndex = notifications.indexWhere((element) => element.id == id);
      if (elIndex == -1) return;
      notifications[elIndex].unread = false;
      unreadCount.value = unreadCount.value - 1;
      notifications.refresh();
      await apiProvider.markNotificationAsRead(id);
    } catch (e) {
      printLog(e);
    }
  }

  Future<void> markAllAsRead() async {
    bool hasConnection = await HelperFunction.checkConnectivity();
    if (!hasConnection) return;
    try {
      for (var i = 0; i < notifications.length; i++) {
        notifications[i].unread = false;
      }
      notifications.refresh();
      unreadCount.value = 0;
      await apiProvider.markAllNotificationsAsRead();
    } catch (e) {
      printLog(e);
    }
  }

  int? getLastId() {
    if (notifications.isEmpty) return null;
    return notifications.first.id;
  }

  Future<void> deleteNotification(int id) async {
    bool hasConnection = await HelperFunction.checkConnectivity();
    if (!hasConnection) return;
    try {
      var elIndex = notifications.indexWhere((element) => element.id == id);
      if (elIndex != -1) {
        var notification = notifications[elIndex];
        if (notification.unread) unreadCount.value -= 1;
        notifications.removeWhere((element) => element.id == id);
      }
      notifications.refresh();
      await apiProvider.deleteNotification(id);
    } catch (e) {
      printLog(e);
    }
  }

  Future<void> deleteAllNotifications() async {
    bool hasConnection = await HelperFunction.checkConnectivity();
    if (!hasConnection) return;
    try {
      notifications.clear();
      notifications.refresh();
      await apiProvider.deleteAllNotifications();
      unreadCount.value = 0;
    } catch (e) {
      printLog(e);
    }
  }
}

Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  debugPrint('Handling a background message: ${message.notification!.title}');
}
