import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/user_notification.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../services/notification_service.dart';

class NotificationListTile extends StatefulWidget {
  const NotificationListTile({super.key, required this.notification});
  final UserNotification notification;

  @override
  State<NotificationListTile> createState() => _NotificationListTileState();
}

class _NotificationListTileState extends State<NotificationListTile> {
  Timer? _timer;
  @override
  void initState() {
    _timer = Timer.periodic(const Duration(minutes: 1), (_) {
      if (mounted) setState(() {});
    });
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomListTile(
      title: widget.notification.title,
      fontSize: 12.sp,
      maxLine: 3,
      backgroundColor: widget.notification.statusColor,
      detail: CustomText(
        text: widget.notification.content,
        maxLine: 10,
        fontSize: 12.sp,
        textAlign: TextAlign.start,
        color: AppColor.greyColor3,
        fontWeight: FontWeight.bold,
      ),
      subtitle: widget.notification.timeAgo,
      subtitleIcon: AppAssets.calender,
      leadingIcon: AppAssets.notification,
      leadingIconSize: 30,
      endAction: [
        SlidableAction(
          onPressed: (context) {
            NotificationService.instance
                .deleteNotification(widget.notification.id);
          },
          icon: Icons.delete_outline_rounded,
          backgroundColor: AppColor.redColor,
          foregroundColor: AppColor.whiteColor,
          label: 'Delete'.tr,
        )
      ],
      onTap: () {
        if (widget.notification.unread) {
          NotificationService.instance.markAsRead(widget.notification.id);
        }
        NotificationService.instance
            .handleNotificationClick(widget.notification.data);
      },
    );
  }
}
