import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../widgets/notifications_list_tile.dart';
import '../../services/notification_service.dart';

import '../../../../core/screens/layout_screen.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: 'Notifications'.tr,
      withBackground: false,
      body: Obx(() {
        var notification = NotificationService.instance.notifications.toList();

        if (notification.isEmpty) {
          return EmptyWidget(
            message: 'No Notifications'.tr,
            icon: AppAssets.notification,
            iconSize: 110,
          );
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InkWell(
                    onTap: () {
                      NotificationService.instance.deleteAllNotifications();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'Clear all'.tr,
                        style: const TextStyle(
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      NotificationService.instance.markAllAsRead();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'Mark all as read'.tr,
                        style: TextStyle(
                          color: AppColor.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.separated(
                separatorBuilder: (context, index) => 16.verticalSpace,
                itemCount: notification.length,
                padding: const EdgeInsets.only(
                  right: 16,
                  left: 16,
                ),
                itemBuilder: (context, index) {
                  return NotificationListTile(
                    notification: notification[index],
                  );
                },
              ),
            ),
          ],
        );
      }),
    );
  }
}
