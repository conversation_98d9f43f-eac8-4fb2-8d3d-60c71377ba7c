import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/about_page.dart';

class AboutUsController extends BaseController {
  var aboutPages = <AboutPage>[].obs;
  @override
  void onInit() {
    getAboutPages();
    super.onInit();
  }

  Future<void> getAboutPages() async {
    callApi(() async {
      var response = await apiProvider.getAboutPages();
      aboutPages.value = response;
      if (aboutPages.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else {
        setPageStatus(PageStatus.loaded);
      }
    });
  }
}
