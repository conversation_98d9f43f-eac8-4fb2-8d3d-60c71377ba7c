import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/daily_post_media.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import 'image_gallery.dart';
import '../../../daily_post/presentation/widgets/post_card/post_media_slider.dart';

class AttatchedMedia extends StatelessWidget {
  const AttatchedMedia({
    super.key,
    required this.title,
    required this.type,
    required this.urls,
    this.maxHeight = 100,
  });
  final String title;
  final MediaType type;
  final List<String> urls;
  final double maxHeight;
  @override
  Widget build(BuildContext context) {
    if (urls.isEmpty) {
      return const SizedBox.shrink();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 8,
        ),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        type == MediaType.image
            ? ConstrainedBox(
                constraints: BoxConstraints(maxHeight: maxHeight),
                child: CarouselView(
                  itemExtent: Get.width * 0.35,
                  shrinkExtent: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  onTap: (index) {
                    Get.to(
                      () => ImageGalery(
                        index: index,
                        images: urls,
                      ),
                    );
                  },
                  children: urls
                      .map(
                        (url) => CustomCachedNetworkImage(
                          imageUrl: url,
                          fit: BoxFit.cover,
                        ),
                      )
                      .toList(),
                ),
              )
            : PostMediaSlider(
                borderRadius: BorderRadius.circular(8),
                media: urls
                    .map(
                      (e) => DailyPostMedia(
                        fileUrl: e,
                        type: MediaType.video,
                      ),
                    )
                    .toList(),
              ),
      ],
    );
  }
}
