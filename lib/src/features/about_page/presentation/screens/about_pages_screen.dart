import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../controller/about_us_controller.dart';
import '../../../../core/screens/layout_screen.dart';

import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/extensions.dart';

class AboutPagesScreen extends StatelessWidget {
  const AboutPagesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(AboutUsController());

    return LayoutScreen(
      title: 'About Rawd AlJenan'.tr,
      controller: controller,
      body: Obx(() {
        return ListView.builder(
          padding: EdgeInsets.all(15.h),
          itemCount: controller.aboutPages.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: SmartFingersListTile(
                title: controller.aboutPages[index].title!.tr,
                onTap: () => Get.toNamed(
                  AppRouter.aboutDetailScreen,
                  arguments: controller.aboutPages[index],
                ),
                trailing: SmartFingersImage(imageUrl: AppAssets.arrowForward),
              ),
            );
          },
        );
      }),
    );
  }
}
