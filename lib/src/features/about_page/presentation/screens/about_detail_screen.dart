import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

import 'package:get/get.dart';
import '../../../../core/data/models/about_page.dart';
import '../../../../core/data/models/daily_post_media.dart';

import '../../../../core/screens/layout_screen.dart';
import '../widgets/attatched_media.dart';

class AboutDetailScreen extends StatelessWidget {
  final AboutPage? aboutUs;

  const AboutDetailScreen({super.key, required this.aboutUs});

  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: 'About Us'.tr,
      body: SingleChildScrollView(
          child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(15.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 5.0),
                  child: HtmlWidget(
                    aboutUs!.detail.toString(),
                    factoryBuilder: () => _WidgetFactory(),
                    textStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontSize: 14.sp,
                        ),
                  ),
                ),
                AttatchedMedia(
                  title: "Attached Images".tr,
                  urls: aboutUs?.images ?? [],
                  type: MediaType.image,
                ),
                AttatchedMedia(
                  title: "Attached Vidoes".tr,
                  urls: aboutUs?.videos ?? [],
                  type: MediaType.video,
                ),
              ],
            ),
          )
        ],
      )),
    );
  }
}

class _WidgetFactory extends WidgetFactory {
  @override
  void parseStyle(BuildTree tree, style) {
    if (style.property == 'font-family' || style.property == 'font-size') {
      // ignore
      return;
    }
    return super.parseStyle(tree, style);
  }
}
