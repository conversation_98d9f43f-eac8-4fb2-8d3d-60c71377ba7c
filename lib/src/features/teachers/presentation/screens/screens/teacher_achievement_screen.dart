import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/data/providers/api_provider.dart';
import '../../../../../core/router/app_router.dart';
import '../../../../../core/screens/layout_screen.dart';
import '../../../../../core/utils/app_consts.dart';
import '../../../../../core/widgets/buttons/custom_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../../auth/services/auth_service.dart';

import '../../../../../core/data/models/teachers.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/utils/extensions.dart';
import '../../../../../core/widgets/media/custom_boxes.dart';

class TeacherAchievementScreen extends StatelessWidget {
  final Teacher teacher;
  const TeacherAchievementScreen({super.key, required this.teacher});

  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: 'Teacher Achievements'.tr,
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            CustomCard(
              child: Row(
                children: [
                  SmartFingersImage(
                    imageUrl: teacher.image,
                    fit: BoxFit.fill,
                    height: 80,
                    width: 90,
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    flex: 6,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        FittedBox(
                          fit: BoxFit.scaleDown,
                          alignment: AlignmentDirectional.centerStart,
                          child: CustomText(
                            text: teacher.name,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColor.primaryColor,
                            maxLine: 2,
                          ),
                        ),
                        const SizedBox(height: 15),
                        CustomButton(
                          text: 'مراسلة الأستاذ',
                          onTap: () async {
                            AuthService.instance.selectedSon.value = null;
                            AuthService.instance.user.value =
                                AuthService.instance.parentUser.value;
                            AuthService.instance.token.value =
                                box.read("token");
                            var chat =
                                await ApiProvider(AppConsts.authenticatedDio)
                                    .getChatByReceiverIdOrConversationId(
                              receiverId: teacher.id,
                            );
                            Get.toNamed(AppRouter.chatDetails, arguments: chat);
                          },
                          color: AppColor.primaryColor,
                          borderRadius: 20,
                          textColor: AppColor.whiteColor,
                          fontSize: 12,
                          height: 35,
                          width: 100,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        // CustomGreyBox(
                        //   height: 40,
                        //   color: AppColor.greyColor.withOpacity(0.2),
                        //   child: Row(
                        //     mainAxisAlignment: MainAxisAlignment.center,
                        //     children: [
                        //       CustomText(
                        //         text: '500',
                        //         fontSize: 12,
                        //         fontWeight: FontWeight.bold,
                        //         color: AppColor.primaryColor,
                        //       ),
                        //       const Spacer(),
                        //       Icon(
                        //         Icons.stars,
                        //         size: 20,
                        //         color: AppColor.primaryColor,
                        //       )
                        //     ],
                        //   ),
                        // ),
                        const SizedBox(height: 20),
                        SmartFingersImage(
                          imageUrl: AppAssets.cup2,
                          height: 40,
                          width: 40,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // CustomCard(
            //   child: Row(
            //     children: [
            //       CustomText(
            //         text: 'المادة',
            //         fontSize: 18,
            //         fontWeight: FontWeight.bold,
            //         color: AppColor.primaryColor,
            //       ),
            //       const Spacer(),
            //       CustomButton(
            //         text: 'مادة التجويد',
            //         onTap: () {},
            //         textColor: AppColor.primaryColor,
            //         fontSize: 15,
            //         borderRadius: 20,
            //         color: AppColor.primaryColor.withOpacity(0.1),
            //       ),
            //     ],
            //   ),
            // ),
            // CustomCard(
            //   child: Column(
            //     children: [
            //       CustomCard(
            //         elevation: 0,
            //         padding: EdgeInsets.zero,
            //         child: Row(
            //           children: [
            //             CustomText(
            //               text: 'عدد ايام الحضور',
            //               fontSize: 16,
            //               fontWeight: FontWeight.bold,
            //               color: AppColor.greenColor,
            //             ),
            //             const Spacer(),
            //             CustomButton(
            //               height: 40.h,
            //               text: '200',
            //               onTap: () {},
            //               textColor: AppColor.greenColor,
            //               width: 100.w,
            //               fontSize: 15,
            //               borderRadius: 20.r,
            //               color: AppColor.greenColor.withOpacity(0.1),
            //             ),
            //           ],
            //         ),
            //       ),
            //       const Divider(),
            //       CustomCard(
            //         padding: EdgeInsets.zero,
            //         elevation: 0,
            //         child: Row(
            //           children: [
            //             CustomText(
            //               text: 'عدد ايام الغياب',
            //               fontSize: 16,
            //               fontWeight: FontWeight.bold,
            //               color: AppColor.redColor,
            //             ),
            //             const Spacer(),
            //             CustomButton(
            //               text: '10',
            //               onTap: () {},
            //               textColor: AppColor.redColor,
            //               width: 100.w,
            //               fontSize: 14.h,
            //               borderRadius: 20.r,
            //               color: AppColor.redColor.withOpacity(0.1),
            //             ),
            //           ],
            //         ),
            //       ),
            //     ],
            //   ),
            // )
          ],
        ),
      ),
    );
  }
}
