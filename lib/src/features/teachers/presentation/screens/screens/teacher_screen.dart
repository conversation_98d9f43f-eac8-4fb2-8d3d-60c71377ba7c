import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../../core/screens/layout_screen.dart';
import '../../../../../core/utils/extensions.dart';

import '../../../../../core/router/app_router.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/widgets/custom_text.dart';

import '../../../../home/<USER>/teacher_controller.dart';

class TeachersScreen extends StatelessWidget {
  const TeachersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(TeacherController());
    // Refresh the data when the screen is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.teachers.clear();
      controller.getTeachers();
    });

    return LayoutScreen(
      title: 'Teachers'.tr,
      body: Obx(
        () {
          var teachers = controller.teachers;

          return ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: teachers.length,
            itemBuilder: (context, index) {
              return SmartFingersListTile(
                title: teachers[index].name,
                listTileTitleAlignment: ListTileTitleAlignment.titleHeight,
                subtitle: CustomText(
                    text: teachers[index].job, textAlign: TextAlign.start),
                minTileHeight: 84.h,
                trailing: Container(
                  height: 53,
                  width: 53,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: AppColor.primaryColor.withOpacity(0.15),
                  ),
                  padding: EdgeInsets.all(8.h),
                  child: SmartFingersImage(imageUrl: AppAssets.cup2),
                ),
                leading: SmartFingersImage(
                  imageUrl: controller.teachers[index].image,
                ),
                onTap: () => Get.toNamed(
                  AppRouter.teacherAchievementScreen,
                  arguments: controller.teachers[index],
                ),
              );
            },
            separatorBuilder: (BuildContext context, int index) {
              return 16.verticalSpace;
            },
          );
        },
      ),
      controller: controller,
    );
  }
}
