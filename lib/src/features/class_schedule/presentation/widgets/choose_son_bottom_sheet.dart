import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/user.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../auth/services/auth_service.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../core/widgets/custom_text.dart';

class ChooseSonBottomSheet extends StatelessWidget {
  const ChooseSonBottomSheet({
    super.key,
    required this.sons,
    required this.selectedChildId,
    this.onTap,
    this.onBack,
  });

  final List<User> sons;
  final int? selectedChildId;
  final void Function(User son)? onTap;
  final void Function()? onBack;

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      title: 'Change account'.tr,
      body: Padding(
        padding: const EdgeInsets.all(0.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (selectedChildId != null)
              InkWell(
                onTap: () {
                  AuthService.instance.selectedSon.value = null;
                  AuthService.instance.user.value =
                      AuthService.instance.parentUser.value;
                  AuthService.instance.token.value = box.read("token");
                  AuthService.instance.parentUser.value = null;
                  Get.back();
                },
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    "العودة لحساب ولي الامر",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Get.theme.primaryColor,
                        decoration: TextDecoration.underline),
                  ),
                ),
              ),
            ListView.builder(
              shrinkWrap: true,
              itemCount: sons.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  child: Obx(
                    () {
                      bool isSelected = selectedChildId == sons[index].id;
                      return Column(
                        children: [
                          ListTile(
                            trailing: !isSelected
                                ? null
                                : Icon(
                                    Icons.check,
                                    color: Colors.blue,
                                  ),
                            title: CustomText(
                              textAlign: TextAlign.start,
                              text: sons[index].name ?? "",
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            onTap: () {
                              if (AuthService.instance.user.value?.role ==
                                  Roles.Guardian) {
                                AuthService.instance.parentUser.value =
                                    AuthService.instance.user.value;
                              }
                              AuthService.instance.user.value = sons[index];
                              AuthService.instance.token.value =
                                  sons[index].token;
                              AuthService.instance.selectedSon.value =
                                  sons[index];
                              Get.back();
                            },
                          ),
                          if (index != sons.length - 1)
                            Divider(
                              height: 0.5,
                              color: AppColor.greyBorder,
                            ),
                        ],
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
