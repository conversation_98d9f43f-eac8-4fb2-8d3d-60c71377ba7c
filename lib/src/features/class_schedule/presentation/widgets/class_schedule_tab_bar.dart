import 'package:flutter/material.dart';
import '../../../../core/data/models/class_schedule.dart';
import 'class_schedule_tile.dart';
import 'day_tab.dart';

import '../../../../core/utils/extensions.dart';

class ClassScheduleTabBar extends StatefulWidget {
  const ClassScheduleTabBar({
    super.key,
    required this.classSchedule,
  });
  final List<ClassSchedule> classSchedule;

  @override
  State<ClassScheduleTabBar> createState() => _ClassScheduleTabBarState();
}

class _ClassScheduleTabBarState extends State<ClassScheduleTabBar> {
  int index = 0;
  // print(widget.classSchedule);
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: widget.classSchedule.length,
      child: Column(
        children: [
          SmartFingersTabBar(
            labelPadding: const EdgeInsets.symmetric(
              horizontal: 4,
            ),
            padding: const EdgeInsets.only(
              top: 24,
              right: 8,
              left: 8,
              bottom: 24,
            ),
            dividerHeight: 0,
            indicatorWeight: 0,
            indicator: const BoxDecoration(),
            isScrollable: true,
            tabAlignment: TabAlignment.center,
            onTap: (value) {
              setState(() {
                index = value;
              });
            },
            tabs: widget.classSchedule.map(
              (e) {
                var selected = index == widget.classSchedule.indexOf(e);
                return DayTab(text: e.day, selected: selected);
              },
            ).toList(),
          ),
          Expanded(
            child: TabBarView(
              physics: const NeverScrollableScrollPhysics(),
              children: widget.classSchedule.map((e) {
                return ListView.separated(
                  padding: const EdgeInsets.only(
                    right: 16,
                    left: 16,
                    bottom: 16,
                  ),
                  itemCount: e.lesson.length,
                  itemBuilder: (context, index) {
                    return ClassScheduleTile(
                      title: e.lesson[index].title,
                      subtitle: e.lesson[index].time,
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const SizedBox(
                      height: 16,
                    );
                  },
                );
              }).toList(),
            ),
          )
        ],
      ),
    );
  }
}
