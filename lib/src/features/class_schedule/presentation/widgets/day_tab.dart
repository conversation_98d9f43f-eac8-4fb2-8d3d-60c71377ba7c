import 'package:flutter/material.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_text.dart';

class DayTab extends StatelessWidget {
  const DayTab({
    super.key,
    required this.selected,
    required this.text,
  });

  final bool selected;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100),
        gradient: selected
            ? LinearGradient(
                colors: AppColor.blueGradient,
              )
            : LinearGradient(
                colors: [AppColor.whiteColor, AppColor.whiteColor],
              ),
        border: selected
            ? Border.all(color: Colors.transparent)
            : Border.all(color: Theme.of(context).primaryColor),
      ),
      child: CustomText(
        text: text,
        color: selected ? Colors.white : Theme.of(context).primaryColor,
        textAlign: TextAlign.center,
        fontWeight: FontWeight.bold,
        fontSize: 12,
        maxLine: 2,
      ),
    );
  }
}
