import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/extensions.dart';

import '../../../../core/widgets/custom_text.dart';

class ClassScheduleTile extends StatelessWidget {
  final String? title;
  final String? subtitle;

  const ClassScheduleTile({
    super.key,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return SmartFingersListTile(
      title: title ?? '',
      onTap: () {},
      leading: SmartFingersImage(
        imageUrl: AppAssets.schedule,
        height: 56.h,
        width: 56.w,
      ),
      minTileHeight: 75.h,
      listTileTitleAlignment: ListTileTitleAlignment.top,
      subtitle: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        verticalDirection: VerticalDirection.up,
        children: [
          Row(children: [
            SmartFingersImage(imageUrl: AppAssets.clock),
            SizedBox(width: 5.w),
          ]),
          CustomText(
            text: subtitle ?? '',
          )
        ],
      ),
    );
    // CustomListTile(
    //   leadingIcon: AppAssets.schedule,
    //   title: title,
    //   subtitle: subtitle,
    //   subtitleIcon: AppAssets.clock,
    // );
  }
}
