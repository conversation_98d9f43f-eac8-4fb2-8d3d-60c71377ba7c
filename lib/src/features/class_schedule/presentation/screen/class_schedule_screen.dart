import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../controller/class_schedule_controller.dart';
import '../widgets/class_schedule_tab_bar.dart';

class ClassScheduleScreen extends StatelessWidget {
  const ClassScheduleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(ClassScheduleController());
    return LayoutScreen(
      title: 'Class Schedule'.tr,
      controller: ClassScheduleController.instance,
      body: Obx(
        () => ClassScheduleTabBar(
          classSchedule: ClassScheduleController.instance.classSchedule.value,
        ),
      ),
    );
  }
}
