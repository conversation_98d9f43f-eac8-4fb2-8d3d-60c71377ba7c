import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/class_schedule.dart';

class ClassScheduleController extends BaseController {
  static ClassScheduleController get instance => Get.find();
  Rx<List<ClassSchedule>> classSchedule = Rx<List<ClassSchedule>>([]);

  @override
  void onInit() {
    fetchData();
    super.onInit();
  }

  Future<void> fetchData() async {
    print(await checkConnectivity());
    callApi(
      () async {
        classSchedule.value = await apiProvider.getClassSchedule();
        setPageStatus(PageStatus.loaded);
      },
    );
  }
}
