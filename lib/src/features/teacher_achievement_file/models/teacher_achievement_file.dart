class TeacherAchievementFile {
  final int? id;
  final int? teacherId;
  final String? teacherName;
  final String? fileUrl;
  final String? createdAt;

  TeacherAchievementFile({
    this.id,
    this.teacherId,
    this.teacherName,
    this.fileUrl,
    this.createdAt,
  });

  factory TeacherAchievementFile.fromJson(Map<String, dynamic> json) {
    return TeacherAchievementFile(
      id: json['id'],
      teacherId: json['teacher_id'],
      teacherName: json['teacher_name'],
      fileUrl: json['file_url'],
      createdAt: json['created_at'],
    );
  }
}
