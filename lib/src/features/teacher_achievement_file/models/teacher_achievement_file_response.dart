import 'teacher_achievement_file.dart';

class TeacherAchievementFileResponse {
  final TeacherAchievementFile? data;

  TeacherAchievementFileResponse({this.data});

  factory TeacherAchievementFileResponse.fromJson(Map<String, dynamic> json) {
    return TeacherAchievementFileResponse(
      data:
          json['data'] != null
              ? TeacherAchievementFile.fromJson(json['data'])
              : null,
    );
  }
}

class TeacherAchievementFileUploadResponse {
  final String message;
  final TeacherAchievementFile? data;

  TeacherAchievementFileUploadResponse({required this.message, this.data});

  factory TeacherAchievementFileUploadResponse.fromJson(
    Map<String, dynamic> json,
  ) {
    return TeacherAchievementFileUploadResponse(
      message: json['message'] ?? '',
      data:
          json['data'] != null
              ? TeacherAchievementFile.fromJson(json['data'])
              : null,
    );
  }
}
