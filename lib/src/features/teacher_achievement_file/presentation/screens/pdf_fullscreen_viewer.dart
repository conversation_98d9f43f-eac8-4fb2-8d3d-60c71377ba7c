import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_text.dart';

class PdfFullscreenViewer extends StatefulWidget {
  final String pdfUrl;
  final String title;

  const PdfFullscreenViewer({
    Key? key,
    required this.pdfUrl,
    this.title = '',
  }) : super(key: key);

  @override
  State<PdfFullscreenViewer> createState() => _PdfFullscreenViewerState();
}

class _PdfFullscreenViewerState extends State<PdfFullscreenViewer> {
  final RxBool isFullScreen = false.obs;
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 0.obs;

  @override
  void initState() {
    super.initState();
    // Start in fullscreen mode by default
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Enter full screen - hide status bar but maintain current orientation
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      isFullScreen.value = true;
    });
  }

  @override
  void dispose() {
    // Restore system UI when leaving the screen
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: SystemUiOverlay.values,
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: isFullScreen.value
            ? null
            : AppBar(
                title: Text(widget.title.isNotEmpty
                    ? widget.title
                    : 'PDF Document Viewer'.tr),
                leading: IconButton(
                  icon: Icon(Icons.arrow_back),
                  onPressed: () => Get.back(),
                ),
              ),
        body: Stack(
          children: [
            // PDF Viewer
            PDF(
              swipeHorizontal: true,
              enableSwipe: true,
              pageFling: true,
              pageSnap: true,
              autoSpacing: true,
              onPageChanged: (page, total) {
                currentPage.value = page ?? 1;
                if (total != null) {
                  totalPages.value = total;
                }
              },
            ).cachedFromUrl(
              widget.pdfUrl,
              placeholder: (progress) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      value: progress / 100,
                      color: AppColor.primaryColor,
                    ),
                    SizedBox(height: 16.h),
                    CustomText(
                      text:
                          '${'Loading PDF Document...'.tr} ${progress.toInt()}%',
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
              ),
              errorWidget: (error) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 48.r),
                    SizedBox(height: 16.h),
                    CustomText(
                      text: '${'Failed to load PDF Document'.tr}: $error',
                      fontSize: 14.sp,
                      color: Colors.red,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            // Control buttons (always visible and with higher z-index)
            Positioned(
              top: 16.r,
              right: 16.r,
              child: Row(
                children: [
                  // Fullscreen toggle button
                  Material(
                    color: Colors.transparent,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(180),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: IconButton(
                        icon: Icon(
                          isFullScreen.value
                              ? Icons.fullscreen_exit
                              : Icons.fullscreen,
                          color: Colors.white,
                          size: 24,
                        ),
                        onPressed: () {
                          isFullScreen.value = !isFullScreen.value;
                          if (isFullScreen.value) {
                            SystemChrome.setEnabledSystemUIMode(
                                SystemUiMode.immersiveSticky);
                          } else {
                            SystemChrome.setEnabledSystemUIMode(
                              SystemUiMode.manual,
                              overlays: SystemUiOverlay.values,
                            );
                          }
                        },
                        tooltip: isFullScreen.value
                            ? 'Exit Fullscreen Mode'.tr
                            : 'Fullscreen Mode'.tr,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  // Close button
                  Material(
                    color: Colors.transparent,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(180),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 24,
                        ),
                        onPressed: () {
                          // Ensure system UI is restored before navigating back
                          SystemChrome.setEnabledSystemUIMode(
                            SystemUiMode.manual,
                            overlays: SystemUiOverlay.values,
                          );
                          Get.back();
                        },
                        tooltip: 'Close'.tr,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Page indicator
            if (totalPages.value > 0)
              Positioned(
                bottom: 16.r,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.r,
                      vertical: 8.r,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withAlpha(128),
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: CustomText(
                      text: '${currentPage.value} / ${totalPages.value}',
                      fontSize: 14.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }
}
