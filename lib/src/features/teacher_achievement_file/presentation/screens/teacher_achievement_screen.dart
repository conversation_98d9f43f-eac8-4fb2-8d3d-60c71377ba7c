import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/buttons/custom_button.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../controller/teacher_achievement_controller.dart';
import 'pdf_fullscreen_viewer.dart';

class TeacherAchievementScreen extends StatelessWidget {
  TeacherAchievementScreen({super.key});

  final TeacherAchievementController controller =
      Get.put(TeacherAchievementController());

  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: 'Teacher Achievements'.tr,
      withBackground: false,
      controller: controller,
      body: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Header section with instructions
            _buildHeaderSection(),
            SizedBox(height: 16.h),

            // File management section
            _buildFileManagementSection(),
            SizedBox(height: 24.h),

            // PDF viewer or empty state
            Expanded(
              child: _buildPdfViewerSection(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.blue.withAlpha(25),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColor.primaryColor.withAlpha(76)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: 'Achievement File'.tr,
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColor.primaryColor,
          ),
          SizedBox(height: 8.h),
          CustomText(
            text: 'Upload Achievement File'.tr,
            fontSize: 14.sp,
            color: Colors.grey[700],
          ),
        ],
      ),
    );
  }

  Widget _buildFileManagementSection() {
    return Obx(() {
      final hasFile = controller.achievementFile.value != null;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File selection section
          if (!hasFile || controller.selectedFile.value != null)
            _buildFileSelectionSection(),

          // Action buttons
          SizedBox(height: 16.h),
          Row(
            children: [
              // Add/Update button
              Expanded(
                child: CustomButton(
                  text: 'Upload Achievement File'.tr,
                  onTap: controller.selectedFile.value != null
                      ? () => controller.uploadAchievementFile()
                      : () => controller.pickPdfFile(),
                  color: AppColor.primaryColor,
                  textColor: Colors.white,
                  height: 50.h,
                ),
              ),

              // Delete button (only show if file exists)
              if (hasFile && controller.selectedFile.value == null) ...[
                SizedBox(width: 16.w),
                Expanded(
                  child: CustomButton(
                    text: 'Delete File'.tr,
                    onTap: () => _showDeleteConfirmation(),
                    color: Colors.red,
                    textColor: Colors.white,
                    height: 50.h,
                  ),
                ),
              ],

              // Cancel selection button (only show if file is selected)
              if (controller.selectedFile.value != null) ...[
                SizedBox(width: 16.w),
                Expanded(
                  child: CustomButton(
                    text: 'Cancel'.tr,
                    onTap: () {
                      controller.selectedFile.value = null;
                      controller.selectedFileName.value = '';
                    },
                    color: Colors.grey,
                    textColor: Colors.white,
                    height: 50.h,
                  ),
                ),
              ],
            ],
          ),
        ],
      );
    });
  }

  Widget _buildFileSelectionSection() {
    return Obx(() {
      final hasSelectedFile = controller.selectedFile.value != null;
      final fileName = controller.selectedFileName.value;

      return Container(
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: hasSelectedFile
              ? Colors.green.withAlpha(25)
              : Colors.grey.withAlpha(25),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: hasSelectedFile
                ? Colors.green.withAlpha(76)
                : Colors.grey.withAlpha(76),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.picture_as_pdf,
                  color: hasSelectedFile ? Colors.green : Colors.grey,
                  size: 24.r,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: CustomText(
                    text:
                        hasSelectedFile ? fileName : 'Please select a file'.tr,
                    fontSize: 14.sp,
                    color:
                        hasSelectedFile ? Colors.green[700] : Colors.grey[700],
                    fontWeight:
                        hasSelectedFile ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                if (!hasSelectedFile)
                  CustomButton(
                    text: 'Select PDF File'.tr,
                    onTap: () => controller.pickPdfFile(),
                    color: AppColor.primaryColor,
                    textColor: Colors.white,
                    height: 40.h,
                    width: 120.w,
                  ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildPdfViewerSection() {
    return Obx(() {
      final fileUrl = controller.achievementFile.value?.fileUrl;

      if (controller.isUploading.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: AppColor.primaryColor),
              SizedBox(height: 16.h),
              CustomText(
                text: 'Uploading...'.tr,
                fontSize: 16.sp,
                color: Colors.grey[700],
              ),
            ],
          ),
        );
      }

      if (fileUrl == null) {
        return EmptyWidget(
          message: 'No achievement file found'.tr,
        );
      }

      return Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withAlpha(76)),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: PDF(
                swipeHorizontal: true,
                enableSwipe: true,
                pageFling: true,
                pageSnap: true,
                autoSpacing: true,
              ).cachedFromUrl(
                fileUrl,
                placeholder: (progress) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        value: progress / 100,
                        color: AppColor.primaryColor,
                      ),
                      SizedBox(height: 16.h),
                      CustomText(
                        text: '${'Loading PDF... '.tr} ${progress.toInt()}%',
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                    ],
                  ),
                ),
                errorWidget: (error) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: Colors.red, size: 48.r),
                      SizedBox(height: 16.h),
                      CustomText(
                        text: '${'Failed to load PDF'.tr}: $error',
                        fontSize: 14.sp,
                        color: Colors.red,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Fullscreen button
          Positioned(
            top: 16.r,
            right: 16.r,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(128),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.fullscreen,
                  color: Colors.white,
                ),
                onPressed: () {
                  // Open fullscreen PDF viewer
                  Get.to(
                    () => PdfFullscreenViewer(
                      pdfUrl: fileUrl,
                      title: 'Achievement File'.tr,
                    ),
                  );
                },
                tooltip: 'Fullscreen'.tr,
              ),
            ),
          ),
        ],
      );
    });
  }

  void _showDeleteConfirmation() {
    final fileId = controller.achievementFile.value?.id;
    if (fileId == null) return;

    Get.defaultDialog(
      title: 'Delete'.tr,
      middleText: 'Are you sure you want to delete this file?'.tr,
      textConfirm: 'Delete'.tr,
      textCancel: 'Cancel'.tr,
      confirmTextColor: Colors.white,
      cancelTextColor: AppColor.primaryColor,
      buttonColor: Colors.red,
      onConfirm: () {
        Get.back();
        controller.deleteAchievementFile(fileId);
      },
      onCancel: () => Get.back(),
    );
  }
}
