import 'dart:io';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/utils/helper_function.dart';
import '../models/teacher_achievement_file.dart';

class TeacherAchievementController extends BaseController {
  // Observable variables
  final Rx<TeacherAchievementFile?> achievementFile =
      Rx<TeacherAchievementFile?>(null);
  final Rx<File?> selectedFile = Rx<File?>(null);
  final RxString selectedFileName = ''.obs;
  final RxBool isUploading = false.obs;

  @override
  void onInit() {
    super.onInit();

    // Load achievement file data
    fetchAchievementFile();
  }

  /// Fetches the teacher's achievement file from the API
  Future<void> fetchAchievementFile() async {
    setPageStatus(PageStatus.loading);

    try {
      final response = await apiProvider.getTeacherAchievementFile();
      achievementFile.value = response.data;
      setPageStatus(PageStatus.loaded);
    } catch (e) {
      handleError(e);
    }
  }

  /// Refreshes the achievement file data
  Future<void> refreshData() async {
    await fetchAchievementFile();
  }

  /// Opens file picker to select a PDF file
  Future<void> pickPdfFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.first.path != null) {
        selectedFile.value = File(result.files.first.path!);
        selectedFileName.value = result.files.first.name;
      }
    } catch (e) {
      HelperFunction.showErrorMessage('Error selecting file: ${e.toString()}');
    }
  }

  /// Uploads the selected achievement file to the server
  Future<void> uploadAchievementFile() async {
    // Validate file selection
    if (selectedFile.value == null) {
      HelperFunction.showErrorMessage('Please select a PDF file');
      return;
    }

    isUploading.value = true;

    try {
      final response =
          await apiProvider.uploadTeacherAchievementFile(selectedFile.value!);

      // Update the file in the UI
      if (response.data != null) {
        achievementFile.value = response.data;
      }

      // Reset the selected file
      selectedFile.value = null;
      selectedFileName.value = '';

      // Show success message
      final successMessage = response.message.isNotEmpty
          ? response.message
          : 'File uploaded successfully';
      HelperFunction.showSuccessMessage(successMessage);

      // Refresh data to ensure UI is up to date
      await fetchAchievementFile();
    } catch (e) {
      handleError(e);
    } finally {
      isUploading.value = false;
    }
  }

  /// Deletes an achievement file by ID
  Future<void> deleteAchievementFile(int id) async {
    setPageStatus(PageStatus.loading);

    try {
      final response = await apiProvider.deleteTeacherAchievementFile(id);

      // Remove the file from the UI
      achievementFile.value = null;

      // Show success message
      final successMessage =
          response.message != null && response.message!.isNotEmpty
              ? response.message!
              : 'File deleted successfully';
      HelperFunction.showSuccessMessage(successMessage);

      setPageStatus(PageStatus.loaded);
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void onClose() {
    // Clean up resources if needed
    selectedFile.value = null;
    super.onClose();
  }
}
