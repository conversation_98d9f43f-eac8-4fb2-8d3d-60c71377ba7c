import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../../../core/data/models/user.dart';
import '../../../core/data/providers/api_provider.dart';
import '../../../core/router/app_router.dart';
import '../../../core/utils/app_consts.dart';
import '../../../core/utils/helper_function.dart';
import '../models/login_model.dart';
import '../../../core/data/models/section_with_subjects.dart';
import '../../notification/services/notification_service.dart';

import '../../../core/utils/extensions.dart';

class AuthService extends GetxService {
  static AuthService get instance => Get.find();

  var apiProvider = ApiProvider(AppConsts.authenticatedDio);

  var user = Rx<User?>(null);

  var parentUser = Rx<User?>(null);

  var selectedSon = Rx<User?>(null);

  var token = Rx<String?>(null);

  RxList<SectionWithSubjects> sectionWithSubjects =
      RxList<SectionWithSubjects>([]);

  RxBool isLoading = false.obs;
  RxnString loginErrorMessage = RxnString();

  final formKey = GlobalKey<FormState>();

  AuthService();

  Future<AuthService> init() async {
    token.value = box.read('token');
    getSectionsWithSubjects();
    token.listen((value) {
      if (value != null) {
        getSectionsWithSubjects();
      }
      NotificationService.instance.registerDevice();
    });
    user.listen((value) {
      NotificationService.instance.notifications.clear();
      NotificationService.instance.getNotifications(lastId: null);
    });
    return this;
  }

  Future<void> getSectionsWithSubjects() async {
    if (token.value == null) return;
    var apiProvider = ApiProvider(AppConsts.authenticatedDio);
    try {
      var response = await apiProvider.getSectionsWithSubjects();
      sectionWithSubjects.value = response;
    } catch (e) {
      printLog(e);
    }
  }

  Future<void> getProfile() async {
    if (token.value == null) return;
    var apiProvider = ApiProvider(AppConsts.authenticatedDio);
    try {
      var response = await apiProvider.profile();
      user.value = response;
    } catch (e) {
      printLog(e);
    }
  }

  Future<void> login(LoginModel loginModel) async {
    try {
      var t = await FirebaseMessaging.instance.getAPNSToken();
      print("token: $t");
      String? deviceToken =
          (await FirebaseMessaging.instance.getToken()) ?? "simulatorToken";
      loginModel.deviceToken = deviceToken;
    } catch (e) {
      printLog(e);
      loginModel.deviceToken = "simulatorToken";
    }

    try {
      var response = await apiProvider.login(loginModel);
      user.value = response.user;
      token.value = response.accessToken;
      await box.write('token', response.accessToken);

      await getSectionsWithSubjects();
      Get.offAllNamed(AppRouter.homeScreen);
    } catch (e) {
      if (e is DioException) {
        HelperFunction.showErrorMessage(e.response?.data['message']);
      }
      printLog(e);
    }
  }

  Future<void> forgetPassword({required String email}) async {
    try {
      var response = await apiProvider.forgetPassword({
        'email': email,
      });
      Get.toNamed(AppRouter.forgetCodePasswordScreen, arguments: email);
      HelperFunction.showSuccessMessage(response.message ?? "");
    } catch (e) {
      printLog(e);
    }
  }

  Future<void> checkResetPasswordCode(
      {required String code, required String email}) async {
    try {
      await apiProvider.checkResetPasswordCode({
        'code': code,
        'email': email,
      });
      Get.toNamed(AppRouter.resetPasswordScreen, arguments: {
        'code': code,
        'email': email,
      });
      // HelperFunction.showSuccessMessage(response.message ?? "");
    } catch (e) {
      printLog(e);
      if (e is DioException) {
        HelperFunction.showErrorMessage(e.response?.data['message']);
      } else {
        HelperFunction.showErrorMessage(e.toString());
      }
    }
  }

  Future<void> resetPassword({
    required String code,
    required String email,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    try {
      var response = await apiProvider.resetPassword({
        'code': code,
        'email': email,
        'password': newPassword,
        'password_confirmation': newPasswordConfirmation,
      });
      Get.toNamed(AppRouter.loginScreen);
      HelperFunction.showSuccessMessage(response.message ?? "");
    } catch (e) {
      printLog(e);
      HelperFunction.showErrorMessage(e.toString());
    }
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    try {
      var response = await apiProvider.changePassword({
        'current_password': currentPassword,
        'new_password': newPassword,
        'new_password_confirmation': newPasswordConfirmation,
      });
      Get.back();
      HelperFunction.showSuccessMessage(response.message ?? "");
    } catch (e) {
      printLog(e);
      if (e is DioException) {
        HelperFunction.showErrorMessage(e.response?.data['message']);
      } else {
        HelperFunction.showErrorMessage(e.toString());
      }
    }
  }

  Future<void> logOut() async {
    await box.remove('token');
    Get.offAllNamed(AppRouter.loginScreen);
    user.value = null;
    token.value = null;
    apiProvider.logout();
  }
}
