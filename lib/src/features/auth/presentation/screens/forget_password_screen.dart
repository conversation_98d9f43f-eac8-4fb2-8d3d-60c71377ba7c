// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../services/auth_service.dart';
import 'auth_layout_screen.dart';

class ForgetPasswordScreen extends StatelessWidget {
  const ForgetPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String email = '';
    return AuthLayoutScreen(
      title: 'Reset Password',
      body: Form(
        key: formKey,
        child: Column(
          children: [
            CustomTextField(
              hint: 'Enter Email'.tr,
              filled: true,
              filledColor: AppColor.whiteColor,
              validator: ValidationBuilder(
                requiredMessage: "This field is required".tr,
              ).email().build(),
              onSaved: (value) {
                email = value!;
              },
              keyboardType: TextInputType.emailAddress,
            ),
            SubmitButton(
              margin: EdgeInsets.zero,
              text: 'Send'.tr,
              color: AppColor.green2Color,
              onSubmit: () async {
                await AuthService.instance.forgetPassword(email: email);
              },
              formKey: formKey,
            )
          ],
        ),
      ),
    );
  }
}
