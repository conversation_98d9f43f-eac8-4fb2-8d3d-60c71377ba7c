import 'package:flutter/material.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../services/auth_service.dart';
import 'auth_layout_screen.dart';

class ResetPasswordScreen extends StatelessWidget {
  const ResetPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String code = Get.arguments['code'];
    String email = Get.arguments['email'];
    String password = '';
    String passwordConfirmation = '';
    return AuthLayoutScreen(
      title: 'Reset Password',
      body: Form(
        key: formKey,
        child: Column(
          children: [
            CustomTextField(
              hint: 'New password'.tr,
              filled: true,
              filledColor: AppColor.whiteColor,
              keyboardType: TextInputType.number,
              validator: ValidationBuilder(
                requiredMessage: "This field is required".tr,
              ).required("This field is required".tr).build(),
              obscureText: true,
              onSaved: (value) {
                password = value!;
              },
            ),
            CustomTextField(
              hint: 'New password confirmation'.tr,
              filled: true,
              filledColor: AppColor.whiteColor,
              keyboardType: TextInputType.number,
              validator: ValidationBuilder(
                requiredMessage: "This field is required".tr,
              ).required("This field is required".tr).build(),
              obscureText: true,
              onSaved: (value) {
                passwordConfirmation = value!;
              },
            ),
            SubmitButton(
              text: 'Change Password'.tr,
              color: AppColor.green2Color,
              onSubmit: () async {
                await AuthService.instance.resetPassword(
                    email: email,
                    code: code,
                    newPassword: password,
                    newPasswordConfirmation: passwordConfirmation);
              },
              formKey: formKey,
            )
          ],
        ),
      ),
    );
  }
}
