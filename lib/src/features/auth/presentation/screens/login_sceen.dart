// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../models/login_model.dart';
import '../../services/auth_service.dart';
import 'auth_layout_screen.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var formKey = GlobalKey<FormState>();
    LoginModel loginModel = LoginModel();
    return AuthLayoutScreen(
      title: 'Login',
      body: Form(
        key: formKey,
        child: Column(
          children: [
            CustomTextField(
              hint: 'Enter Email or Academic Number'.tr,
              filled: true,
              filledColor: AppColor.whiteColor,
              prefixIcon: SvgPicture.asset(AppAssets.userImage),
              validator: ValidationBuilder(
                requiredMessage: "This field is required".tr,
              ).required("This field is required".tr).build(),
              keyboardType: TextInputType.text,
              onSaved: (value) {
                loginModel.email = value;
              },
            ),
            CustomTextField(
              hint: 'Enter Password'.tr,
              filled: true,
              obscureText: true,
              filledColor: AppColor.whiteColor,
              prefixIcon: SvgPicture.asset(AppAssets.passwordImage),
              validator: ValidationBuilder(
                requiredMessage: "This field is required".tr,
              ).minLength(8).build(),
              keyboardType: TextInputType.visiblePassword,
              onSaved: (value) {
                loginModel.password = value;
              },
            ),
            InkWell(
              onTap: () => Get.toNamed(AppRouter.forgetPasswordScreen),
              child: Align(
                alignment: AlignmentDirectional.centerStart,
                child: Text(
                  'Forget Password?'.tr,
                  style: TextStyle(
                      decoration: TextDecoration.underline,
                      decorationColor: AppColor.primaryColor,
                      decorationThickness: 1.0,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColor.primaryColor),
                ),
              ),
            ),
            SubmitButton(
              margin: const EdgeInsets.only(
                top: 16,
              ),
              text: 'Login'.tr,
              color: AppColor.green2Color,
              onSubmit: () async {
                await AuthService.instance.login(loginModel);
              },
              formKey: formKey,
            )
          ],
        ),
      ),
    );
  }
}
