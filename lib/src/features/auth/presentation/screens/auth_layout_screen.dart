import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_text.dart';
import '../widgets/language_switch.dart';

class AuthLayoutScreen extends StatelessWidget {
  const AuthLayoutScreen({super.key, required this.body, required this.title});
  final Widget body;
  final String title;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.primaryColor,
      extendBodyBehindAppBar: true,
      extendBody: true,
      appBar: AppBar(
        // backgroundColor: Colors.transparent,
        iconTheme: IconThemeData(color: AppColor.whiteColor),
        elevation: 0,
        forceMaterialTransparency: true,
        // toolbarOpacity: 0,
        scrolledUnderElevation: 0,
        primary: true,
      ),
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.center,
                height: 0.3.sh,
                child: SvgPicture.asset(AppAssets.whiteLogo),
              ),
              Container(
                height: 0.7.sh,
                decoration: BoxDecoration(
                  color: AppColor.amber2Color,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(50.r),
                    topLeft: Radius.circular(50.r),
                  ),
                ),
                child: Padding(
                    padding: EdgeInsets.only(right: 20.w, left: 20.w),
                    child: SafeArea(
                      top: false,
                      child: Column(
                        children: [
                          32.verticalSpace,
                          CustomText(
                            text: title,
                            color: AppColor.primaryColor,
                            fontSize: 20.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          24.verticalSpace,
                          body,
                          32.verticalSpace,
                          const LanguageSwitch(),
                        ],
                      ),
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
