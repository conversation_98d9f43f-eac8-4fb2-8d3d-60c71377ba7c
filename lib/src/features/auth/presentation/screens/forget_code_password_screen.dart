// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../services/auth_service.dart';
import 'auth_layout_screen.dart';

class ForgetCodePasswordScreen extends StatelessWidget {
  const ForgetCodePasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String code = '';
    String email = Get.arguments;
    return AuthLayoutScreen(
      title: 'Reset Password',
      body: Form(
        key: formKey,
        child: Column(
          children: [
            Directionality(
              textDirection: TextDirection.ltr,
              child: PinCodeTextField(
                appContext: context,
                length: 6,
                obscureText: true,
                textStyle: const TextStyle(
                  color: Colors.black,
                ),
                animationCurve: Curves.bounceIn,
                validator: ValidationBuilder(
                  requiredMessage: "This field is required".tr,
                ).required("This field is required".tr).build(),
                // enableActiveFill: true,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(8),
                  fieldHeight: 50,
                  fieldWidth: 40,
                  inactiveColor: Colors.grey[600],
                ),
                onSaved: (newValue) {
                  code = newValue ?? "";
                },

                cursorColor: Colors.black,
              ),
            ),
            SubmitButton(
              text: 'Check'.tr,
              color: AppColor.green2Color,
              onSubmit: () async {
                await AuthService.instance
                    .checkResetPasswordCode(email: email, code: code);
              },
              formKey: formKey,
            )
          ],
        ),
      ),
    );
  }
}
