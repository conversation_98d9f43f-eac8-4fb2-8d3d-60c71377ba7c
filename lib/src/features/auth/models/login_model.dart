import '../../../core/data/models/user.dart';

class LoginModel {
  String? email;
  String? password;
  String? deviceToken;

  LoginModel({this.email, this.password, this.deviceToken});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'device_token': deviceToken,
    };
  }
}

class GetLoginModel {
  User? user;
  String? accessToken;
  String? tokenType;
  String? themeColor;

  GetLoginModel({this.user, this.accessToken, this.tokenType, this.themeColor});

  factory GetLoginModel.fromJson(Map<String, dynamic> json) {
    return GetLoginModel(
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      accessToken: json['access_token'],
      tokenType: json['token_type'],
      themeColor: json['theme_color'],
    );
  }
}
