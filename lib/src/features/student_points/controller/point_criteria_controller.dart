import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/models/point_criteria.dart';
import '../../../core/utils/helper_function.dart';

class PointCriteriaController extends BaseController {
  static PointCriteriaController get instance => Get.find();
  RxList<PointCriteria> criterias = RxList<PointCriteria>([]);
  @override
  void onInit() {
    getPointCriterias();
    super.onInit();
  }

  void getPointCriterias() {
    callApi(
      () async {
        var response = await apiProvider.getPointCriterias();
        criterias.value = response;
        updateEmptyStatus(response);
        criterias.refresh();
      },
    );
  }

  Future<void> addPointCriteria(Map<String, dynamic> data) async {
    await callApi(
      () async {
        var response = await apiProvider.createPointCriteria(data);
        criterias.insert(0, response.data!);
        updateEmptyStatus(criterias);
        criterias.refresh();
        Get.back();
        HelperFunction.showSuccessMessage(response.message!);
      },
      withLoading: false,
    );
  }

  Future<void> delete(int id) async {
    await callApi(
      () async {
        var response = await apiProvider.deletePointCriteria(id);
        criterias.removeWhere((element) => element.id == id);
        updateEmptyStatus(criterias);
        criterias.refresh();
        HelperFunction.showSuccessMessage(response.message!);
      },
      withLoading: false,
    );
  }
}
