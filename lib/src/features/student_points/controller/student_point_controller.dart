import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/student_point.dart';
import '../../../core/data/models/user.dart';
import '../../../core/mixin/paginator_mixin.dart';
import '../../../core/utils/helper_function.dart';

class StudentPointController extends BaseController with PaginatorMixin {
  static StudentPointController get instance => Get.find();
  RxList<StudentPoint> points = RxList<StudentPoint>([]);
  RxList<User> students = RxList<User>([]);
  RxInt sectionId = RxInt(0);
  RxInt pointSum = RxInt(0);

  Future<void> getPoints(int sectionId, {required int page}) async {
    this.sectionId.value = sectionId;
    if (page == 1) {
      setPageStatus(PageStatus.loading);
    }
    await callApi(() async {
      var response = await apiProvider.getStudentPoints(sectionId: sectionId);

      if (page == 1) {
        points.value = response.data ?? [];
        points.refresh();
        lastPage.value = response.lastPage ?? 1;
        totalItems.value = response.itemsCount ?? 0;
        pointSum.value = response.pointSum ?? 0;
        if (totalItems.value == 0) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      } else {
        points.addAll(response.data ?? []);
        points.refresh();
      }
    }, withLoading: false);

    getStudents();
  }

  Future<void> getStudents() async {
    await callApi(
      () async {
        var response =
            await apiProvider.getSectionStudents(sectionId: sectionId.value);
        students.value = response;
        students.refresh();
      },
      withLoading: false,
    );
  }

  Future<void> addPoint(Map<String, dynamic> data) async {
    await callApi(
      () async {
        var response = await apiProvider.createStudentPoint(data);
        points.insert(0, response.data!);
        updateEmptyStatus(points);
        points.refresh();
        Get.back();
        HelperFunction.showSuccessMessage(response.message!);
      },
      withLoading: false,
    );
  }

  Future<void> delete(int id) async {
    await callApi(
      () async {
        var response = await apiProvider.deleteStudentPoint(id);
        points.removeWhere((element) => element.id == id);
        updateEmptyStatus(points);
        points.refresh();
        HelperFunction.showSuccessMessage(response.message!);
      },
      withLoading: false,
    );
  }

  @override
  void getMore({int? id}) {
    getPoints(sectionId.value, page: currentPage.value + 1);
  }
}
