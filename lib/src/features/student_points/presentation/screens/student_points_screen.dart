import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/point_criteria_controller.dart';
import '../../controller/student_point_controller.dart';
import '../widgets/bottomsheets/manage_points_bottomsheet.dart';
import '../widgets/list_titles/student_point_list_tile.dart';

class StudentPointsScreen extends StatelessWidget {
  const StudentPointsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(StudentPointController());
    Get.put(PointCriteriaController());
    return LayoutScreen(
      title: AuthService.instance.user.value?.role == Roles.Student
          ? "My Points"
          : "Motivational Points",
      controller: controller,
      bodyHeader: SectionWithSubjectsWidget(
        showSubjects: false,
        showSections: AuthService.instance.user.value?.role == Roles.Teacher,
        onSectionChanged: (sectionId) {
          controller.getPoints(sectionId, page: 1);
        },
      ),
      floatingActionButton:
          AuthService.instance.user.value?.role != Roles.Teacher
              ? null
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    //  points criterias
                    FloatingActionButton(
                      heroTag: "pointsCriterias",
                      backgroundColor: AppColor.redColor,
                      isExtended: true,
                      onPressed: () {
                        Get.toNamed(AppRouter.pointCriteriasScreen);
                      },
                      child: SvgPicture.asset(
                        AppAssets.criteria,
                        color: AppColor.whiteColor,
                        width: 25,
                      ),
                    ),
                    SizedBox(
                      width: 8,
                    ),
                    // add points
                    FloatingActionButton(
                      heroTag: "managePoints",
                      backgroundColor: AppColor.primaryColor,
                      onPressed: () {
                        Get.bottomSheet(
                          ManagePointsBottomSheet(),
                        );
                      },
                      child: Icon(
                        Icons.add,
                        color: AppColor.whiteColor,
                      ),
                    ),
                  ],
                ),
      body: Obx(() {
        var points = controller.points;
        return Column(
          children: [
            if (AuthService.instance.user.value?.role == Roles.Student)
              Container(
                padding: const EdgeInsets.all(16),
                color: AppColor.primaryColor,
                child: Row(
                  children: [
                    Text(
                      "My points sum: ".tr +
                          controller.pointSum.value.toString(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColor.whiteColor,
                      ),
                    ),
                  ],
                ),
              ),
            Expanded(
              child: ListView.separated(
                padding: const EdgeInsets.all(16),
                itemCount: points.length,
                itemBuilder: (context, index) {
                  if (index == controller.points.length) {
                    if (controller.canLoadMore()) {
                      controller.getMore(id: controller.sectionId.value);
                      return SizedBox(
                        height: 100,
                        width: double.maxFinite,
                        child: Center(
                          child: Text('loading more ...'.tr),
                        ),
                      );
                    } else {
                      return const SizedBox.shrink();
                    }
                  }
                  return StudentPointListTile(
                    studentPoint: points[index],
                  );
                },
                separatorBuilder: (context, index) => const SizedBox(
                  height: 16,
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
