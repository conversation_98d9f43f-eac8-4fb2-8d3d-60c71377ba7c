import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../controller/point_criteria_controller.dart';
import '../widgets/bottomsheets/manage_point_criteria_bottomsheet.dart';
import '../widgets/list_titles/point_criteria_list_tile.dart';

class PointCriteriasScreen extends StatelessWidget {
  const PointCriteriasScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(PointCriteriaController());
    return LayoutScreen(
      title: "Points Criterias",
      controller: controller,
      body: Obx(
        () {
          var criterias = controller.criterias.toList();
          return ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: criterias.length,
            itemBuilder: (context, index) {
              return PointCriteriaListTile(
                criteria: criterias[index],
              );
            },
            separatorBuilder: (context, index) => const SizedBox(
              height: 16,
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColor.primaryColor,
        child: Icon(
          Icons.add,
          color: AppColor.whiteColor,
        ),
        onPressed: () {
          Get.bottomSheet(
            ManagePointCriteriaBottomsheet(),
          );
        },
      ),
      withBackground: false,
    );
  }
}
