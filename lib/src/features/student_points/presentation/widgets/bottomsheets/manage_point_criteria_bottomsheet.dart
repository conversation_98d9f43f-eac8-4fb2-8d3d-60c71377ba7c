import 'package:flutter/material.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';
import '../../../../../core/data/models/point_criteria.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../../core/widgets/buttons/submit_button.dart';
import '../../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../../controller/point_criteria_controller.dart';

class ManagePointCriteriaBottomsheet extends StatelessWidget {
  const ManagePointCriteriaBottomsheet({super.key});

  @override
  Widget build(BuildContext context) {
    var formKey = GlobalKey<FormState>();
    PointCriteria pointCriteria = PointCriteria();
    return CustomBottomSheet(
      title: "Add Criteria".tr,
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: form<PERSON><PERSON>,
          child: Column(
            children: [
              CustomTextField(
                title: 'Criteria Name'.tr,
                validator: ValidationBuilder(
                  requiredMessage: "This field is required".tr,
                ).required().build(),
                onSaved: (value) {
                  pointCriteria.name = value;
                },
              ),
              CustomTextField(
                title: 'Points'.tr,
                keyboardType: TextInputType.number,
                validator: ValidationBuilder(
                  requiredMessage: "This field is required".tr,
                ).required("This field is required".tr).build(),
                onSaved: (value) {
                  pointCriteria.points = int.parse(value!);
                },
              ),
              SubmitButton(
                text: 'Add'.tr,
                color: AppColor.green2Color,
                onSubmit: () async {
                  await PointCriteriaController.instance.addPointCriteria(
                    pointCriteria.toJson(),
                  );
                },
                formKey: formKey,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
