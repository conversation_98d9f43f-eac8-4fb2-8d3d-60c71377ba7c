import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/data/models/student_point.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../../core/widgets/buttons/submit_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../../../core/widgets/form_fields/custom_dropdown_field.dart';
import '../../../controller/point_criteria_controller.dart';
import '../../../controller/student_point_controller.dart';

class ManagePointsBottomSheet extends StatelessWidget {
  const ManagePointsBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    var formKey = GlobalKey<FormState>();
    StudentPoint point = StudentPoint();
    return CustomBottomSheet(
      title: "Add Points".tr,
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: formKey,
          child: Column(
            children: [
              Obx(() {
                var students =
                    StudentPointController.instance.students.toList();
                return CustomDropdownField(
                  title: 'The student'.tr,
                  items: students,
                  listItemBuilder: (context, item, isSelected, onItemSelect) {
                    return CustomText(text: item.name ?? "");
                  },
                  headerBuilder: (context, item, isSelected) {
                    return CustomText(text: item.name ?? "");
                  },
                  onChanged: (value) {
                    point.studentId = value?.id ?? 0;
                  },
                  validator: (value) {
                    if (value == null) {
                      return "This field is required".tr;
                    }
                    return null;
                  },
                );
              }),
              Obx(() {
                var criterias =
                    PointCriteriaController.instance.criterias.toList();
                return CustomDropdownField(
                  title: 'The criteria'.tr,
                  items: criterias,
                  listItemBuilder: (context, item, isSelected, onItemSelect) {
                    return CustomText(text: item.name ?? "");
                  },
                  headerBuilder: (context, item, isSelected) {
                    return CustomText(text: item.name ?? "");
                  },
                  onChanged: (value) {
                    point.pointCriteriaId = value?.id ?? 0;
                  },
                  validator: (value) {
                    if (value == null) {
                      return "This field is required".tr;
                    }
                    return null;
                  },
                );
              }),
              SubmitButton(
                text: 'Add'.tr,
                color: AppColor.green2Color,
                onSubmit: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();
                    await StudentPointController.instance
                        .addPoint(point.toJson());
                  }
                },
                formKey: formKey,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
