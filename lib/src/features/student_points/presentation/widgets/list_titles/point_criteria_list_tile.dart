import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import '../../../../../core/data/models/point_criteria.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/widgets/custom_list_title.dart';
import '../../../controller/point_criteria_controller.dart';

class PointCriteriaListTile extends StatelessWidget {
  const PointCriteriaListTile({super.key, required this.criteria});
  final PointCriteria criteria;
  @override
  Widget build(BuildContext context) {
    return CustomListTile(
      leadingIcon: AppAssets.criteria,
      title: criteria.name,
      subtitle: "Points: ".tr + criteria.points.toString(),
      endAction: [
        SlidableAction(
          label: "Delete".tr,
          icon: Icons.delete,
          backgroundColor: AppColor.redColor,
          onPressed: (context) {
            PointCriteriaController.instance.delete(criteria.id!);
          },
        )
      ],
    );
  }
}
