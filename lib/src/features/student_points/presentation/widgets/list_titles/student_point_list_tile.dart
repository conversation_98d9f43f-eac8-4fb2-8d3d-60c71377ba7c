import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../../core/data/models/student_point.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/widgets/custom_list_title.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../controller/student_point_controller.dart';

class StudentPointListTile extends StatelessWidget {
  const StudentPointListTile({super.key, required this.studentPoint});
  final StudentPoint studentPoint;
  @override
  Widget build(BuildContext context) {
    return CustomListTile(
      title: studentPoint.name,
      subtitle: studentPoint.studentName,
      leading: Container(
        margin: EdgeInsetsDirectional.only(start: 8),
        width: 60,
        height: 60,
        alignment: Alignment.center,
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColor.primaryColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: CustomText(
          text: (studentPoint.points ?? 0).toString(),
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      detail: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        // mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            AppAssets.date,
          ),
          const SizedBox(width: 4),
          CustomText(
            text: studentPoint.formattedCreatedAt ?? "",
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ],
      ),
      endAction: [
        SlidableAction(
          label: "Delete".tr,
          icon: Icons.delete,
          backgroundColor: AppColor.redColor,
          onPressed: (context) {
            StudentPointController.instance.delete(studentPoint.id!);
          },
        )
      ],
    );
  }
}
