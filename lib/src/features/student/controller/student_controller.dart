import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/user.dart';

class StudentController extends BaseController {
  static StudentController get instance => Get.find();
  RxList<User> students = RxList<User>([]);

  void getStudents(int sectionId) {
    callApi(() async {
      students.value =
          await apiProvider.getSectionStudents(sectionId: sectionId);
      if (students.isEmpty) {
        pageStatus.value = PageStatus.empty;
      } else {
        pageStatus.value = PageStatus.loaded;
      }
    });
  }
}
