import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/models/parent.dart';
import '../../../core/data/models/student_statistics.dart';
import '../../../core/data/models/user.dart';
import '../../../core/utils/app_color.dart';
import '../../../core/utils/helper_function.dart';
import '../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../core/widgets/custom_list_title.dart';

class StudentStatisticsController extends BaseController {
  static StudentStatisticsController get instance => Get.find();
  Rx<StudentStatistics?> statistics = Rx<StudentStatistics?>(null);

  void getStudentStatistics({int? studentId}) {
    callApi(() async {
      statistics.value =
          await apiProvider.getStudentStatistics(studentId: studentId);
    });
  }

  static void chatWithParent(User student) async {
    if (student.parents.isEmpty) {
      HelperFunction.showErrorMessage(
        "لا يوجد ولي الامر لهذا الطالب",
      );
      return;
    }
    Parent? parent;
    if (student.parents.length == 1) {
      parent = student.parents[0];
    } else {
      parent = await Get.bottomSheet<Parent>(
        CustomBottomSheet(
          title: "Choose Parent".tr,
          body: Container(
            color: AppColor.scaffoldColor,
            padding: const EdgeInsets.all(16.0),
            constraints: BoxConstraints(
              maxHeight: Get.height * 0.8,
              minHeight: Get.height * 0.5,
            ),
            child: ListView.separated(
              itemCount: student.parents.length,
              itemBuilder: (context, index) {
                return CustomListTile(
                  title: student.parents[index].name ?? "",
                  onTap: () {
                    Get.back(
                      result: student.parents[index],
                    );
                  },
                  trailing: Icon(
                    Icons.chevron_right_rounded,
                    color: AppColor.primaryColor,
                  ),
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(
                  height: 16,
                );
              },
            ),
          ),
        ),
      );
      // parent = student.parents[0];
    }
    if (parent != null) {
      HelperFunction.navigateToChatDetails(
        receiverId: parent.id ?? 0,
      );
    }
  }
}
