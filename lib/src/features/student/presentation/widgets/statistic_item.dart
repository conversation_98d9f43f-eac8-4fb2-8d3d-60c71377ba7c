import 'package:flutter/widgets.dart';
import '../../../../core/widgets/buttons/custom_button.dart';
import '../../../../core/widgets/custom_text.dart';

class StatisticItem extends StatelessWidget {
  const StatisticItem({
    super.key,
    required this.text,
    required this.count,
    required this.color,
    this.onTap,
  });
  final String text;
  final int count;
  final Color color;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          CustomText(
            text: text,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          const Spacer(),
          CustomButton(
            height: 40,
            text: count.toString(),
            onTap: onTap,
            textColor: color,
            fontSize: 15,
            padding: EdgeInsets.symmetric(horizontal: 24),
            borderRadius: 20,
            color: color.withOpacity(0.1),
          ),
        ],
      ),
    );
  }
}
