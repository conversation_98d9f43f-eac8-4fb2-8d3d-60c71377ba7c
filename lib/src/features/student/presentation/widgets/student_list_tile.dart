import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:rawd_aljenan/src/core/utils/helper_function.dart';
import '../../../../core/data/models/user.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../controller/student_statistics_controller.dart';
import 'achievement_button.dart';
import 'chat_button.dart';

class StudentListTile extends StatelessWidget {
  const StudentListTile({
    super.key,
    required this.student,
    this.forOpenChat = false,
    this.isStudent = false,
  });
  final User student;
  final bool forOpenChat;
  final bool isStudent;
  @override
  Widget build(BuildContext context) {
    return CustomListTile(
      leading: AspectRatio(
        aspectRatio: 1,
        child: CustomCachedNetworkImage(
          imageUrl: student.image ?? "",
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            width: 0.5,
            color: AppColor.primaryColor,
          ),
        ),
      ),
      title: student.name,
      subtitle: student.sectionName,
      trailing: !forOpenChat
          ? AchievementButtom(
              onTap: () {
                Get.toNamed(
                  AppRouter.studentAchievementScreen,
                  arguments: student,
                );
              },
            )
          : ChatBotton(onTap: () {
              Get.back();
              if (isStudent) {
                int receiverId = student.id ?? 0;
                HelperFunction.navigateToChatDetails(receiverId: receiverId);
              } else {
                StudentStatisticsController.chatWithParent(student);
              }
            }),
    );
  }
}
