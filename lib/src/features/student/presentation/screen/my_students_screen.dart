import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../controller/student_controller.dart';
import '../widgets/student_list_tile.dart';

class MyStudentsScreen extends StatelessWidget {
  const MyStudentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(StudentController());
    return LayoutScreen(
      bodyHeader: SectionWithSubjectsWidget(
        showSections: true,
        showSubjects: false,
        onSubjectChanged: (sectionId, subjectId) {
          controller.getStudents(sectionId);
        },
      ),
      title: 'My Students'.tr,
      controller: controller,
      body: Obx(() {
        return ListView.separated(
          padding: EdgeInsets.all(16),
          itemCount: controller.students.length,
          itemBuilder: (BuildContext context, int index) {
            var student = controller.students[index];
            return StudentListTile(
              student: student,
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return const SizedBox(
              height: 16,
            );
          },
        );
      }),
    );
  }
}
