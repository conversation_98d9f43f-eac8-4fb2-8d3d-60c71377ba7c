import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/user.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/utils/extensions.dart';
import '../../../../core/widgets/buttons/custom_button.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/media/custom_boxes.dart';
import '../../../auth/services/auth_service.dart';
import '../../../home/<USER>/son_controller.dart';
import '../../controller/student_statistics_controller.dart';
import '../widgets/statistic_item.dart';

class StudentStatisticsScreen extends StatelessWidget {
  final User student;
  const StudentStatisticsScreen({super.key, required this.student});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(StudentStatisticsController());
    controller.getStudentStatistics(
      studentId: student.id,
    );
    return LayoutScreen(
      title: 'Student Achievements'.tr,
      controller: controller,
      body: Obx(
        () => Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              CustomCard(
                child: Row(
                  children: [
                    CustomCachedNetworkImage(
                      imageUrl: student.image ?? "",
                      fit: BoxFit.cover,
                      height: 100,
                      width: 90,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        width: 0.5,
                        color: AppColor.primaryColor,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      flex: 8,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          FittedBox(
                            fit: BoxFit.scaleDown,
                            alignment: AlignmentDirectional.centerStart,
                            child: CustomText(
                              text: student.name ?? "",
                              fontSize: 16,
                              fontWeight: FontWeight.w900,
                              color: AppColor.primaryColor,
                              maxLine: 2,
                            ),
                          ),
                          SizedBox(height: 16),
                          if (AuthService.instance.user.value?.role ==
                              Roles.Teacher)
                            CustomButton(
                              text: 'مراسلة ولي الامر',
                              onTap: () {
                                StudentStatisticsController.chatWithParent(
                                  student,
                                );
                              },
                              color: AppColor.primaryColor,
                              borderRadius: 20,
                              textColor: AppColor.whiteColor,
                              fontSize: 12,
                              height: 35,
                            ),
                          if (AuthService.instance.user.value?.role ==
                              Roles.Guardian)
                            CustomButton(
                              text: 'اختيار الحساب',
                              onTap: () async {
                                if (AuthService.instance.user.value?.role ==
                                    Roles.Guardian) {
                                  AuthService.instance.parentUser.value =
                                      AuthService.instance.user.value;
                                }
                                var son = SonController.instance.sons
                                    .where((son) => son.id == student.id)
                                    .first;
                                AuthService.instance.user.value = son;
                                AuthService.instance.token.value = son.token;
                                AuthService.instance.selectedSon.value = son;
                                Get.back();
                                Get.back();
                              },
                              color: AppColor.primaryColor,
                              borderRadius: 20,
                              textColor: AppColor.whiteColor,
                              fontSize: 12,
                              height: 35,
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      flex: 4,
                      child: Column(
                        children: [
                          CustomGreyBox(
                            color: AppColor.greyColor.withOpacity(0.2),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                CustomText(
                                  text: controller.statistics.value?.pointsCount
                                          .toString() ??
                                      "",
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppColor.primaryColor,
                                  height: 1.5,
                                ),
                                SvgPicture.asset(
                                  AppAssets.star,
                                  width: 30,
                                )
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          SmartFingersImage(
                            imageUrl: AppAssets.cup2,
                            height: 40,
                            width: 40,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              CustomCard(
                margin: EdgeInsets.only(top: 16),
                child: Column(
                  children: [
                    StatisticItem(
                      text: "عدد لوحات الشرف",
                      count: controller.statistics.value?.honorBoardsCount ?? 0,
                      color: AppColor.primaryColor,
                    ),
                    const Divider(),
                    StatisticItem(
                      text: 'عدد ايام الحضور',
                      count: controller.statistics.value?.presentDaysCount ?? 0,
                      color: AppColor.greenColor,
                    ),
                    const Divider(),
                    StatisticItem(
                      text: 'عدد ايام الغياب',
                      count: controller.statistics.value?.upsentDaysCount ?? 0,
                      color: AppColor.redColor,
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
