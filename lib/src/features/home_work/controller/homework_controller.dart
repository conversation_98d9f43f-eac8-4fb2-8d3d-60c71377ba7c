import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/homework.dart';
import '../../../core/data/models/responses/grouped_homework_plan.dart';
import '../../../core/utils/helper_function.dart';
import '../../../core/data/models/section_with_subjects.dart';
import '../../auth/services/auth_service.dart';

import '../../../core/utils/app_consts.dart';

class HomeworkController extends BaseController {
  static HomeworkController get instance => Get.find();

  RxList<Homework> homeworks = RxList<Homework>([]);
  RxList<GroupedHomeWork> studentHomeworks = RxList<GroupedHomeWork>([]);
  Rx<SectionWithSubjects?> selectedSection = Rx<SectionWithSubjects?>(null);

  @override
  void onInit() {
    homeworks.listen(
      (value) {
        if (value.isEmpty) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      },
    );
    super.onInit();
  }

  void getHomeworks({
    int? sectionId,
    int? subjectId,
  }) {
    callApi(() async {
      var role = AuthService.instance.user.value?.role;
      if (role == Roles.Student || role == Roles.Guardian) {
        var response = await apiProvider.getHomeworksForStudent(
          sectionId: sectionId,
          subjectId: subjectId,
        );
        studentHomeworks.value = response;
        studentHomeworks.refresh();
        if (studentHomeworks.isEmpty) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      } else {
        var response = await apiProvider.getHomeworks(
          sectionId: sectionId,
          subjectId: subjectId,
        );
        homeworks.value = response;
        homeworks.refresh();
      }
    });
  }

  Future<void> addHomework(Homework homework) async {
    await callApi(() async {
      var response = await apiProvider.createHomework(homework);
      Get.back();
      HelperFunction.showSuccessMessage(response.message ?? "");
      homeworks.add(response.data!);
    }, withLoading: false);
  }

  Future<void> updateHomework(Homework homework) async {
    await callApi(() async {
      var response = await apiProvider.updateHomework(homework.id!, homework);
      Get.back();
      homeworks[homeworks.indexWhere((element) => element.id == homework.id)] =
          response.data!;
      HelperFunction.showSuccessMessage(response.message ?? "");
    }, withLoading: false);
  }

  Future<void> deleteHomework(int id) async {
    await callApi(() async {
      var response = await apiProvider.deleteHomework(id);
      HelperFunction.showSuccessMessage(response.message ?? "");
      homeworks.removeWhere((element) => element.id == id);
    }, withLoading: false);
  }
}
