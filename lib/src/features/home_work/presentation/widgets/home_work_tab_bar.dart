import 'package:flutter/material.dart';
import '../../../../core/data/models/responses/grouped_homework_plan.dart';
import '../../../class_schedule/presentation/widgets/day_tab.dart';

class HomeworkTabBar extends StatefulWidget {
  const HomeworkTabBar({
    super.key,
    required this.studentHomeworks,
  });

  final List<GroupedHomeWork> studentHomeworks;

  @override
  State<HomeworkTabBar> createState() => _HomeworkTabBarState();
}

class _HomeworkTabBarState extends State<HomeworkTabBar> {
  int selectedIndex = 0;
  @override
  Widget build(BuildContext context) {
    return TabBar(
      onTap: (value) {
        setState(() {
          selectedIndex = value;
        });
      },
      tabs: List.generate(
        widget.studentHomeworks.length,
        (index) {
          return DayTab(
            selected: index == selectedIndex,
            text: widget.studentHomeworks[index].day,
          );
        },
      ),
      labelPadding: const EdgeInsets.symmetric(
        horizontal: 4,
      ),
      padding: const EdgeInsets.only(
        top: 16,
        right: 8,
        left: 8,
        bottom: 8,
      ),
      dividerHeight: 0,
      indicatorWeight: 0,
      indicator: const BoxDecoration(),
      isScrollable: true,
      tabAlignment: TabAlignment.center,
    );
  }
}
