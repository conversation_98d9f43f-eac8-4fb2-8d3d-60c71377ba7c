import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/homework.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../controller/homework_controller.dart';

import '../../../../core/utils/app_consts.dart';
import '../../../auth/services/auth_service.dart';

class HomeworkListTile extends StatelessWidget {
  const HomeworkListTile({super.key, required this.homework});
  final Homework homework;
  @override
  Widget build(BuildContext context) {
    var userRole = AuthService.instance.user.value?.role;

    return CustomListTile(
      title: homework.title,
      subtitle: userRole == Roles.Teacher
          ? (homework.formattedDueDate ?? "")
          : (homework.subjectName ?? ""),
      subtitleIcon: AuthService.instance.user.value?.role == Roles.Teacher
          ? AppAssets.calender
          : null,
      leadingIcon: AppAssets.homework,
      endAction: userRole != Roles.Teacher
          ? null
          : [
              SlidableAction(
                onPressed: (context) {
                  Get.toNamed(AppRouter.manageHomeWork, arguments: {
                    'isUpdate': true,
                    'data': homework.clone(),
                  });
                },
                icon: Icons.edit,
                backgroundColor: Get.theme.primaryColor,
                label: 'Edit'.tr,
              ),
              SlidableAction(
                onPressed: (context) {
                  HomeworkController.instance.deleteHomework(homework.id!);
                },
                icon: Icons.delete,
                backgroundColor: Colors.red,
                label: 'Delete'.tr,
              )
            ],
      onTap: () {
        showDetailBottomSheet();
      },
    );
  }

  void showDetailBottomSheet() {
    Get.bottomSheet(
      CustomBottomSheet(
        title: homework.title,
        body: Container(
          constraints: BoxConstraints(
            maxHeight: Get.height * 0.8,
            minHeight: Get.height * 0.5,
          ),
          padding: const EdgeInsets.all(16.0),
          child: Text(
            homework.description!,
            textAlign: TextAlign.start,
          ),
        ),
      ),
      isScrollControlled: true,
    );
  }
}
