import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/homework_controller.dart';
import '../widgets/home_work_tab_bar.dart';
import '../widgets/homework_list_tile.dart';

class HomeworksScreen extends StatelessWidget {
  const HomeworksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(HomeworkController());

    return LayoutScreen(
      floatingActionButton:
          AuthService.instance.user.value?.role == Roles.Teacher
              ? FloatingActionButton(
                  backgroundColor: AppColor.primaryColor,
                  onPressed: () =>
                      Get.toNamed(AppRouter.manageHomeWork, arguments: {
                    'isUpdate': false,
                  }),
                  child: Icon(
                    Icons.add,
                    color: AppColor.whiteColor,
                  ),
                )
              : null,
      title: 'Home Work'.tr,
      controller: controller,
      bodyHeader: SectionWithSubjectsWidget(
        showSections: AuthService.instance.user.value?.role == Roles.Teacher,
        showSubjects: AuthService.instance.user.value?.role == Roles.Teacher,
        onSubjectChanged: (sectionId, subjectId) {
          controller.getHomeworks(
            sectionId: sectionId,
            subjectId: subjectId,
          );
        },
      ),
      body: Obx(() {
        var role = AuthService.instance.user.value?.role;
        if (role == Roles.Student || role == Roles.Guardian) {
          var studentHomeworks = controller.studentHomeworks;

          return DefaultTabController(
            length: studentHomeworks.length,
            child: Column(
              children: [
                HomeworkTabBar(studentHomeworks: studentHomeworks),
                Expanded(
                  child: TabBarView(
                    children: List.generate(studentHomeworks.length, (index) {
                      return ListView.separated(
                        padding: const EdgeInsets.all(16),
                        itemCount: studentHomeworks[index].homeworks.length,
                        itemBuilder: (context, homeworkIndex) {
                          return HomeworkListTile(
                            homework: studentHomeworks[index]
                                .homeworks[homeworkIndex],
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) {
                          return 16.verticalSpace;
                        },
                      );
                    }),
                  ),
                ),
              ],
            ),
          );
        }

        final homeworks = controller.homeworks;
        return ListView.separated(
          separatorBuilder: (context, index) => 16.verticalSpace,
          itemCount: homeworks.length,
          padding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 16,
          ),
          itemBuilder: (context, index) {
            return HomeworkListTile(
              homework: homeworks[index],
            );
          },
        );
      }),
    );
  }
}
