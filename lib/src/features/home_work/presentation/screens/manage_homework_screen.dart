import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/homework.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/extensions.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../../../core/widgets/form_fields/custom_dropdown_field.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../../../core/widgets/form_fields/date_time_form_field.dart';
import '../../../auth/services/auth_service.dart';
import '../../../../core/data/models/section_with_subjects.dart';
import '../../controller/homework_controller.dart';

class ManageHomeWorkScreen extends StatelessWidget {
  const ManageHomeWorkScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final arguments = Get.arguments;
    final Homework homework = arguments['data'] ?? Homework();
    final isUpdated = arguments['isUpdate'];
    var formKey = GlobalKey<FormState>();
    if (homework.sectionId != null) {
      HomeworkController.instance.selectedSection.value = AuthService
          .instance.sectionWithSubjects
          .firstWhere((element) => element.id == homework.sectionId);
    }
    return LayoutScreen(
      title: isUpdated == false ? 'Add HomeWork'.tr : 'Update HomeWork'.tr,
      withBackground: false,
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(right: 16, left: 16, top: 16),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomDropdownField(
                title: 'Class'.tr,
                initialItem: homework.sectionId == null
                    ? null
                    : AuthService.instance.sectionWithSubjects.firstWhere(
                        (element) => element.id == homework.sectionId),
                items: AuthService.instance.sectionWithSubjects,
                headerBuilder: (context, item, isSelected) {
                  return Text(item.name);
                },
                listItemBuilder: (context, item, isSelected, onItemSelect) {
                  return Text(item.name);
                },
                validator: (value) {
                  if (value == null) {
                    return "Please select a class".tr;
                  }
                  return null;
                },
                onChanged: (value) {
                  homework.sectionId = value?.id;
                  HomeworkController.instance.selectedSection.value = value;
                },
              ),
              Obx(
                () => CustomDropdownField<Subject>(
                  key: Key(Random.secure().toString()),
                  title: 'Subject'.tr,
                  initialItem: homework.subjectId == null
                      ? null
                      : HomeworkController
                          .instance.selectedSection.value?.subjects
                          .firstWhere(
                              (element) => element.id == homework.subjectId),
                  items: HomeworkController
                          .instance.selectedSection.value?.subjects ??
                      [],
                  headerBuilder: (context, item, isSelected) {
                    return Text(item.name);
                  },
                  listItemBuilder: (context, item, isSelected, onItemSelect) {
                    return Text(item.name);
                  },
                  validator: (value) {
                    if (value == null) {
                      return "Please select a subject".tr;
                    }
                    return null;
                  },
                  onChanged: (value) {
                    homework.subjectId = value?.id;
                  },
                ),
              ),
              CustomTextField(
                margin: const EdgeInsets.only(
                  top: 8,
                ),
                title: 'HomeWork title'.tr,
                filled: true,
                filledColor: AppColor.whiteColor,
                hint: 'Enter homeWork title'.tr,
                initialValue: homework.title,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "The field is required".tr;
                  }
                  return null;
                },
                onSaved: (value) {
                  homework.title = value;
                },
              ),
              CustomTextField(
                margin: const EdgeInsets.only(
                  top: 8,
                ),
                title: 'HomeWork details'.tr,
                hint: 'Enter homeWork details'.tr,
                maxLine: 6,
                initialValue: homework.description,
                onSaved: (value) {
                  homework.description = value;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "The field is required".tr;
                  }
                  return null;
                },
              ),
              CustomDateTimeFormField(
                margin: const EdgeInsets.only(
                  top: 8,
                ),
                title: 'Submission date'.tr,
                hint: 'Enter submission date'.tr,
                suffixIcon: SvgPicture.asset(
                  AppAssets.calender,
                ),
                initialValue: homework.dueDate,
                onSaved: (value) {
                  homework.dueDate = value;
                },
                validator: (value) {
                  if (value == null) {
                    return "The field is required".tr;
                  }
                  return null;
                },
                onChanged: (DateTime? value) {},
              ),
              SubmitButton(
                margin: const EdgeInsets.only(
                  top: 24,
                  bottom: 16,
                ),
                text: isUpdated == false ? 'Add'.tr : 'Update'.tr,
                onSubmit: () async {
                  if (isUpdated == false) {
                    await HomeworkController.instance.addHomework(homework);
                  } else {
                    await HomeworkController.instance.updateHomework(homework);
                  }
                },
                color: AppColor.primaryColor,
                formKey: formKey,
              ),
            ],
          ).verticalSpace(10.h),
        ),
      ),
    );
  }
}
