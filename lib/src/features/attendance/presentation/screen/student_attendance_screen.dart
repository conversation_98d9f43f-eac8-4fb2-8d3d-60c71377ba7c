import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/utils/extensions.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../controller/attendance_controller.dart';
import '../widgets/student_attendance_list_tile.dart';
import '../../../auth/services/auth_service.dart';

class StudentAttendanceScreen extends StatelessWidget {
  const StudentAttendanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(AttendanceController());
    return LayoutScreen(
      title: 'Student Attendance'.tr,
      controller: controller,
      bodyHeader: Column(
        children: [
          SectionWithSubjectsWidget(
            onSectionChanged: (sectionId) {
              if (AuthService.instance.user.value?.role ==
                  Roles.AdminAssistant) {
                controller.getAttendance(sectionId: sectionId);
              }
            },
            showSubjects:
                AuthService.instance.user.value?.role != Roles.AdminAssistant,
            onSubjectChanged: (sectionId, subjectId) {
              if (AuthService.instance.user.value?.role !=
                  Roles.AdminAssistant) {
                controller.getAttendance(
                    sectionId: sectionId, subjectId: subjectId);
              }
            },
          ),
          const SizedBox(height: 8),
          Text(DateTime.now().format('yyyy-MM-dd')),
          const SizedBox(height: 8),
        ],
      ),
      body: Obx(() {
        final attendances = controller.attendances;
        return ListView.separated(
          key: Key(
              "${AttendanceController.instance.selectedSectionId}_${AttendanceController.instance.selectedSubjectId}"),
          padding: const EdgeInsets.only(right: 16, left: 16, bottom: 16),
          itemCount: attendances.length,
          itemBuilder: (context, index) {
            return StudentAttendanceListTile(
              studentAttendance: attendances[index],
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return 16.verticalSpace;
          },
        );
      }),
    );
  }
}
