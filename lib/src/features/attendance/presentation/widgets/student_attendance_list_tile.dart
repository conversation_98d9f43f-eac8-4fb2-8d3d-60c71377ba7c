import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/student_attendance.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../controller/attendance_controller.dart';

class StudentAttendanceListTile extends StatelessWidget {
  const StudentAttendanceListTile({super.key, required this.studentAttendance});
  final StudentAttendance studentAttendance;
  @override
  Widget build(BuildContext context) {
    return CustomListTile(
      title: studentAttendance.name ?? "",
      leading: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Get.theme.primaryColor,
          ),
        ),
        child: CustomCachedNetworkImage(
          imageUrl: studentAttendance.image ?? "",
          fit: BoxFit.cover,
          width: 40,
          height: 40,
        ),
      ),
      endAction: [
        SlidableAction(
          backgroundColor: AppColor.fromHex("#FF484C"),
          foregroundColor: Get.theme.scaffoldBackgroundColor,
          label: 'Absent'.tr,
          onPressed: (BuildContext context) {
            var attendance = studentAttendance.clone();
            attendance.attendanceStatus = "absent";
            AttendanceController.instance.attend(attendance);
          },
        ),
        SlidableAction(
          backgroundColor: AppColor.fromHex("#D4D06D"),
          foregroundColor: Get.theme.scaffoldBackgroundColor,
          label: 'Excused'.tr,
          onPressed: (BuildContext context) {
            var attendance = studentAttendance.clone();
            attendance.attendanceStatus = "excused";
            AttendanceController.instance.attend(attendance);
          },
        ),
        SlidableAction(
          backgroundColor: AppColor.fromHex("#7B7B7B"),
          foregroundColor: Get.theme.scaffoldBackgroundColor,
          label: 'Late'.tr,
          onPressed: (BuildContext context) {
            var attendance = studentAttendance.clone();
            attendance.attendanceStatus = "late";
            AttendanceController.instance.attend(attendance);
          },
        ),
      ],
      startAction: [
        SlidableAction(
          backgroundColor: Get.theme.primaryColor,
          foregroundColor: Get.theme.scaffoldBackgroundColor,
          label: 'Present'.tr,
          onPressed: (BuildContext context) {
            var attendance = studentAttendance.clone();
            attendance.attendanceStatus = "present";
            AttendanceController.instance.attend(attendance);
          },
        ),
      ],
      border: studentAttendance.attendanceStatus == null
          ? null
          : Border.all(
              color: AppColor.fromHex(
                studentAttendance.attendanceStatusColor ?? "",
              ),
            ),
    );
  }
}
