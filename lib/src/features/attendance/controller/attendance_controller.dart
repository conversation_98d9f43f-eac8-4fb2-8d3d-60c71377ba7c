import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/student_attendance.dart';

class AttendanceController extends BaseController {
  static AttendanceController get instance => Get.find();

  var selectedSectionId = 0.obs;
  var selectedSubjectId = 0.obs;

  RxList<StudentAttendance> attendances = RxList<StudentAttendance>([]);

  void getAttendance({
    required int sectionId,
    int? subjectId,
  }) {
    selectedSectionId.value = sectionId;
    selectedSubjectId.value = subjectId ?? 0;
    callApi(() async {
      var response = await apiProvider.getStudentAttendance(
        sectionId: sectionId,
        subjectId: subjectId,
      );

      // print(response);
      attendances.clear();
      attendances.value = response;

      attendances.refresh();
      if (attendances.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else {
        setPageStatus(PageStatus.loaded);
      }
    });
  }

  Future<void> attend(StudentAttendance attendance) async {
    await callApi(() async {
      attendance.subjectId =
          selectedSubjectId.value == 0 ? null : selectedSubjectId.value;
      var response = await apiProvider.studentAttend(attendance);
      // HelperFunction.showSuccessMessage(response.message ?? "");

      var elIndex = attendances
          .toList()
          .indexWhere((element) => element.id == response.data!.id);
      print(elIndex);
      if (elIndex == -1) return;
      attendances[elIndex] = response.data!;
      attendances.refresh();
    }, withLoading: false);
  }
}
