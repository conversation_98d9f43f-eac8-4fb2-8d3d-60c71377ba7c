import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/honor_board.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/honor_board_controller.dart';

class HonorBoardListTile extends StatelessWidget {
  const HonorBoardListTile({super.key, required this.honorBoard});
  final HonorBoard honorBoard;
  @override
  Widget build(BuildContext context) {
    var userRole = AuthService.instance.user.value?.role;
    return CustomListTile(
      title: honorBoard.title,
      leadingIcon: AppAssets.medal,
      leadingIconColor: AppColor.primaryColor,
      onTap: () => Get.toNamed(
        AppRouter.honorBoardStudentsScreen,
        arguments: honorBoard,
      ),
      endAction: userRole == Roles.Student
          ? null
          : [
              SlidableAction(
                onPressed: (_) {
                  Get.toNamed(
                    AppRouter.manageHonorBoardScreen,
                    arguments: {
                      'isUpdate': true,
                      'data': honorBoard.clone(),
                    },
                  );
                },
                icon: Icons.edit,
                backgroundColor: AppColor.primaryColor,
                foregroundColor: AppColor.whiteColor,
                label: 'Edit'.tr,
              ),
              SlidableAction(
                onPressed: (_) {
                  HonorBoardController.instance
                      .deleteHonorBoard(honorBoard.id ?? 0);
                },
                icon: Icons.delete,
                backgroundColor: AppColor.redColor,
                foregroundColor: AppColor.whiteColor,
                label: 'Delete'.tr,
              ),
            ],
    );
  }
}
