import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/data/models/honor_board.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/extensions.dart';

class HonorBoardStudentCard extends StatelessWidget {
  final int order;
  final HonorBoardStudent student;

  const HonorBoardStudentCard({
    super.key,
    required this.order,
    required this.student,
  });

  @override
  Widget build(BuildContext context) {
    var name = student.name ?? "";
    name = name.length > 18 ? "${name.substring(0, 18)}..." : name;
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            color: AppColor.whiteColor,
          ),
          child: Padding(
            padding: EdgeInsets.all(8.h),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: AppColor.primaryColor,
                      width: 0.5,
                    ),
                  ),
                  width: double.infinity,
                  child: AspectRatio(
                      aspectRatio: 1,
                      child: SmartFingersImage(imageUrl: student.image ?? "")),
                ),
                8.verticalSpace,
                Text(name,
                    style: TextStyle(
                      color: AppColor.primaryColor,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    )),
              ],
            ),
          ),
        ),
        PositionedDirectional(
          top: 0,
          end: 25,
          child: Container(
            width: 26,
            height: 35,
            decoration: BoxDecoration(
                color: AppColor.primaryColor,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(7.r),
                    bottomRight: Radius.circular(7.r))),
            child: Center(
              child: Text(
                order.toString().padLeft(2, '0'),
                style: TextStyle(
                  color: AppColor.whiteColor,
                  fontSize: 13.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
