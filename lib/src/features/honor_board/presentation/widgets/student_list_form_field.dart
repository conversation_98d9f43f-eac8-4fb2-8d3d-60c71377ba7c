import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/user.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/form_fields/custom_dropdown_field.dart';

class StudentListFormField extends StatelessWidget {
  const StudentListFormField({
    super.key,
    required this.students,
    this.margin,
    this.title,
    this.initialValue,
    this.validator,
    this.onSaved,
  });
  final List<User> students;
  final EdgeInsetsGeometry? margin;
  final String? title;
  final List<int>? initialValue;
  final String? Function(List<int>? value)? validator;
  final void Function(List<int>? value)? onSaved;

  @override
  Widget build(BuildContext context) {
    Map<int, User> studentsList = {};
    for (var student in students) {
      studentsList[student.id!] = student;
    }
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Padding(
              padding: const EdgeInsetsDirectional.only(start: 8, bottom: 8.0),
              child: Text(
                title!,
                style: TextStyle(
                  color: AppColor.greyColor5,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          FormField<List<int>>(
              initialValue: initialValue,
              validator: validator,
              onSaved: onSaved,
              builder: (field) {
                return Column(
                  children: [
                    ReorderableListView.builder(
                      itemBuilder: (_, index) {
                        return Container(
                          key: ValueKey(index),
                          margin: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(
                                  Icons.menu,
                                  color: AppColor.primaryColor,
                                ),
                              ),
                              Expanded(
                                child: CustomDropdownField<int>(
                                  key: ValueKey(field.value?[index]),
                                  margin: const EdgeInsets.all(0),
                                  items: students.map((e) => e.id!).toList(),
                                  initialItem: field.value?[index] == 0
                                      ? null
                                      : field.value?[index],
                                  headerBuilder: (context, item, isSelected) {
                                    return Text(studentsList[item]!.name!);
                                  },
                                  listItemBuilder: (context, item, isSelected,
                                      onItemSelect) {
                                    return Text(studentsList[item]!.name!);
                                  },
                                  onChanged: (value) {
                                    var ids = field.value ?? [];
                                    ids[index] = value!;
                                    field.didChange(ids);
                                  },
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  var ids = field.value ?? [];
                                  ids.removeAt(index);
                                  field.didChange(ids);
                                },
                                icon: SvgPicture.asset(
                                  AppAssets.delete,
                                  color: AppColor.redColor,
                                ),
                              )
                            ],
                          ),
                        );
                      },
                      itemCount: field.value?.length ?? 0,
                      onReorder: (oldIndex, newIndex) {
                        var ids = field.value ?? [];
                        print("$ids old index $oldIndex new index $newIndex");
                        if (newIndex > oldIndex) {
                          newIndex -= 1;
                        }

                        final value = ids.removeAt(oldIndex);
                        print(ids);
                        ids.insert(newIndex, value);
                        print("$ids old index $oldIndex new index $newIndex");

                        field.didChange(ids);
                      },
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                    ),
                    if (field.hasError)
                      SizedBox(
                        width: double.maxFinite,
                        child: Text(
                          field.errorText!,
                          style: TextStyle(color: AppColor.redColor),
                        ),
                      ),
                    const SizedBox(height: 16),
                    OutlinedButton.icon(
                      onPressed: () {
                        field.didChange([...(field.value ?? []), 0]);
                      },
                      label: Text(
                        "Add Student".tr,
                        style: TextStyle(
                          color: AppColor.primaryColor,
                        ),
                      ),
                      icon: Icon(
                        Icons.add,
                        color: AppColor.primaryColor,
                      ),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: AppColor.primaryColor,
                        ),
                      ),
                    )
                  ],
                );
              }),
        ],
      ),
    );
  }
}
