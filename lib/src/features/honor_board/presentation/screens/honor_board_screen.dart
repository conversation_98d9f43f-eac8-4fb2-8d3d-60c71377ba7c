import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/honor_board_controller.dart';
import '../widgets/honor_board_list_tile.dart';

class HonorBoardScreen extends StatelessWidget {
  const HonorBoardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(HonorBoardController());
    var userRole = AuthService.instance.user.value?.role;

    return LayoutScreen(
      floatingActionButton: userRole == Roles.Teacher
          ? FloatingActionButton(
              backgroundColor: AppColor.primaryColor,
              child: Icon(
                Icons.add,
                color: AppColor.whiteColor,
              ),
              onPressed: () {
                Get.toNamed(AppRouter.manageHonorBoardScreen, arguments: {
                  'isUpdate': false,
                });
              },
            )
          : null,
      title: 'Honor board'.tr,
      controller: controller,
      bodyHeader: SectionWithSubjectsWidget(
        showSubjects: false,
        showSections: userRole == Roles.Teacher || userRole == Roles.Guardian,
        onSectionChanged: (sectionId) {
          controller.getHonorBoards(sectionId);
        },
      ),
      body: Obx(() {
        var honorBoards = controller.honorBoards;
        return ListView.separated(
          padding: const EdgeInsets.all(16),
          itemCount: honorBoards.length,
          itemBuilder: (context, index) {
            return HonorBoardListTile(
              honorBoard: controller.honorBoards[index],
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return SizedBox(
              height: 16,
            );
          },
        );
      }),
    );
  }
}
