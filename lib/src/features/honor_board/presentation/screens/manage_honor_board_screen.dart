import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/honor_board.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../../../core/widgets/form_fields/custom_dropdown_field.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../../auth/services/auth_service.dart';
import '../../../../core/data/models/section_with_subjects.dart';
import '../../controller/honor_board_controller.dart';
import '../widgets/student_list_form_field.dart';

class ManageHonorBoardScreen extends StatelessWidget {
  const ManageHonorBoardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final arguments = Get.arguments;
    final bool isUpdate = arguments['isUpdate'];
    HonorBoard honorBoard = arguments['data'] ?? HonorBoard();
    var formKey = GlobalKey<FormState>();
    if (honorBoard.sectionId != null) {
      HonorBoardController.instance.selectedSection.value =
          AuthService.instance.sectionWithSubjects.firstWhere(
        (element) => element.id.toString() == honorBoard.sectionId,
      );
      HonorBoardController.instance
          .getSectionStudents(int.parse(honorBoard.sectionId!));
    }
    return LayoutScreen(
      title: isUpdate ? 'Update honor board'.tr : 'Add honor board'.tr,
      withBackground: false,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: formKey,
          child: Column(
            children: [
              CustomDropdownField(
                title: 'Class'.tr,
                initialItem: honorBoard.sectionId == null
                    ? null
                    : AuthService.instance.sectionWithSubjects.firstWhere(
                        (element) =>
                            element.id.toString() == honorBoard.sectionId),
                items: AuthService.instance.sectionWithSubjects,
                headerBuilder: (context, item, isSelected) {
                  return Text(item.name);
                },
                listItemBuilder: (context, item, isSelected, onItemSelect) {
                  return Text(item.name);
                },
                validator: (value) {
                  if (value == null) {
                    return "Please select a class".tr;
                  }
                  return null;
                },
                onChanged: (value) {
                  honorBoard.sectionId = value?.id.toString();
                  HonorBoardController.instance.selectedSection.value = value;
                  honorBoard.subjectId = null;
                  HonorBoardController.instance
                      .getSectionStudents(value?.id ?? 0);
                },
              ),
              Obx(
                () {
                  return CustomDropdownField<Subject>(
                    key: Key(Random(100).nextInt(10000).toString()),
                    title: 'Subject'.tr,
                    initialItem: honorBoard.subjectId == null
                        ? null
                        : HonorBoardController
                            .instance.selectedSection.value?.subjects
                            .firstWhere((element) =>
                                element.id ==
                                int.parse(honorBoard.subjectId ?? "0")),
                    items: HonorBoardController
                            .instance.selectedSection.value?.subjects ??
                        [],
                    headerBuilder: (context, item, isSelected) {
                      return Text(item.name);
                    },
                    listItemBuilder: (context, item, isSelected, onItemSelect) {
                      return Text(item.name);
                    },
                    validator: (value) {
                      if (value == null) {
                        return "Please select a subject".tr;
                      }
                      return null;
                    },
                    onChanged: (value) {
                      honorBoard.subjectId = value?.id.toString();
                    },
                  );
                },
              ),
              CustomTextField(
                title: "Title".tr,
                initialValue: honorBoard.title,
                onSaved: (value) {
                  honorBoard.title = value;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "Please enter title".tr;
                  }
                  return null;
                },
              ),
              Obx(
                () => HonorBoardController.instance.isStudentLoaded.value
                    ? StudentListFormField(
                        title: "Students".tr,
                        initialValue:
                            honorBoard.students?.map((e) => e.id ?? 0).toList(),
                        students: HonorBoardController.instance.sectionStudents
                            .toList(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return "Please select students".tr;
                          }
                          return null;
                        },
                        onSaved: (value) {
                          honorBoard.studentIds = value ?? [];
                        },
                      )
                    : const SizedBox.shrink(),
              ),
              SubmitButton(
                  text: isUpdate ? 'Update'.tr : 'Add'.tr,
                  onSubmit: () async {
                    if (isUpdate) {
                      await HonorBoardController.instance
                          .updateHonorBoard(honorBoard);
                    } else {
                      await HonorBoardController.instance
                          .addHonorBoard(honorBoard);
                    }
                  },
                  formKey: formKey)
            ],
          ),
        ),
      ),
    );
  }
}
