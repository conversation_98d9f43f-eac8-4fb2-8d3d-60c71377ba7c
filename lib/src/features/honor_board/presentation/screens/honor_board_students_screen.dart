import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/honor_board.dart';
import '../../../../core/screens/layout_screen.dart';
import '../widgets/honor_board_student_card.dart';

class HonorBoardStudentsScreen extends StatelessWidget {
  const HonorBoardStudentsScreen({super.key, required this.honor});
  final HonorBoard honor;
  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: 'Students'.tr,
      body: GridView.builder(
        itemCount: honor.students?.length,
        padding: const EdgeInsets.all(16),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 15.w,
          mainAxisSpacing: 15.h,
          childAspectRatio: (1 / 1.2),
        ),
        itemBuilder: (_, index) {
          return HonorBoardStudentCard(
            order: index + 1,
            student: honor.students![index],
          );
        },
      ),
    );
  }
}
