import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/honor_board.dart';
import '../../../core/data/models/user.dart';
import '../../../core/utils/helper_function.dart';
import '../../../core/data/models/section_with_subjects.dart';

class HonorBoardController extends BaseController {
  static HonorBoardController get instance => Get.find();

  Rx<bool> isStudentLoaded = Rx<bool>(false);

  Rx<SectionWithSubjects?> selectedSection = Rx<SectionWithSubjects?>(null);

  RxList<HonorBoard> honorBoards = RxList<HonorBoard>([]);

  RxList<User> sectionStudents = RxList<User>([]);

  void getHonorBoards(int? sectionId) {
    callApi(() async {
      var response = await apiProvider.getHonorBoards(sectionId);
      honorBoards.value = response;
      honorBoards.refresh();
      if (honorBoards.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else {
        setPageStatus(PageStatus.loaded);
      }
    });
  }

  void getSectionStudents(int sectionId) {
    isStudentLoaded.value = false;
    callApi(() async {
      var response = await apiProvider.getSectionStudents(sectionId: sectionId);
      sectionStudents.value = response;
      sectionStudents.refresh();
      isStudentLoaded.value = true;
    }, withLoading: false);
  }

  Future<void> addHonorBoard(HonorBoard honorBoard) async {
    await callApi(() async {
      var response = await apiProvider.createHonorBoard(honorBoard);
      honorBoards.add(response.data!);
      honorBoards.refresh();
      Get.back();
      HelperFunction.showSuccessMessage(response.message!);
    }, withLoading: false);
  }

  Future<void> updateHonorBoard(HonorBoard honorBoard) async {
    await callApi(() async {
      print(honorBoard.toJson());
      var response =
          await apiProvider.updateHonorBoard(honorBoard.id ?? 0, honorBoard);
      honorBoards[honorBoards.indexWhere(
          (element) => element.id == honorBoard.id)] = response.data!;
      honorBoards.refresh();
      Get.back();
      HelperFunction.showSuccessMessage(response.message!);
    }, withLoading: false);
  }

  void deleteHonorBoard(int id) {
    callApi(() async {
      var response = await apiProvider.deleteHonorBoard(id);
      honorBoards.removeWhere((element) => element.id == id);
      honorBoards.refresh();
      HelperFunction.showSuccessMessage(response.message!);
    }, withLoading: false);
  }
}
