import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/advertisement.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/extensions.dart';
import '../../../../core/widgets/custom_text.dart';

class AdvertisementsCard extends StatelessWidget {
  final Advertisement advertisement;

  const AdvertisementsCard({super.key, required this.advertisement});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        color: AppColor.whiteColor,
      ),
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: AppColor.primaryColor,
                  width: 0.5,
                ),
              ),
              width: double.infinity,
              child: AspectRatio(
                aspectRatio: 1.8,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20.r),
                  child: SmartFingersImage(
                    imageUrl: advertisement.image ?? "",
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            4.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    if (advertisement.type == "news") 8.verticalSpace,
                    SmartFingersImage(
                      imageUrl: advertisement.type == "advertisement"
                          ? AppAssets.ad
                          : AppAssets.news,
                      color: AppColor.primaryColor,
                      width: 45,
                    ),
                  ],
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Column(
                    children: [
                      8.verticalSpace,
                      SizedBox(
                        width: double.maxFinite,
                        child: Text(
                          advertisement.title ?? "",
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            color: AppColor.primaryColor,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      4.verticalSpace,
                      Row(
                        children: [
                          SmartFingersImage(
                            imageUrl: AppAssets.calender,
                            width: 18,
                          ),
                          SizedBox(width: 4.w),
                          CustomText(
                            text: advertisement.date ?? "",
                            color: Colors.grey.shade700,
                            fontSize: 12.sp,
                          ),
                        ],
                      ),
                      8.verticalSpace,
                      Text(
                        advertisement.description ?? "",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      8.verticalSpace,
                      Align(
                        alignment: const AlignmentDirectional(1, 0),
                        child: Material(
                          borderRadius: BorderRadius.circular(8),
                          color: Get.theme.primaryColor,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(8),
                            onTap: () {
                              Get.toNamed(
                                AppRouter.advertisementDetailScreen,
                                arguments: advertisement,
                              );
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                'Read More'.tr,
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
