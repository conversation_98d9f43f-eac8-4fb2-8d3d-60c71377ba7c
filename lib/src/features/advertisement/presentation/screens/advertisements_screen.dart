import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../controller/advertisement_controller.dart';
import '../widgets/advertisement_card.dart';

class AdvertisementsScreen extends StatelessWidget {
  const AdvertisementsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    String type = Get.arguments;
    AdvertisementController controller =
        Get.put(AdvertisementController(type: type));
    return LayoutScreen(
      floatingActionButton: box.read("userType") == Roles.Supervisor
          ? FloatingActionButton(
              onPressed: () {
                Get.toNamed(AppRouter.addUpdateAdvScreen, arguments: {
                  'isUpdate': false,
                });
              },
              backgroundColor: AppColor.primaryColor,
              child: Icon(
                Icons.add,
                color: AppColor.whiteColor,
              ),
            )
          : null,
      title: type == 'news' ? 'News'.tr : 'Advertisements'.tr,
      controller: controller,
      body: Obx(() => ListView.separated(
            separatorBuilder: (context, index) => 16.verticalSpace,
            padding: const EdgeInsets.all(16),
            itemCount: controller.advertisements.length,
            itemBuilder: (context, index) {
              final advertisement = controller.advertisements[index];
              return AdvertisementsCard(
                advertisement: advertisement,
              );
            },
          )),
    );
  }
}
