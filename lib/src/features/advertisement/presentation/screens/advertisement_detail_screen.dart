import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/advertisement.dart';
import '../../../../core/data/models/daily_post_media.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/extensions.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../about_page/presentation/widgets/attatched_media.dart';

class AdvertisementDetailScreen extends StatelessWidget {
  const AdvertisementDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Advertisement advertisement = Get.arguments;
    return LayoutScreen(
      withBackground: false,
      title: advertisement.title ?? "",
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomCachedNetworkImage(
              imageUrl: advertisement.image ?? "",
              borderRadius: BorderRadius.zero,
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (advertisement.type == "news") 8.verticalSpace,
                  SmartFingersImage(
                    imageUrl: advertisement.type == "advertisement"
                        ? AppAssets.ad
                        : AppAssets.news,
                    color: AppColor.primaryColor,
                    width: 45,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Column(children: [
                      SizedBox(
                        width: double.maxFinite,
                        child: Text(
                          advertisement.title ?? "",
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            color: AppColor.primaryColor,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      4.verticalSpace,
                      Row(
                        children: [
                          SmartFingersImage(
                            imageUrl: AppAssets.calender,
                            width: 18,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            advertisement.date ?? "",
                            style: TextStyle(
                              color: Colors.grey.shade700,
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                      ),
                      16.verticalSpace,
                      SizedBox(
                        width: double.maxFinite,
                        child: Text(
                          advertisement.description ?? "",
                          textAlign: TextAlign.start,
                        ),
                      ),
                      AttatchedMedia(
                        title: "Attached Images".tr,
                        urls: advertisement.images ?? [],
                        type: MediaType.image,
                        maxHeight: 80,
                      ),
                      AttatchedMedia(
                        title: "Attached Vidoes".tr,
                        urls: advertisement.videos ?? [],
                        type: MediaType.video,
                      ),
                    ]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
