import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/advertisement.dart';

class AdvertisementController extends BaseController {
  static AdvertisementController get instance => Get.find();
  final String type;
  RxList<Advertisement> advertisements = RxList<Advertisement>([]);

  AdvertisementController({required this.type});

  @override
  void onInit() {
    getAdvertisement();
    super.onInit();
  }

  void getAdvertisement() {
    callApi(() async {
      var response = await apiProvider.getAdvertisements(type: type);
      advertisements.value = response;
      advertisements.refresh();
      if (advertisements.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else {
        setPageStatus(PageStatus.loaded);
      }
    });
  }
}
