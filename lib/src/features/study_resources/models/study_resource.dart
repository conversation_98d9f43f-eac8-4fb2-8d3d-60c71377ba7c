class StudyResource {
  final int? id;
  final int? teacherId;
  final String? teacherName;
  final String title;
  final String description;
  final String iconPath; // Used for local UI only
  final String type;
  final String? resourceUrl;
  final String? youtubeId;
  final List<Map<String, dynamic>>? sections;
  final List<Map<String, dynamic>>? subjects;
  final String? createdAt;
  final String? formattedDate;

  StudyResource({
    this.id,
    this.teacherId,
    this.teacherName,
    required this.title,
    required this.description,
    required this.iconPath,
    required this.type,
    this.resourceUrl,
    this.youtubeId,
    this.sections,
    this.subjects,
    this.createdAt,
    this.formattedDate,
  });

  factory StudyResource.fromJson(Map<String, dynamic> json) {
    return StudyResource(
      id: json['id'],
      teacherId: json['teacher_id'],
      teacherName: json['teacher_name'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      iconPath: '', // This will be set based on type in the controller
      type: json['type'] ?? '',
      resourceUrl: json['resource_url'],
      youtubeId: json['youtube_id'],
      sections: json['sections'] != null
          ? List<Map<String, dynamic>>.from(json['sections'])
          : null,
      subjects: json['subjects'] != null
          ? List<Map<String, dynamic>>.from(json['subjects'])
          : null,
      createdAt: json['created_at'],
      formattedDate: json['formatted_date'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'teacher_id': teacherId,
      'title': title,
      'description': description,
      'type': type,
      'youtube_id': youtubeId,
    };
  }
}
