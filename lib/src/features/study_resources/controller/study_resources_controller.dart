import 'dart:io';
import 'dart:developer' as developer;
import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/router/app_router.dart';
import '../../../core/utils/app_assets.dart';
import '../../../core/utils/helper_function.dart';
import '../models/study_resource.dart';

class StudyResourcesController extends BaseController {
  static StudyResourcesController get instance => Get.find();

  final RxList<StudyResource> apiResources = <StudyResource>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isCreating = false.obs;
  final RxInt currentPage = 1.obs;
  final RxInt lastPage = 1.obs;
  final RxBool hasMoreData = true.obs;

  // Filter options
  final Rx<int?> selectedSectionId = Rx<int?>(null);
  final Rx<int?> selectedSubjectId = Rx<int?>(null);
  final Rx<String?> selectedType = Rx<String?>(null);

  // Selected resource for viewing
  final Rx<StudyResource?> selectedResource = Rx<StudyResource?>(null);

  @override
  void onReady() {
    apiResources.listen(
      (value) {
        if (value.isEmpty) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      },
    );
    super.onReady();
  }

  @override
  void onInit() {
    super.onInit();
    // Set initial loading state
    setPageStatus(PageStatus.loading);
    // Fetch data with refresh to ensure we start with a clean slate
    fetchStudyResources(refresh: true);
  }

  void getStudyResources({
    int? sectionId,
    int? subjectId,
  }) {
    // This method is now deprecated - use fetchStudyResources(refresh: true) instead
    fetchStudyResources(refresh: true);
  }

  Future<void> fetchStudyResources({bool refresh = false}) async {
    if (refresh) {
      currentPage.value = 1;
      hasMoreData.value = true;
      apiResources.clear();
    }

    if (!hasMoreData.value) return;

    callApi(() async {
      developer.log('Fetching study resources, page: ${currentPage.value}',
          name: 'StudyResourcesController');

      try {
        final response = await apiProvider.getStudyResources(
          sectionId: selectedSectionId.value,
          subjectId: selectedSubjectId.value,
          page: currentPage.value,
        );

        developer.log('API Response received: ${response.data.length} items',
            name: 'StudyResourcesController');

        // Check if we have data
        if (response.data.isEmpty) {
          developer.log('No data received from API',
              name: 'StudyResourcesController');
          if (apiResources.isEmpty) {
            setPageStatus(PageStatus.empty);
          }
          return;
        }

        // Create a set of existing IDs to avoid duplicates
        final existingIds = apiResources.map((r) => r.id).toSet();

        // Set icon paths based on resource type and filter out duplicates
        final newResources = response.data.where((resource) {
          // Only include resources that don't already exist in our list
          return !existingIds.contains(resource.id);
        }).map((resource) {
          String iconPath = '';
          switch (resource.type.toLowerCase()) {
            case 'pdf':
              iconPath = AppAssets.pdfIcon;
              break;
            case 'youtube':
              iconPath = AppAssets.youtubeIcon;
              break;
            case 'url':
              iconPath = AppAssets.linkIcon;
              break;
            default:
              iconPath = AppAssets.documentIcon;
          }

          return StudyResource(
            id: resource.id,
            teacherId: resource.teacherId,
            teacherName: resource.teacherName,
            title: resource.title,
            description: resource.description,
            iconPath: iconPath,
            type: resource.type,
            resourceUrl: resource.resourceUrl,
            youtubeId: resource.youtubeId,
            sections: resource.sections,
            subjects: resource.subjects,
            createdAt: resource.createdAt,
            formattedDate: resource.formattedDate,
          );
        }).toList();

        developer.log('Processed ${newResources.length} unique resources',
            name: 'StudyResourcesController');

        // Only add new resources if there are any
        if (newResources.isNotEmpty) {
          apiResources.addAll(newResources);
          developer.log('Total resources: ${apiResources.length}',
              name: 'StudyResourcesController');
        }

        currentPage.value++;
        lastPage.value = response.lastPage;
        hasMoreData.value =
            currentPage.value <= lastPage.value && newResources.isNotEmpty;

        // Explicitly set the page status to loaded if we have data
        if (apiResources.isNotEmpty) {
          setPageStatus(PageStatus.loaded);
        }
      } catch (e) {
        developer.log('Error fetching study resources: $e',
            name: 'StudyResourcesController', error: e);
        rethrow;
      }
    }, withLoading: refresh);
  }

  Future<void> createStudyResource({
    required String title,
    required String description,
    required String type,
    String? youtubeId,
    List<int>? sectionIds,
    List<int>? subjectIds,
    File? file,
    String? url,
  }) async {
    isCreating.value = true;

    try {
      await callApi(() async {
        developer.log('Creating study resource with type: $type',
            name: 'StudyResourcesController');

        dio.MultipartFile? multipartFile;
        if (file != null) {
          developer.log('Preparing file: ${file.path}',
              name: 'StudyResourcesController');
          multipartFile = await dio.MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          );
        }

        final formData = dio.FormData.fromMap({
          'title': title,
          'description': description,
          'type': type,
        });

        if (youtubeId != null && youtubeId.isNotEmpty) {
          developer.log('Adding YouTube ID: $youtubeId',
              name: 'StudyResourcesController');
          formData.fields.add(MapEntry('youtube_id', youtubeId));
        }

        if (url != null && url.isNotEmpty) {
          developer.log('Adding URL: $url', name: 'StudyResourcesController');
          formData.fields.add(MapEntry('url', url));
        }

        if (multipartFile != null) {
          developer.log('Adding file to form data',
              name: 'StudyResourcesController');
          formData.files.add(MapEntry('file', multipartFile));
        }

        if (sectionIds != null && sectionIds.isNotEmpty) {
          developer.log('Adding section IDs: $sectionIds',
              name: 'StudyResourcesController');
          for (var sectionId in sectionIds) {
            formData.fields.add(MapEntry('sections[]', sectionId.toString()));
          }
        }

        if (subjectIds != null && subjectIds.isNotEmpty) {
          developer.log('Adding subject IDs: $subjectIds',
              name: 'StudyResourcesController');
          for (var subjectId in subjectIds) {
            formData.fields.add(MapEntry('subjects[]', subjectId.toString()));
          }
        }

        developer.log('Sending create request to API',
            name: 'StudyResourcesController');
        var response = await apiProvider.createStudyResource(formData);
        developer.log('API response: ${response.message}',
            name: 'StudyResourcesController');

        Get.back();
        HelperFunction.showSuccessMessage(
            "Study resource created successfully".tr);
        fetchStudyResources(refresh: true);
      }, withLoading: false);
    } catch (e) {
      developer.log('Error creating study resource: $e',
          name: 'StudyResourcesController', error: e);
      HelperFunction.showErrorMessage("Failed to create study resource".tr);
    } finally {
      isCreating.value = false;
    }
  }

  Future<void> updateStudyResource({
    required int id,
    required String title,
    required String description,
    required String type,
    String? youtubeId,
    List<int>? sectionIds,
    List<int>? subjectIds,
    File? file,
    String? url,
  }) async {
    isCreating.value = true;

    try {
      await callApi(() async {
        developer.log('Updating study resource ID: $id, type: $type',
            name: 'StudyResourcesController');

        dio.MultipartFile? multipartFile;
        if (file != null) {
          developer.log('Preparing file: ${file.path}',
              name: 'StudyResourcesController');
          multipartFile = await dio.MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          );
        }

        final formData = dio.FormData.fromMap({
          'resource_id':
              id, // This is the key change - we need to send resource_id
          'title': title,
          'description': description,
          'type': type,
        });

        if (youtubeId != null && youtubeId.isNotEmpty) {
          developer.log('Adding YouTube ID: $youtubeId',
              name: 'StudyResourcesController');
          formData.fields.add(MapEntry('youtube_id', youtubeId));
        }

        if (url != null && url.isNotEmpty) {
          developer.log('Adding URL: $url', name: 'StudyResourcesController');
          formData.fields.add(MapEntry('url', url));
        }

        if (multipartFile != null) {
          developer.log('Adding file to form data',
              name: 'StudyResourcesController');
          formData.files.add(MapEntry('file', multipartFile));
        }

        if (sectionIds != null && sectionIds.isNotEmpty) {
          developer.log('Adding section IDs: $sectionIds',
              name: 'StudyResourcesController');
          for (var sectionId in sectionIds) {
            formData.fields.add(MapEntry(
                'sections[]',
                sectionId
                    .toString())); // Changed from section_ids[] to sections[]
          }
        }

        if (subjectIds != null && subjectIds.isNotEmpty) {
          developer.log('Adding subject IDs: $subjectIds',
              name: 'StudyResourcesController');
          for (var subjectId in subjectIds) {
            formData.fields.add(MapEntry(
                'subjects[]',
                subjectId
                    .toString())); // Changed from subject_ids[] to subjects[]
          }
        }

        developer.log('Sending update request to API',
            name: 'StudyResourcesController');
        var response = await apiProvider.updateStudyResource(formData);
        developer.log('API response: ${response.message}',
            name: 'StudyResourcesController');

        Get.back();
        HelperFunction.showSuccessMessage(
            "Study resource updated successfully".tr);
        fetchStudyResources(refresh: true);
      }, withLoading: false);
    } catch (e) {
      developer.log('Error updating study resource: $e',
          name: 'StudyResourcesController', error: e);
      HelperFunction.showErrorMessage("Failed to update study resource".tr);
    } finally {
      isCreating.value = false;
    }
  }

  Future<void> deleteStudyResource(int id) async {
    try {
      await callApi(() async {
        developer.log('Deleting study resource ID: $id',
            name: 'StudyResourcesController');
        await apiProvider.deleteStudyResource(id);
        developer.log('Study resource deleted successfully',
            name: 'StudyResourcesController');
        HelperFunction.showSuccessMessage(
            "Study resource deleted successfully".tr);
        apiResources.removeWhere((resource) => resource.id == id);
      }, withLoading: false);
    } catch (e) {
      developer.log('Error deleting study resource: $e',
          name: 'StudyResourcesController', error: e);
      HelperFunction.showErrorMessage("Failed to delete study resource".tr);
    }
  }

  void setFilters({
    int? sectionId,
    int? subjectId,
    String? type,
  }) {
    if (sectionId != null) selectedSectionId.value = sectionId;
    if (subjectId != null) selectedSubjectId.value = subjectId;
    if (type != null) selectedType.value = type;

    fetchStudyResources(refresh: true);
  }

  void clearFilters() {
    selectedSectionId.value = null;
    selectedSubjectId.value = null;
    selectedType.value = null;

    fetchStudyResources(refresh: true);
  }

  void viewResource(StudyResource resource) {
    // Navigate to the detail screen instead of setting selectedResource
    Get.toNamed(
      AppRouter.studyResourceDetailScreen,
      arguments: resource,
    );
  }

  // This method is kept for backward compatibility but may not be needed anymore
  void clearSelectedResource() {
    selectedResource.value = null;
  }
}
