import 'dart:io';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/helper_function.dart';
import '../../../../core/utils/youtube_util.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../../../core/data/models/section_with_subjects.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/study_resources_controller.dart';
import '../../models/study_resource.dart';
import '../widgets/pdf_input_widget.dart';
import '../widgets/url_input_widget.dart';
import '../widgets/youtube_input_widget.dart';
import '../widgets/sections_selection_widget.dart';
import '../widgets/subjects_selection_widget.dart';

class ManageStudyResourceScreen extends StatefulWidget {
  final StudyResource? resource;
  final bool isUpdate;

  const ManageStudyResourceScreen({
    super.key,
    this.resource,
    this.isUpdate = false,
  });

  @override
  State<ManageStudyResourceScreen> createState() =>
      _ManageStudyResourceScreenState();
}

class _ManageStudyResourceScreenState extends State<ManageStudyResourceScreen> {
  final formKey = GlobalKey<FormState>();
  late TextEditingController titleController;
  late TextEditingController descriptionController;
  late TextEditingController youtubeIdController;
  late TextEditingController urlController;

  late Rx<String> selectedType;
  final Rx<File?> selectedFile = Rx<File?>(null);
  final RxString selectedFileName = ''.obs;
  final RxString youtubeUrl = ''.obs;

  // For multiple sections and subjects
  final RxList<int> selectedSectionIds = <int>[].obs;
  final RxList<int> selectedSubjectIds = <int>[].obs;

  @override
  void initState() {
    super.initState();

    // Get arguments if coming from named route
    final arguments = Get.arguments;
    StudyResource? resourceFromArgs;
    bool isUpdateFromArgs = false;

    if (arguments != null && arguments is Map<String, dynamic>) {
      resourceFromArgs = arguments['resource'] as StudyResource?;
      isUpdateFromArgs = arguments['isUpdate'] as bool? ?? false;
    }

    final resource = widget.resource ?? resourceFromArgs;
    final isUpdate = widget.isUpdate || isUpdateFromArgs;

    // Initialize controllers with values if editing
    if (isUpdate && resource != null) {
      titleController = TextEditingController(text: resource.title);
      descriptionController = TextEditingController(text: resource.description);
      youtubeIdController =
          TextEditingController(text: resource.youtubeId ?? '');
      urlController = TextEditingController(text: resource.resourceUrl ?? '');
      selectedType = resource.type.obs;
      youtubeUrl.value = resource.youtubeId ?? '';

      // Initialize selected sections and subjects if available
      if (resource.sections != null) {
        for (var section in resource.sections!) {
          if (section['id'] != null) {
            selectedSectionIds.add(section['id'] as int);
          }
        }
      }

      if (resource.subjects != null) {
        for (var subject in resource.subjects!) {
          if (subject['id'] != null) {
            selectedSubjectIds.add(subject['id'] as int);
          }
        }
      }
    } else {
      titleController = TextEditingController();
      descriptionController = TextEditingController();
      youtubeIdController = TextEditingController();
      urlController = TextEditingController();
      selectedType = 'pdf'.obs;
      youtubeUrl.value = '';
    }
  }

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    youtubeIdController.dispose();
    urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<StudyResourcesController>();
    final sections = AuthService.instance.sectionWithSubjects;

    // Get arguments if coming from named route
    final arguments = Get.arguments;
    bool isUpdateFromArgs = false;

    if (arguments != null && arguments is Map<String, dynamic>) {
      isUpdateFromArgs = arguments['isUpdate'] as bool? ?? false;
    }

    final isUpdate = widget.isUpdate || isUpdateFromArgs;

    return LayoutScreen(
      title: isUpdate ? 'Edit Study Resource'.tr : 'Create Study Resource'.tr,
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.all(16.r),
          children: [
            // Title
            CustomTextField(
              title: 'Title'.tr,
              filled: true,
              filledColor: Colors.white,
              hint: 'Enter resource title'.tr,
              controller: titleController,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a title'.tr;
                }
                return null;
              },
            ),

            // Description
            CustomTextField(
              title: 'Description'.tr,
              filled: true,
              filledColor: Colors.white,
              hint: 'Enter resource description'.tr,
              controller: descriptionController,
              maxLine: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a description'.tr;
                }
                return null;
              },
            ),

            // Resource Type
            Padding(
              padding: EdgeInsets.only(top: 16.r, bottom: 8.r),
              child: Text(
                'Resource Type'.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Obx(() => Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 12.r),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: selectedType.value,
                      isExpanded: true,
                      items: [
                        DropdownMenuItem(
                          value: 'pdf',
                          child: Text('PDF'.tr),
                        ),
                        DropdownMenuItem(
                          value: 'youtube',
                          child: Text('YouTube Video'.tr),
                        ),
                        DropdownMenuItem(
                          value: 'url',
                          child: Text('URL'.tr),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          selectedType.value = value;
                        }
                      },
                    ),
                  ),
                )),

            SizedBox(height: 16.h),

            // Dynamic input based on selected type
            Obx(() {
              if (selectedType.value == 'pdf') {
                return _buildPdfInput();
              } else if (selectedType.value == 'youtube') {
                return _buildYoutubeInput();
              } else if (selectedType.value == 'url') {
                return _buildUrlInput();
              }
              return SizedBox();
            }),

            SizedBox(height: 24.h),

            // Sections selection
            if (sections.isNotEmpty) _buildSectionsSelection(sections),

            SizedBox(height: 16.h),

            // Subjects selection
            if (sections.isNotEmpty &&
                sections.any((s) => s.subjects.isNotEmpty))
              _buildSubjectsSelection(sections),

            SizedBox(height: 32.h),

            // Submit button
            Obx(
              () => controller.isCreating.value
                  ? Center(
                      child: CircularProgressIndicator(
                        color: AppColor.primaryColor,
                      ),
                    )
                  : SubmitButton(
                      text: isUpdate ? 'Update'.tr : 'Create'.tr,
                      onSubmit: () async {
                        if (formKey.currentState!.validate()) {
                          // Validate based on type
                          // For PDF type, require a file only for new resources or if there's no existing file
                          if (selectedType.value == 'pdf' &&
                              selectedFile.value == null) {
                            // Get resource from arguments or widget
                            final resource = widget.resource ??
                                (arguments != null &&
                                        arguments is Map<String, dynamic>
                                    ? arguments['resource'] as StudyResource?
                                    : null);

                            // Only show error if it's a new resource or the existing resource has no file
                            if (!isUpdate ||
                                (resource == null ||
                                    resource.resourceUrl == null)) {
                              HelperFunction.showErrorMessage(
                                  'Please select a PDF file'.tr);
                              return;
                            }
                          }

                          // Validate sections
                          if (selectedSectionIds.isEmpty) {
                            HelperFunction.showErrorMessage(
                                'Please select at least one section'.tr);
                            return;
                          }

                          if (isUpdate) {
                            // Get resource from arguments or widget
                            final resource = widget.resource ??
                                (arguments != null &&
                                        arguments is Map<String, dynamic>
                                    ? arguments['resource'] as StudyResource?
                                    : null);

                            if (resource == null) {
                              HelperFunction.showErrorMessage(
                                  'Resource not found'.tr);
                              return;
                            }

                            await controller.updateStudyResource(
                              id: resource.id!,
                              title: titleController.text,
                              description: descriptionController.text,
                              type: selectedType.value,
                              file: selectedType.value == 'pdf'
                                  ? selectedFile.value
                                  : null,
                              youtubeId: selectedType.value == 'youtube'
                                  ? _extractYoutubeId(youtubeIdController.text)
                                  : null,
                              url: selectedType.value == 'url'
                                  ? urlController.text
                                  : null,
                              sectionIds: selectedSectionIds,
                              subjectIds: selectedSubjectIds.isNotEmpty
                                  ? selectedSubjectIds
                                  : null,
                            );
                          } else {
                            developer.log('Creating study resource');
                            await controller.createStudyResource(
                              title: titleController.text,
                              description: descriptionController.text,
                              type: selectedType.value,
                              file: selectedType.value == 'pdf'
                                  ? selectedFile.value
                                  : null,
                              youtubeId: selectedType.value == 'youtube'
                                  ? _extractYoutubeId(youtubeIdController.text)
                                  : null,
                              url: selectedType.value == 'url'
                                  ? urlController.text
                                  : null,
                              sectionIds: selectedSectionIds,
                              subjectIds: selectedSubjectIds.isNotEmpty
                                  ? selectedSubjectIds
                                  : null,
                            );
                          }
                        }
                      },
                      formKey: formKey,
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPdfInput() {
    // Get resource from arguments or widget
    final arguments = Get.arguments;
    final resourceFromArgs =
        arguments != null && arguments is Map<String, dynamic>
            ? arguments['resource'] as StudyResource?
            : null;
    final resource = widget.resource ?? resourceFromArgs;
    final isUpdateFromArgs =
        arguments != null && arguments is Map<String, dynamic>
            ? arguments['isUpdate'] as bool? ?? false
            : false;
    final isUpdate = widget.isUpdate || isUpdateFromArgs;

    return PdfInputWidget(
      selectedFile: selectedFile,
      selectedFileName: selectedFileName,
      currentFileUrl: isUpdate ? resource?.resourceUrl : null,
      isUpdate: isUpdate,
    );
  }

  Widget _buildYoutubeInput() {
    // Get resource from arguments or widget
    final arguments = Get.arguments;
    final resourceFromArgs =
        arguments != null && arguments is Map<String, dynamic>
            ? arguments['resource'] as StudyResource?
            : null;
    final resource = widget.resource ?? resourceFromArgs;
    final isUpdateFromArgs =
        arguments != null && arguments is Map<String, dynamic>
            ? arguments['isUpdate'] as bool? ?? false
            : false;
    final isUpdate = widget.isUpdate || isUpdateFromArgs;

    return YouTubeInputWidget(
      controller: youtubeIdController,
      youtubeUrl: youtubeUrl,
      initialValue: isUpdate ? resource?.youtubeId : null,
      onChanged: (value) {
        // Additional logic if needed
      },
    );
  }

  Widget _buildUrlInput() {
    // Get resource from arguments or widget
    final arguments = Get.arguments;
    final resourceFromArgs =
        arguments != null && arguments is Map<String, dynamic>
            ? arguments['resource'] as StudyResource?
            : null;
    final resource = widget.resource ?? resourceFromArgs;
    final isUpdateFromArgs =
        arguments != null && arguments is Map<String, dynamic>
            ? arguments['isUpdate'] as bool? ?? false
            : false;
    final isUpdate = widget.isUpdate || isUpdateFromArgs;

    return UrlInputWidget(
      controller: urlController,
      initialValue: isUpdate ? resource?.resourceUrl : null,
      onChanged: (value) {
        // Additional logic if needed
      },
    );
  }

  Widget _buildSectionsSelection(List<dynamic> sections) {
    return SectionsSelectionWidget(
      sections: List<SectionWithSubjects>.from(sections),
      selectedSectionIds: selectedSectionIds,
    );
  }

  Widget _buildSubjectsSelection(List<dynamic> sections) {
    return SubjectsSelectionWidget(
      sections: List<SectionWithSubjects>.from(sections),
      selectedSubjectIds: selectedSubjectIds,
    );
  }

  // Removed unused _pickPdfFile method

  /// Extracts YouTube video ID from a URL or returns the ID if already valid
  String _extractYoutubeId(String input) {
    // If it's already a valid video ID (11 characters)
    if (input.length == 11 && RegExp(r'^[a-zA-Z0-9_-]{11}$').hasMatch(input)) {
      return input;
    }

    // If it's a URL, extract the ID
    if (YoutubeUtils.isValidYoutubeUrl(input)) {
      final extractedId = YoutubeUtils.extractYoutubeVideoId(input);
      if (extractedId != null) {
        return extractedId;
      }
    }

    // If all else fails, return the original input
    return input;
  }
}
