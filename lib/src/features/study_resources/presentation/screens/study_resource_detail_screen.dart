import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:rawd_aljenan/src/features/study_resources/utils/resource_utils.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/helper_function.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../models/study_resource.dart';

class StudyResourceDetailScreen extends StatefulWidget {
  final StudyResource? resource;

  const StudyResourceDetailScreen({
    super.key,
    this.resource,
  });

  @override
  State<StudyResourceDetailScreen> createState() =>
      _StudyResourceDetailScreenState();
}

class _StudyResourceDetailScreenState extends State<StudyResourceDetailScreen> {
  YoutubePlayerController? _controller;
  // Track full-screen state
  final RxBool isFullScreen = false.obs;

  @override
  void dispose() {
    // Reset orientation when leaving the screen
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final resourceFromArgs =
        Get.arguments is StudyResource ? Get.arguments : null;
    final currentResource = widget.resource ?? resourceFromArgs;

    if (currentResource == null) {
      return LayoutScreen(
        title: 'Study Resources'.tr,
        body: Center(
          child: EmptyWidget(message: 'Resource not found'.tr),
        ),
      );
    }

    // Use Obx to reactively rebuild when isFullScreen changes
    return Obx(() => LayoutScreen(
          // Hide title when in full-screen mode
          title: isFullScreen.value ? '' : currentResource.title,
          // Hide bodyHeader when in full-screen mode
          bodyHeader:
              isFullScreen.value ? null : _buildResourceHeader(currentResource),
          body: _buildResourceContent(currentResource),
        ));
  }

  Widget _buildResourceHeader(StudyResource resource) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20.r),
          bottomRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Resource type badge
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                decoration: BoxDecoration(
                  color: ResourceUtils.getResourceColor(resource.type)
                      .withAlpha(25),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      resource.iconPath,
                      width: 16.w,
                      height: 16.h,
                      colorFilter: ColorFilter.mode(
                        ResourceUtils.getResourceColor(resource.type),
                        BlendMode.srcIn,
                      ),
                    ),
                    SizedBox(width: 6.w),
                    Text(
                      ResourceUtils.getStandardizedType(resource.type),
                      style: TextStyle(
                        color: ResourceUtils.getResourceColor(resource.type),
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // Teacher and date info
          Row(
            children: [
              if (resource.teacherName != null) ...[
                Icon(
                  Icons.person,
                  size: 16.r,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 6.w),
                CustomText(
                  text: resource.teacherName!,
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 16.w),
              ],
              if (resource.formattedDate != null) ...[
                Icon(
                  Icons.calendar_today,
                  size: 16.r,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 6.w),
                CustomText(
                  text: resource.formattedDate!,
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ],
            ],
          ),

          SizedBox(height: 16.h),

          // Description
          CustomText(
            text: resource.description,
            fontSize: 16.sp,
            color: Colors.black87,
          ),
        ],
      ),
    );
  }

  Widget _buildResourceContent(StudyResource resource) {
    return Container(
      margin: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20.r),
        child: _buildResourceTypeContent(resource),
      ),
    );
  }

  Widget _buildResourceTypeContent(StudyResource resource) {
    switch (resource.type.toLowerCase()) {
      case 'pdf':
        if (resource.resourceUrl != null) {
          return Stack(
            children: [
              PDF(
                swipeHorizontal: true,
                enableSwipe: true,
                pageFling: true,
                pageSnap: true,
                autoSpacing: true,
              ).cachedFromUrl(
                resource.resourceUrl!,
                placeholder: (progress) => Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          value: progress / 100,
                          color: AppColor.primaryColor,
                        ),
                        SizedBox(height: 16.h),
                        CustomText(
                          text: '${'Loading PDF... '.tr}${progress.toInt()}%',
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ],
                    ),
                  ),
                ),
                errorWidget: (error) => SingleChildScrollView(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.r),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error_outline,
                              color: Colors.red, size: 48.r),
                          SizedBox(height: 16.h),
                          CustomText(
                            text: 'Failed to load PDF'.tr,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                          SizedBox(height: 8.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 24.w),
                            child: CustomText(
                              text: error.toString(),
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(height: 16.h),
                          ElevatedButton.icon(
                            onPressed: () async {
                              final Uri url = Uri.parse(resource.resourceUrl!);
                              try {
                                if (await canLaunchUrl(url)) {
                                  await launchUrl(url,
                                      mode: LaunchMode.externalApplication);
                                } else {
                                  HelperFunction.showErrorMessage(
                                      'Could not open URL'.tr);
                                }
                              } catch (e) {
                                HelperFunction.showErrorMessage(
                                    '${'Error opening URL: '.tr}$e');
                              }
                            },
                            icon: const Icon(Icons.open_in_browser),
                            label: Text('Open in Browser'.tr),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColor.primaryColor,
                              foregroundColor: Colors.white,
                            ),
                          ),
                          SizedBox(height: 16.h), // Add some bottom padding
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 16.r,
                right: 16.r,
                child: FloatingActionButton(
                  mini: true,
                  backgroundColor: AppColor.primaryColor,
                  onPressed: () async {
                    final Uri url = Uri.parse(resource.resourceUrl!);
                    try {
                      if (await canLaunchUrl(url)) {
                        await launchUrl(url,
                            mode: LaunchMode.externalApplication);
                      } else {
                        HelperFunction.showErrorMessage(
                            'Could not open URL'.tr);
                      }
                    } catch (e) {
                      HelperFunction.showErrorMessage(
                          '${'Error opening URL: '.tr}$e');
                    }
                  },
                  child: const Icon(Icons.open_in_browser, color: Colors.white),
                ),
              ),
            ],
          );
        }
        return EmptyWidget(message: 'No content available'.tr);

      case 'youtube':
        if (resource.youtubeId != null) {
          // Initialize the controller if it hasn't been created yet
          _controller ??= YoutubePlayerController(
            initialVideoId: resource.youtubeId!,
            flags: const YoutubePlayerFlags(
              autoPlay: false,
              mute: false,
              disableDragSeek: false,
              loop: false,
              isLive: false,
              forceHD: false,
              enableCaption: true,
            ),
          );

          // Use YoutubePlayerBuilder to handle full screen properly
          return YoutubePlayerBuilder(
            onExitFullScreen: () {
              // Update full-screen state
              isFullScreen.value = false;
              // The player forces portrait when exiting full screen, so we need to force it back to the current orientation
              SystemChrome.setPreferredOrientations(DeviceOrientation.values);
            },
            onEnterFullScreen: () {
              // Update full-screen state
              isFullScreen.value = true;
              // Force landscape orientation when entering full screen
              SystemChrome.setPreferredOrientations([
                DeviceOrientation.landscapeLeft,
                DeviceOrientation.landscapeRight,
              ]);
            },
            player: YoutubePlayer(
              controller: _controller!,
              showVideoProgressIndicator: true,
              progressIndicatorColor: AppColor.primaryColor,
              progressColors: ProgressBarColors(
                playedColor: AppColor.primaryColor,
                handleColor: AppColor.primaryColor,
              ),
              onReady: () {
                _controller!.addListener(() {});
              },
              bottomActions: [
                CurrentPosition(),
                ProgressBar(
                  isExpanded: true,
                  colors: ProgressBarColors(
                    playedColor: AppColor.primaryColor,
                    handleColor: AppColor.primaryColor,
                  ),
                ),
                RemainingDuration(),
                // Custom full-screen button implementation
                IconButton(
                  icon: const Icon(
                    Icons.fullscreen,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    // Toggle full-screen state
                    isFullScreen.value = !isFullScreen.value;
                    // Toggle full-screen mode
                    _controller!.toggleFullScreenMode();
                  },
                ),
              ],
              actionsPadding: const EdgeInsets.all(8.0),
              aspectRatio: 16 / 9,
              topActions: [
                const SizedBox(width: 8.0),
                Expanded(
                  child: Text(
                    _controller!.metadata.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18.0,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    Icons.settings,
                    color: Colors.white,
                    size: 25.0,
                  ),
                  onPressed: () {
                    // Show settings
                  },
                ),
              ],
            ),
            builder: (context, player) {
              return Column(
                children: [
                  // YouTube player at the top
                  player,
                  // Scrollable content below
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.all(16.r),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            CustomText(
                              text: 'YouTube Video'.tr,
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                            ),
                            SizedBox(height: 8.h),
                            CustomText(
                              text: '${'Video ID: '.tr}${resource.youtubeId}',
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                            SizedBox(height: 16.h),
                            ElevatedButton.icon(
                              onPressed: () async {
                                final Uri url = Uri.parse(
                                    'https://www.youtube.com/watch?v=${resource.youtubeId}');
                                try {
                                  if (await canLaunchUrl(url)) {
                                    await launchUrl(url,
                                        mode: LaunchMode.externalApplication);
                                  } else {
                                    HelperFunction.showErrorMessage(
                                        'Could not open YouTube'.tr);
                                  }
                                } catch (e) {
                                  HelperFunction.showErrorMessage(
                                      '${'Error opening YouTube: '.tr}$e');
                                }
                              },
                              icon: const Icon(Icons.open_in_browser),
                              label: Text('Open in YouTube'.tr),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                foregroundColor: Colors.white,
                              ),
                            ),
                            SizedBox(height: 16.h), // Add some bottom padding
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        }
        return EmptyWidget(message: 'No content available'.tr);

      case 'url':
        if (resource.resourceUrl != null) {
          return SingleChildScrollView(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(24.r),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 100.w,
                      height: 100.h,
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(25),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.link_rounded,
                        size: 60.r,
                        color: Colors.blue,
                      ),
                    ),
                    SizedBox(height: 24.h),
                    CustomText(
                      text: 'External Link'.tr,
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    SizedBox(height: 16.h),
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 12.h),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: CustomText(
                        text: resource.resourceUrl!,
                        fontSize: 14.sp,
                        color: Colors.blue,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(height: 32.h),
                    ElevatedButton.icon(
                      onPressed: () async {
                        final Uri url = Uri.parse(resource.resourceUrl!);
                        try {
                          if (await canLaunchUrl(url)) {
                            await launchUrl(url,
                                mode: LaunchMode.externalApplication);
                          } else {
                            HelperFunction.showErrorMessage(
                                'Could not open URL'.tr);
                          }
                        } catch (e) {
                          HelperFunction.showErrorMessage(
                              '${'Error opening URL: '.tr}$e');
                        }
                      },
                      icon: const Icon(Icons.open_in_browser),
                      label: Text('Open Link'.tr),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                            horizontal: 24.w, vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                    ),
                    SizedBox(height: 16.h), // Add some bottom padding
                  ],
                ),
              ),
            ),
          );
        }
        return EmptyWidget(message: 'No content available'.tr);

      default:
        return EmptyWidget(message: 'No content available'.tr);
    }
  }
}
