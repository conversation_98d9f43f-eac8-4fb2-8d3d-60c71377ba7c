import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../../../core/utils/app_consts.dart';
import '../../controller/study_resources_controller.dart';
import '../widgets/study_resources_list.dart';

class StudyResourcesScreen extends StatelessWidget {
  const StudyResourcesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(StudyResourcesController());
    final userRole = AuthService.instance.user.value?.role;
    final isTeacher = userRole == Roles.Teacher;

    return LayoutScreen(
      title: 'Study Resources'.tr,
      controller: controller,
      bodyHeader: SectionWithSubjectsWidget(
        showSections: isTeacher,
        showSubjects: true,
        onSectionChanged: (sectionId) {
          controller.setFilters(sectionId: sectionId);
        },
        onSubjectChanged: (sectionId, subjectId) {
          controller.setFilters(subjectId: subjectId);
        },
      ),
      floatingActionButton: isTeacher
          ? FloatingActionButton(
              onPressed: () {
                Get.toNamed(AppRouter.manageStudyResourceScreen);
              },
              backgroundColor: AppColor.primaryColor,
              child: const Icon(Icons.add),
            )
          : null,
      body: StudyResourcesList(controller: controller),
    );
  }
}
