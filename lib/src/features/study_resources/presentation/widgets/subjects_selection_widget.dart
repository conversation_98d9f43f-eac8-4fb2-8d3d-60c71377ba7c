import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/section_with_subjects.dart';
import '../../../../core/utils/app_color.dart';

class SubjectsSelectionWidget extends StatelessWidget {
  final List<SectionWithSubjects> sections;
  final RxList<int> selectedSubjectIds;

  const SubjectsSelectionWidget({
    Key? key,
    required this.sections,
    required this.selectedSubjectIds,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Subjects'.tr,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey.shade200),
          ),
          padding: EdgeInsets.all(12.r),
          child: Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children:
                sections.expand((section) => section.subjects).map((subject) {
              return Obx(() => FilterChip(
                    label: Text(subject.name),
                    selected: selectedSubjectIds.contains(subject.id),
                    selectedColor: AppColor.primaryColor.withAlpha(50),
                    checkmarkColor: AppColor.primaryColor,
                    onSelected: (selected) {
                      if (selected) {
                        selectedSubjectIds.add(subject.id);
                      } else {
                        selectedSubjectIds.remove(subject.id);
                      }
                    },
                  ));
            }).toList(),
          ),
        ),
      ],
    );
  }
}
