import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/study_resources_controller.dart';
import '../../models/study_resource.dart';
import '../../utils/resource_utils.dart';

class ApiResourceCard extends StatelessWidget {
  final StudyResource resource;
  final StudyResourcesController controller;

  const ApiResourceCard({
    Key? key,
    required this.resource,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final userRole = AuthService.instance.user.value?.role;
    final isTeacher = userRole == Roles.Teacher;
    final isOwner =
        isTeacher && resource.teacherId == AuthService.instance.user.value?.id;

    return Card(
      margin: EdgeInsets.only(bottom: 16.r),
      elevation: 3,
      shadowColor: AppColor.primaryColor.withAlpha(25),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15.r),
        side: BorderSide(
          color: AppColor.primaryColor.withAlpha(25),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          controller.viewResource(resource);
        },
        borderRadius: BorderRadius.circular(15.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Resource type icon
                  ResourceTypeIcon(resource: resource),
                  SizedBox(width: 16.w),

                  // Resource details
                  Expanded(
                    child: ResourceDetails(resource: resource),
                  ),

                  // Actions menu for teacher's own resources
                  if (isOwner)
                    ResourceActionsMenu(
                      resource: resource,
                      controller: controller,
                    ),
                ],
              ),

              // Description
              Padding(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                child: CustomText(
                  text: resource.description,
                  fontSize: 14.sp,
                  color: Colors.grey[700],
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      controller.viewResource(resource);
                    },
                    icon: Icon(Icons.visibility, size: 18.r),
                    label: Text('View'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColor.primaryColor,
                      foregroundColor: Colors.white,
                      padding:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ResourceTypeIcon extends StatelessWidget {
  final StudyResource resource;

  const ResourceTypeIcon({
    Key? key,
    required this.resource,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60.w,
      height: 60.h,
      decoration: BoxDecoration(
        color: ResourceUtils.getResourceColor(resource.type).withAlpha(25),
        borderRadius: BorderRadius.circular(15.r),
      ),
      child: Center(
        child: SvgPicture.asset(
          resource.iconPath,
          width: 30.w,
          height: 30.h,
          colorFilter: ColorFilter.mode(
              ResourceUtils.getResourceColor(resource.type), BlendMode.srcIn),
        ),
      ),
    );
  }
}

class ResourceDetails extends StatelessWidget {
  final StudyResource resource;

  const ResourceDetails({
    Key? key,
    required this.resource,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Resource title
        CustomText(
          text: resource.title,
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
        ),

        // Resource type badge
        ResourceTypeBadge(resource: resource),

        // Teacher name
        if (resource.teacherName != null)
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Row(
              children: [
                Icon(
                  Icons.person,
                  size: 14.r,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 4.w),
                CustomText(
                  text: resource.teacherName!,
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),

        // Date
        if (resource.formattedDate != null)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14.r,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 4.w),
                CustomText(
                  text: resource.formattedDate!,
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
      ],
    );
  }
}

class ResourceTypeBadge extends StatelessWidget {
  final StudyResource resource;

  const ResourceTypeBadge({
    Key? key,
    required this.resource,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 6.h),
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 3.h),
      decoration: BoxDecoration(
        color: ResourceUtils.getResourceColor(resource.type).withAlpha(25),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        ResourceUtils.getStandardizedType(resource.type).tr.toUpperCase(),
        style: TextStyle(
          color: ResourceUtils.getResourceColor(resource.type),
          fontSize: 10.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class ResourceActionsMenu extends StatelessWidget {
  final StudyResource resource;
  final StudyResourcesController controller;

  const ResourceActionsMenu({
    Key? key,
    required this.resource,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: AppColor.primaryColor,
      ),
      onSelected: (value) {
        if (value == 'edit') {
          Get.toNamed(
            AppRouter.manageStudyResourceScreen,
            arguments: {
              'resource': resource,
              'isUpdate': true,
            },
          );
        } else if (value == 'delete') {
          _showDeleteConfirmation(context, controller, resource);
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, color: AppColor.primaryColor),
              SizedBox(width: 8.w),
              Text('Edit'.tr),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 8.w),
              Text('Delete'.tr),
            ],
          ),
        ),
      ],
    );
  }

  void _showDeleteConfirmation(BuildContext context,
      StudyResourcesController controller, StudyResource resource) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Study Resource'.tr),
        content:
            Text('Are you sure you want to delete this study resource?'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () {
              controller.deleteStudyResource(resource.id!);
              Get.back();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text('Delete'.tr),
          ),
        ],
      ),
    );
  }
}
