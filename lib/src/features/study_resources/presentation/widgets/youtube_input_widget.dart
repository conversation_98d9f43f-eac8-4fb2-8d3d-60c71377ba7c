import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'dart:developer' as developer;

import '../../../../core/utils/youtube_util.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';

class YouTubeInputWidget extends StatelessWidget {
  final TextEditingController controller;
  final RxString youtubeUrl;
  final String? initialValue;
  final Function(String)? onChanged;

  const YouTubeInputWidget({
    Key? key,
    required this.controller,
    required this.youtubeUrl,
    this.initialValue,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Initialize controller if needed
    if (initialValue != null && controller.text.isEmpty) {
      controller.text = initialValue!;
      youtubeUrl.value = initialValue!;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomTextField(
          title: 'YouTube Video URL'.tr,
          filled: true,
          filledColor: Colors.white,
          hint: 'https://www.youtube.com/watch?v=HH88-x2pwcA'.tr,
          controller: controller,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a YouTube URL'.tr;
            }

            // Check if it's already a video ID (11 characters)
            if (value.length == 11 &&
                RegExp(r'^[a-zA-Z0-9_-]{11}$').hasMatch(value)) {
              return null;
            }

            // Validate as a URL
            if (!YoutubeUtils.isValidYoutubeUrl(value)) {
              return 'Please enter a valid YouTube URL'.tr;
            }

            return null;
          },
          onChanged: (value) {
            // Update the reactive variable to trigger UI updates
            youtubeUrl.value = value;

            // If it's a full URL, extract the ID and store it
            if (value.isNotEmpty && value.length > 11) {
              if (YoutubeUtils.isValidYoutubeUrl(value)) {
                final extractedId = YoutubeUtils.extractYoutubeVideoId(value);
                if (extractedId != null && extractedId != value) {
                  // We don't update the text field, but we'll use the extracted ID when submitting
                  // This allows users to enter the full URL but we store just the ID
                  developer.log(
                      'Extracted YouTube ID: $extractedId from URL: $value');
                }
              }
            }

            // Call the parent's onChanged if provided
            if (onChanged != null) {
              onChanged!(value);
            }
          },
        ),
        Padding(
          padding: EdgeInsets.only(left: 8.r, top: 4.r),
          child: Text(
            'Enter a YouTube URL or video ID'.tr,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
            ),
          ),
        ),

        // Preview of extracted ID
        Obx(() {
          final value = youtubeUrl.value;
          if (value.isNotEmpty) {
            String? videoId;

            // Check if it's already a video ID
            if (value.length == 11 &&
                RegExp(r'^[a-zA-Z0-9_-]{11}$').hasMatch(value)) {
              videoId = value;
            } else if (YoutubeUtils.isValidYoutubeUrl(value)) {
              videoId = YoutubeUtils.extractYoutubeVideoId(value);
            }

            if (videoId != null) {
              return Container(
                margin: EdgeInsets.only(top: 12.h),
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(25),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.video_library,
                            color: Colors.red, size: 24.r),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Text(
                            'Video ID: '.tr + videoId,
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: AspectRatio(
                        aspectRatio: 16 / 9,
                        child: Image.network(
                          'https://img.youtube.com/vi/$videoId/0.jpg',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[300],
                              child: Center(
                                child: Icon(
                                  Icons.error_outline,
                                  color: Colors.red,
                                  size: 40.r,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
          }
          return SizedBox();
        }),
      ],
    );
  }

  /// Extracts YouTube video ID from a URL or returns the ID if already valid
  static String extractYoutubeId(String input) {
    // If it's already a valid video ID (11 characters)
    if (input.length == 11 && RegExp(r'^[a-zA-Z0-9_-]{11}$').hasMatch(input)) {
      return input;
    }

    // If it's a URL, extract the ID
    if (YoutubeUtils.isValidYoutubeUrl(input)) {
      final extractedId = YoutubeUtils.extractYoutubeVideoId(input);
      if (extractedId != null) {
        return extractedId;
      }
    }

    // If all else fails, return the original input
    return input;
  }
}
