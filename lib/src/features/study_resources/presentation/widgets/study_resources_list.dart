import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/enums/page_status.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../../../core/widgets/errors/loading_widget.dart';
import '../../controller/study_resources_controller.dart';
import 'api_resource_card.dart';

class StudyResourcesList extends StatelessWidget {
  final StudyResourcesController controller;

  const StudyResourcesList({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.pageStatus.value == PageStatus.loading) {
        return const LoadingWidget();
      }

      if (controller.pageStatus.value == PageStatus.empty ||
          controller.apiResources.isEmpty) {
        return EmptyWidget(message: 'No study resources available'.tr);
      }

      return RefreshIndicator(
        onRefresh: () => controller.fetchStudyResources(refresh: true),
        child: ListView.builder(
          padding: EdgeInsets.only(right: 16, left: 16, top: 16, bottom: 100),
          itemCount: controller.apiResources.length +
              (controller.hasMoreData.value ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == controller.apiResources.length) {
              // Load more indicator
              if (controller.hasMoreData.value) {
                controller.fetchStudyResources();
                return const LoadMoreIndicator();
              }
              return const SizedBox();
            }

            final resource = controller.apiResources[index];
            return ApiResourceCard(
              resource: resource,
              controller: controller,
            );
          },
        ),
      );
    });
  }
}

class LoadMoreIndicator extends StatelessWidget {
  const LoadMoreIndicator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }
}
