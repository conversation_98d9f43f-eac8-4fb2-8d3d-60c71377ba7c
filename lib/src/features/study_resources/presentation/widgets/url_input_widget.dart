import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';

class UrlInputWidget extends StatelessWidget {
  final TextEditingController controller;
  final String? initialValue;
  final Function(String)? onChanged;

  const UrlInputWidget({
    Key? key,
    required this.controller,
    this.initialValue,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Initialize controller if needed
    if (initialValue != null && controller.text.isEmpty) {
      controller.text = initialValue!;
    }

    return CustomTextField(
      title: 'URL'.tr,
      filled: true,
      filledColor: Colors.white,
      hint: 'https://example.com'.tr,
      controller: controller,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a URL'.tr;
        }
        if (!Uri.parse(value).isAbsolute) {
          return 'Please enter a valid URL'.tr;
        }
        return null;
      },
      onChanged: onChanged,
    );
  }
}
