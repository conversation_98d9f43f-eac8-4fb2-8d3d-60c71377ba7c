import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/section_with_subjects.dart';
import '../../../../core/utils/app_color.dart';

class SectionsSelectionWidget extends StatelessWidget {
  final List<SectionWithSubjects> sections;
  final RxList<int> selectedSectionIds;

  const SectionsSelectionWidget({
    Key? key,
    required this.sections,
    required this.selectedSectionIds,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Sections'.tr,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey.shade200),
          ),
          padding: EdgeInsets.all(12.r),
          child: Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: sections.map((section) {
              return Obx(() => FilterChip(
                    label: Text(section.name),
                    selected: selectedSectionIds.contains(section.id),
                    selectedColor: AppColor.primaryColor.withAlpha(50),
                    checkmarkColor: AppColor.primaryColor,
                    onSelected: (selected) {
                      if (selected) {
                        selectedSectionIds.add(section.id);
                      } else {
                        selectedSectionIds.remove(section.id);
                      }
                    },
                  ));
            }).toList(),
          ),
        ),
      ],
    );
  }
}
