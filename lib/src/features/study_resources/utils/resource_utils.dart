import 'package:flutter/material.dart';
import 'package:get/get_utils/get_utils.dart';
import '../../../core/utils/app_color.dart';

class ResourceUtils {
  static Color getResourceColor(String type) {
    switch (type.toLowerCase()) {
      case 'pdf':
        return Colors.orangeAccent;
      case 'youtube':
        return Colors.red;
      case 'url':
        return AppColor.primaryColor;
      default:
        return AppColor.primaryColor;
    }
  }

  static String getStandardizedType(String type) {
    switch (type.toLowerCase()) {
      case 'pdf':
        return 'PDF'.tr;
      case 'youtube':
        return 'YouTube Video'.tr;
      case 'url':
        return 'External Link'.tr;
      default:
        return type;
    }
  }
}
