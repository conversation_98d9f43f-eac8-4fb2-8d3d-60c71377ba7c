import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/daily_post.dart';
import '../../../core/data/providers/api_provider.dart';
import '../../../core/data/providers/api_provider_impl.dart';
import '../../../core/utils/helper_function.dart';

class DailyPostController extends BaseController {
  static DailyPostController instance = Get.find();

  // Create a custom API provider implementation for tracking upload progress
  late final ApiProviderImpl _apiProviderImpl;

  RxList<DailyPost> posts = RxList<DailyPost>([]);

  RxInt sectionId = 0.obs;

  RxInt page = 1.obs;

  DailyPostController() {
    _apiProviderImpl = ApiProviderImpl(apiProvider);
  }

  RxInt lastPage = 1.obs;
  void getPosts({
    int? sectionId,
    int page = 1,
  }) {
    callApi(() async {
      this.sectionId.value = sectionId ?? this.sectionId.value;
      this.page.value = page;
      if (page == 1) {
        setPageStatus(PageStatus.loading);
      }
      var response = await apiProvider.getDailyUpdates(
        sectionId: sectionId,
        page: page,
      );
      if (page == 1) {
        posts.value = response.dailyPost ?? [];
        posts.refresh();
        lastPage.value = response.lastPage ?? 1;
        if (response.itemsCount == 0) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      } else {
        posts.addAll(response.dailyPost ?? []);
        posts.refresh();
      }
    }, withLoading: false);
  }

  void loadMore() {
    Get.log("Loading more posts");
    getPosts(sectionId: sectionId.value, page: page.value + 1);
  }

  bool canLoadMore() {
    Get.log(
        "Checking if can load more: page=${page.value}, lastPage=${lastPage.value}");
    return page.value < lastPage.value;
  }

  Future<void> addPost(DailyPost post) async {
    await callApi(() async {
      // Compress images before uploading
      await post.compressImages();

      // Use the custom API provider implementation for tracking upload progress
      var response =
          await _apiProviderImpl.createDailyUpdates(post.toFormData());
      Get.back();
      posts.insert(0, response.data!);
      HelperFunction.showSuccessMessage(response.message ?? "");
    }, withLoading: false);
  }

  Future<void> updatePost(DailyPost post) async {
    await callApi(() async {
      // Compress images before uploading
      await post.compressImages();

      // Use the custom API provider implementation for tracking upload progress
      var response = await _apiProviderImpl.updateDailyUpdates(
          post.id ?? 0, post.toFormData());
      Get.back();
      posts[posts.indexWhere((element) => element.id == post.id)] =
          response.data!;
      HelperFunction.showSuccessMessage(response.message ?? "");
    }, withLoading: false);
  }

  Future<void> deletePost(int id) async {
    await callApi(() async {
      var response = await apiProvider.deleteDailyUpdates(id);
      HelperFunction.showSuccessMessage(response.message ?? "");
      posts.removeWhere((element) => element.id == id);
    }, withLoading: false);
  }

  void likePost(int id) async {
    await callApi(() async {
      await apiProvider.toggleLike(id);
    }, withLoading: false);
  }
}
