import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../../core/data/models/daily_post_media.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../../core/widgets/media/youtube_thumnail_widget.dart';

import '../../../../../core/widgets/media/video_thumbnail_widget.dart';

class MediaViewWidet extends StatelessWidget {
  const MediaViewWidet({
    super.key,
    required this.mediaItem,
    this.onDelete,
  });
  final DailyPostMedia mediaItem;
  final VoidCallback? onDelete;
  @override
  Widget build(BuildContext context) {
    Widget widget;
    if (mediaItem.file != null && mediaItem.type == MediaType.image) {
      widget = ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.file(
          mediaItem.file!,
          fit: BoxFit.cover,
        ),
      );
    } else if (mediaItem.file != null && mediaItem.type == MediaType.pdf) {
      // Local PDF file preview
      widget = Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.picture_as_pdf,
                size: 40,
                color: Get.theme.primaryColor,
              ),
              const SizedBox(height: 8),
              Text(
                "PDF".tr,
                style: TextStyle(
                  color: Get.theme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  mediaItem.file!.path.split('/').last,
                  style: const TextStyle(
                    fontSize: 10,
                    overflow: TextOverflow.ellipsis,
                  ),
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      );
    } else if (mediaItem.file == null &&
        mediaItem.fileUrl != null &&
        mediaItem.type == MediaType.image) {
      widget = CustomCachedNetworkImage(
        imageUrl: mediaItem.fileUrl!,
        fit: BoxFit.cover,
        borderRadius: BorderRadius.circular(12),
      );
    } else if (mediaItem.type == MediaType.video) {
      Get.log(mediaItem.file?.path ?? "");
      widget = VideoThumbnailWidget(
        key: ValueKey(mediaItem.fileUrl ?? mediaItem.file?.path ?? ""),
        url: mediaItem.fileUrl ?? mediaItem.file?.path ?? "",
      );
    } else if (mediaItem.youtubeId != null &&
        mediaItem.type == MediaType.youtube) {
      widget = YoutubeThumbnailWidget(
        key: ValueKey(mediaItem.youtubeId ?? ""),
        youtubeId: mediaItem.youtubeId ?? "",
      );
    } else if (mediaItem.file == null &&
        mediaItem.fileUrl != null &&
        mediaItem.type == MediaType.pdf) {
      // Remote PDF file preview
      widget = Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.picture_as_pdf,
                size: 40,
                color: Get.theme.primaryColor,
              ),
              const SizedBox(height: 8),
              Text(
                "PDF".tr,
                style: TextStyle(
                  color: Get.theme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  "PDF Document".tr,
                  style: const TextStyle(
                    fontSize: 10,
                    overflow: TextOverflow.ellipsis,
                  ),
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      widget = const SizedBox();
    }

    return Stack(
      children: [
        AspectRatio(
          aspectRatio: 1,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: SizedBox(
              height: double.maxFinite,
              width: double.maxFinite,
              child: widget,
            ),
          ),
        ),
        PositionedDirectional(
          top: 0,
          end: 0,
          child: Material(
            color: AppColor.redColor.withAlpha(179), // 0.7 * 255 = 179
            borderRadius: BorderRadius.circular(12),
            child: InkWell(
              onTap: onDelete,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: SvgPicture.asset(
                  AppAssets.delete,
                  width: 20,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }
}
