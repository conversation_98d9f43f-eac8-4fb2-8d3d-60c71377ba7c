import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../core/data/models/daily_post_media.dart';
import '../../../../../core/data/models/view_models/media_vm.dart';
import 'add_media_button.dart';
import 'media_view_widget.dart';

class MediaPickerFormField extends StatelessWidget {
  const MediaPickerFormField({
    super.key,
    this.title,
    this.color,
    this.margin,
    this.onSaved,
    this.initialValue,
    this.onChanged,
  });
  final String? title;
  final Color? color;
  final EdgeInsetsGeometry? margin;
  final void Function(MediaVm? value)? onSaved;
  final void Function(MediaVm? value)? onChanged;
  final MediaVm? initialValue;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: double.maxFinite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Padding(
              padding: EdgeInsetsDirectional.only(start: 8, bottom: 8.0.h),
              child: Text(
                title!,
                style: TextStyle(
                  color: color ?? Colors.grey[900],
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          FormField<MediaVm>(
            initialValue: initialValue ?? MediaVm(mediaItems: []),
            onSaved: onSaved,
            builder: (field) {
              return SizedBox(
                height: 110,
                child: ListView.separated(
                  key: Key(Random.secure().toString()),
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return AddMediaButton(
                        onFileSelected: (files, mediaType) {
                          var value = field.value;
                          if (value == null) {
                            value = MediaVm(
                              mediaItems: [
                                ...files.map((file) => DailyPostMedia(
                                    file: file, type: mediaType)),
                              ],
                            );
                          } else {
                            for (var file in files) {
                              value.mediaItems.insert(
                                0,
                                DailyPostMedia(
                                  file: file,
                                  type: mediaType,
                                ),
                              );
                            }
                          }
                          field.didChange(value);
                          if (onChanged != null) onChanged!(value);
                        },
                        onUrlInserted: (url) {
                          var value = field.value;
                          if (value == null) {
                            value = MediaVm(
                              mediaItems: [
                                DailyPostMedia(
                                  youtubeId: url,
                                  type: MediaType.youtube,
                                ),
                              ],
                            );
                          } else {
                            value.mediaItems.insert(
                              0,
                              DailyPostMedia(
                                youtubeId: url,
                                type: MediaType.youtube,
                              ),
                            );
                          }
                          field.didChange(value);
                          if (onChanged != null) onChanged!(value);
                        },
                      );
                    } else {
                      return MediaViewWidet(
                        // key: Key(
                        //     field.value!.mediaItems[index - 1].id?.toString() ??
                        //         ""),
                        mediaItem: field.value!.mediaItems[index - 1],
                        onDelete: () {
                          var value = field.value;
                          if (field.value!.mediaItems[index - 1].id != null) {
                            value!.deletedItems
                                .add(field.value!.mediaItems[index - 1].id!);
                          }
                          value!.mediaItems.removeAt(index - 1);
                          field.didChange(value);
                          if (onChanged != null) onChanged!(value);
                        },
                      );
                    }
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(width: 8.h);
                  },
                  itemCount: (field.value?.mediaItems.length ?? 0) + 1,
                  scrollDirection: Axis.horizontal,
                  shrinkWrap: true,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
