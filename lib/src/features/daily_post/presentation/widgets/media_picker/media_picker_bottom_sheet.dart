import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../../core/data/models/daily_post_media.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/youtube_util.dart';
import '../../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../../core/widgets/buttons/submit_button.dart';
import '../../../../../core/widgets/form_fields/custom_text_field.dart';

class MediaPickerBottomSheet extends StatelessWidget {
  const MediaPickerBottomSheet({
    super.key,
    required this.onFileSelected,
    required this.onUrlInserted,
  });
  final void Function(List<File> files, MediaType mediaType) onFileSelected;
  final void Function(String url) onUrlInserted;
  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      title: "Add image/video".tr,
      body: SafeArea(
        child: Column(
          children: [
            ListTile(
              leading: const Icon(
                Icons.image_outlined,
              ),
              title: Text("Image".tr),
              onTap: () async {
                var source = await showSourceTypeBottomSheet();
                if (source != null) {
                  ImagePicker picker = ImagePicker();

                  if (source == ImageSource.camera) {
                    var xfile = await picker.pickImage(
                      source: source,
                    );
                    if (xfile != null) {
                      var file = File(xfile.path);
                      onFileSelected([file], MediaType.image);
                    }
                  }

                  var xfiles = await picker.pickMultiImage();
                  var files = xfiles.map((e) => File(e.path)).toList();
                  onFileSelected(files, MediaType.image);
                  Get.back();
                }
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.video_file_outlined,
              ),
              title: Text("Video".tr),
              onTap: () async {
                var source = await showSourceTypeBottomSheet();
                if (source != null) {
                  ImagePicker picker = ImagePicker();
                  var xfile = await picker.pickVideo(
                    source: source,
                  );
                  if (xfile != null) {
                    var file = File(xfile.path);
                    onFileSelected([file], MediaType.video);
                  }
                  Get.back();
                }
              },
            ),
            ListTile(
              leading: SvgPicture.asset(
                AppAssets.youtube,
                width: 20,
              ),
              title: Text("Youtube url".tr),
              onTap: () async {
                String? url = await showYoutubeUrlBottomSheet();
                if (url != null) {
                  onUrlInserted(url);
                }
                Get.back();
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.picture_as_pdf,
              ),
              title: Text("PDF".tr),
              onTap: () async {
                FilePickerResult? result = await FilePicker.platform.pickFiles(
                  type: FileType.custom,
                  allowedExtensions: ['pdf'],
                );

                if (result != null && result.files.isNotEmpty) {
                  File file = File(result.files.first.path!);
                  onFileSelected([file], MediaType.pdf);
                }
                Get.back();
              },
            ),
          ],
        ),
      ),
    );
  }

  static Future<ImageSource?> showSourceTypeBottomSheet() async {
    return await Get.bottomSheet<ImageSource>(
      CustomBottomSheet(
        title: "Choose source".tr,
        body: Column(
          children: [
            ListTile(
              leading: const Icon(
                Icons.image_outlined,
              ),
              title: Text("Camera".tr),
              onTap: () {
                Get.back(result: ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.video_file_outlined,
              ),
              title: Text("Gallery".tr),
              onTap: () {
                Get.back(result: ImageSource.gallery);
              },
            ),
          ],
        ),
      ),
      isDismissible: false,
    );
  }

  static Future<String?> showYoutubeUrlBottomSheet() async {
    String? url;
    var formKey = GlobalKey<FormState>();
    return await Get.bottomSheet<String>(
      CustomBottomSheet(
        title: "Add youtube url".tr,
        body: Form(
          key: formKey,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                CustomTextField(
                  title: "Youtube url".tr,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a YouTube URL'.tr;
                    }

                    if (!YoutubeUtils.isValidYoutubeUrl(value)) {
                      return 'Please enter a valid YouTube URL'.tr;
                    }
                    return null;
                  },
                  onSaved: (value) {
                    url = YoutubeUtils.extractYoutubeVideoId(value ?? "");
                  },
                ),
                SubmitButton(
                  text: "Add url".tr,
                  onSubmit: () async => Get.back(result: url),
                  formKey: formKey,
                )
              ],
            ),
          ),
        ),
      ),
      isDismissible: false,
    );
  }
}
