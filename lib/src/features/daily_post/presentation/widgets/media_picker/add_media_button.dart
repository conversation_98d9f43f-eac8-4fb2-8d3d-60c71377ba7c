import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../../core/data/models/daily_post_media.dart';
import '../../../../../core/utils/app_assets.dart';
import 'media_picker_bottom_sheet.dart';

class AddMediaButton extends StatelessWidget {
  const AddMediaButton({
    super.key,
    required this.onFileSelected,
    required this.onUrlInserted,
  });
  final void Function(List<File> files, MediaType mediaType) onFileSelected;
  final void Function(String url) onUrlInserted;
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Get.bottomSheet(
            MediaPickerBottomSheet(
              onFileSelected: onFileSelected,
              onUrlInserted: onUrlInserted,
            ),
          );
        },
        child: AspectRatio(
          aspectRatio: 1,
          child: Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Get.theme.primaryColor,
              ),
            ),
            child: SizedBox(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      AppAssets.cameraBlueImage,
                      fit: BoxFit.contain,
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Text(
                      'Add image/video'.tr,
                      style: TextStyle(
                        fontSize: 11,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
