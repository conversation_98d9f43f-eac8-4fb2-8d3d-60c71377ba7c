import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../../core/data/models/daily_post.dart';
import '../../../../../core/router/app_router.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/app_color.dart';
import '../../../../../core/utils/helper_function.dart';
import '../../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../auth/services/auth_service.dart';
import '../../../controller/daily_post_controller.dart';
import 'post_media_slider.dart';

import 'post_action_popup_item.dart';

class PostCard extends StatefulWidget {
  const PostCard({
    super.key,
    required this.post,
  });
  final DailyPost post;

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  bool liked = false;
  int likeCount = 0;
  @override
  void initState() {
    liked = widget.post.liked ?? false;
    likeCount = widget.post.likesCount ?? 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    CustomCachedNetworkImage(
                      imageUrl: widget.post.userImage ?? "",
                      borderRadius: BorderRadius.circular(100),
                      width: 50,
                      height: 50,
                    ),
                    const SizedBox(
                      width: 16,
                    ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.post.userName ?? "",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Get.theme.primaryColor,
                            ),
                          ),
                          2.verticalSpace,
                          Text(
                            widget.post.createdAt ?? "",
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (AuthService.instance.user.value?.role == "teacher" &&
                        AuthService.instance.user.value?.id ==
                            widget.post.userId)
                      PopupMenuButton(
                        position: PopupMenuPosition.under,
                        offset: const Offset(0, 10),
                        icon: Icon(
                          Icons.more_horiz_rounded,
                          size: 30,
                          color: Colors.grey[700],
                        ),
                        itemBuilder: (BuildContext context) {
                          return [
                            PopupMenuItem(
                              child: PostActionPopupItem(
                                icon: AppAssets.edit,
                                label: "Edit",
                              ),
                              onTap: () {
                                Get.toNamed(
                                  AppRouter.managePostScreen,
                                  arguments: {
                                    "isUpdate": true,
                                    "post": widget.post.clone(),
                                  },
                                );
                              },
                            ),
                            PopupMenuItem(
                              child: PostActionPopupItem(
                                icon: AppAssets.delete,
                                label: 'Delete',
                                color: Colors.red,
                              ),
                              onTap: () {
                                setState(() {
                                  DailyPostController.instance
                                      .deletePost(widget.post.id ?? 0);
                                });
                              },
                            )
                          ];
                        },
                      ),
                  ],
                ),
              ],
            ),
          ),
          if (widget.post.description != null)
            Padding(
              padding: const EdgeInsets.only(
                right: 16,
                left: 16,
                top: 8,
                bottom: 16,
              ),
              child: Text(widget.post.description ?? ""),
            ),
          PostMediaSlider(
            media: widget.post.dailyPostMedia ?? [],
          ),
          Row(
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    liked = !liked;
                    if (liked) {
                      likeCount++;
                    } else {
                      likeCount--;
                    }
                    DailyPostController.instance.likePost(widget.post.id ?? 0);
                  });
                },
                icon: Icon(
                  liked ? Icons.favorite : Icons.favorite_border,
                  color: liked ? AppColor.redColor : Colors.grey[700],
                  size: 35,
                ),
              ),
              Text("$likeCount ${'Likes'.tr}"),
              const Spacer(),
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    HelperFunction.showCommentsBottomSheet(
                      id: widget.post.id ?? 0,
                      type: "daily_update_post",
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        SvgPicture.asset(AppAssets.comment),
                        FittedBox(
                          child: Text(
                            '${widget.post.commentsCount} ${'comments'.tr}',
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
