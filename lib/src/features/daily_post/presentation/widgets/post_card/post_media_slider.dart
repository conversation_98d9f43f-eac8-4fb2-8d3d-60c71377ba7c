import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../core/data/models/daily_post_media.dart';
import '../../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../../core/widgets/media/media_preview.dart';
import '../../../../../core/widgets/media/video_thumbnail_widget.dart';
import '../../../../../core/widgets/media/youtube_thumnail_widget.dart';

class PostMediaSlider extends StatefulWidget {
  const PostMediaSlider({
    super.key,
    required this.media,
    this.borderRadius = BorderRadius.zero,
  });
  final List<DailyPostMedia> media;
  final BorderRadius borderRadius;

  @override
  State<PostMediaSlider> createState() => _PostMediaSliderState();
}

class _PostMediaSliderState extends State<PostMediaSlider> {
  int currentMediaIndex = 0;
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: widget.borderRadius,
      child: Stack(
        children: [
          CarouselSlider.builder(
            itemCount: widget.media.length,
            itemBuilder: (BuildContext context, int index, int realIndex) {
              return Stack(
                children: [
                  SizedBox(
                    width: double.maxFinite,
                    child: Builder(builder: (context) {
                      if (widget.media[index].type == MediaType.video) {
                        return VideoThumbnailWidget(
                          url: widget.media[index].fileUrl ?? "",
                        );
                      } else if (widget.media[index].type ==
                          MediaType.youtube) {
                        return YoutubeThumbnailWidget(
                          youtubeId: widget.media[index].youtubeId ?? "",
                          borderRadius: BorderRadius.circular(0),
                        );
                      } else if (widget.media[index].type == MediaType.pdf) {
                        return Container(
                          color: Colors.grey[200],
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.picture_as_pdf,
                                  size: 50,
                                  color: Get.theme.primaryColor,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  "PDF Document".tr,
                                  style: TextStyle(
                                    color: Get.theme.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      return CustomCachedNetworkImage(
                        imageUrl: widget.media[index].fileUrl ?? "",
                        borderRadius: BorderRadius.circular(0),
                        fit: BoxFit.cover,
                      );
                    }),
                  ),
                  PositionedDirectional(
                    bottom: 0,
                    end: 0,
                    child: InkWell(
                      child: Container(
                        decoration: BoxDecoration(
                            color: Get.theme.primaryColor.withOpacity(0.8),
                            borderRadius: const BorderRadiusDirectional.only(
                                topStart: Radius.circular(10))),
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          widget.media[index].type == MediaType.video ||
                                  widget.media[index].type == MediaType.youtube
                              ? Icons.play_arrow_rounded
                              : widget.media[index].type == MediaType.pdf
                                  ? Icons.picture_as_pdf
                                  : Icons.zoom_out_map,
                          color: Colors.white,
                        ),
                      ),
                      onTap: () {
                        if (widget.media[index].fileUrl != null ||
                            widget.media[index].youtubeId != null) {
                          Get.to(
                            () => MediaPreview(
                              type: widget.media[index].type!,
                              url: widget.media[index].fileUrl ?? "",
                              youtubeId: widget.media[index].youtubeId,
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ],
              );
            },
            options: CarouselOptions(
              aspectRatio: 16 / 9,
              initialPage: 0,
              viewportFraction: 1,
              enableInfiniteScroll: false,
              reverse: false,
              autoPlay: widget.media.length > 1,
              autoPlayInterval: const Duration(seconds: 10),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.fastOutSlowIn,
              enlargeCenterPage: false,
              onPageChanged: (int index, CarouselPageChangedReason reason) {
                setState(() {
                  currentMediaIndex = index;
                });
              },
              scrollDirection: Axis.horizontal,
            ),
          ),
          if (widget.media.length > 1)
            PositionedDirectional(
              start: 0,
              top: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: Get.theme.primaryColor.withOpacity(0.6),
                  borderRadius: const BorderRadiusDirectional.only(
                    bottomEnd: Radius.circular(10),
                  ),
                ),
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  "${currentMediaIndex + 1}/${widget.media.length}",
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
