import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class PostActionPopupItem extends StatelessWidget {
  const PostActionPopupItem({
    super.key,
    required this.icon,
    required this.label,
    this.color,
  });
  final String icon;
  final String label;
  final Color? color;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.end,
      textBaseline: TextBaseline.ideographic,
      children: [
        SvgPicture.asset(
          icon,
          width: 18,
          colorFilter: ColorFilter.mode(
            color ?? Get.theme.primaryColor,
            BlendMode.srcATop,
          ),
        ),
        8.horizontalSpace,
        Text(
          label.tr,
          style: TextStyle(
            color: color ?? Get.theme.primaryColor,
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }
}
