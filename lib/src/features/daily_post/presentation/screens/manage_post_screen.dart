import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:form_validator/form_validator.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/daily_post.dart';
import '../../../../core/data/models/view_models/media_vm.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/upload_progress_controller.dart';
import '../../../../core/widgets/form_fields/custom_dropdown_field.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../../../core/widgets/upload_progress_overlay.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/daily_post_controller.dart';
import '../widgets/media_picker/media_picker_form_field.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/extensions.dart';
import '../../../../core/widgets/custom_text.dart';

class ManagePostScreen extends StatelessWidget {
  const ManagePostScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize the progress controller
    Get.put(UploadProgressController());

    final arguments = Get.arguments;
    final isUpdate = arguments['isUpdate'];
    var formKey = GlobalKey<FormState>();
    DailyPost post = arguments['post'] ?? DailyPost();
    return LayoutScreen(
      title: isUpdate == true ? 'Update Post'.tr : 'Add Post'.tr,
      withBackground: false,
      body: UploadProgressOverlay(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Form(
            key: formKey,
            child: Column(
              children: [
                CustomTextField(
                  title: "Text".tr,
                  maxLine: 4,
                  initialValue: post.description,
                  onSaved: (value) {
                    post.description = value;
                  },
                  onChanged: (value) {
                    post.description = value;
                  },
                  validator: ValidationBuilder(
                          requiredMessage: "Write a comment ..".tr)
                      .minLength(3)
                      .build(),
                  // validator: (value) {
                  //   if (value == null) {
                  //     return "Write a comment".tr;
                  //   }
                  //   return null;
                  // },
                ),
                MediaPickerFormField(
                  initialValue: MediaVm(
                      mediaItems: post.dailyPostMedia ?? [], deletedItems: []),
                  margin: const EdgeInsets.only(
                    top: 16,
                  ),
                  title: 'Media'.tr,
                  onChanged: (value) {
                    printLog(value?.mediaItems);
                  },
                  onSaved: (value) {
                    printLog("on saved");
                    printLog(value?.mediaItems);
                    post.dailyPostMedia = value?.mediaItems ?? [];
                    post.deletedItems = value?.deletedItems ?? [];
                  },
                ),
                CustomDropdownField(
                  title: 'Class'.tr,
                  margin: const EdgeInsets.only(
                    top: 16,
                  ),
                  initialItem: post.sectionId == null
                      ? null
                      : AuthService.instance.sectionWithSubjects.firstWhere(
                          (element) => element.id == post.sectionId),
                  items: AuthService.instance.sectionWithSubjects,
                  hintBuilder: (context, item, isSelected) {
                    return CustomText(
                      text: item,
                      fontSize: 14.sp,
                      color: AppColor.primaryColor,
                      fontWeight: FontWeight.bold,
                    );
                  },
                  headerBuilder: (context, item, isSelected) {
                    return CustomText(
                      text: item.name,
                      fontSize: 14.sp,
                      color: AppColor.primaryColor,
                      fontWeight: FontWeight.bold,
                    );
                  },
                  listItemBuilder: (context, item, isSelected, onItemSelect) {
                    return CustomText(
                      text: item.name,
                      fontSize: 14.sp,
                      color: AppColor.primaryColor,
                      fontWeight: FontWeight.bold,
                    );
                  },
                  validator: (value) {
                    if (value == null) {
                      return "Please select a class".tr;
                    }
                    return null;
                  },
                  onChanged: (value) {
                    post.sectionId = value?.id;
                  },
                ),
                SubmitButton(
                  margin: const EdgeInsets.only(
                    top: 32,
                  ),
                  text: isUpdate == true ? "Update".tr : "Add".tr,
                  onSubmit: () async {
                    if (isUpdate) {
                      await DailyPostController.instance.updatePost(post);
                    } else {
                      await DailyPostController.instance.addPost(post);
                    }
                  },
                  formKey: formKey,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
