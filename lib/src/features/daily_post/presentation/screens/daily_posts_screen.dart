import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/daily_post_controller.dart';
import '../widgets/post_card/post_card.dart';

class DailyPosts extends StatelessWidget {
  const DailyPosts({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(DailyPostController());
    return LayoutScreen(
      title: 'Posts'.tr,
      controller: DailyPostController.instance,
      floatingActionButton:
          AuthService.instance.user.value?.role != Roles.Teacher
              ? null
              : FloatingActionButton(
                  child: const Icon(Icons.add),
                  onPressed: () {
                    Get.toNamed(AppRouter.managePostScreen, arguments: {
                      'isUpdate': false,
                    });
                  },
                ),
      bodyHeader: SectionWithSubjectsWidget(
        showSubjects: false,
        showSections: AuthService.instance.user.value?.role == Roles.Teacher ||
            AuthService.instance.user.value?.role == Roles.Guardian ||
            AuthService.instance.user.value?.role == Roles.Supervisor,
        onSectionChanged: (sectionId) {
          DailyPostController.instance.getPosts(sectionId: sectionId);
        },
      ),
      emptyWidegt: EmptyWidget(
        icon: AppAssets.dailyCoverImage,
        iconColor: Get.theme.primaryColor,
      ),
      body: Obx(
        () => ListView.separated(
          padding: const EdgeInsets.symmetric(vertical: 16),
          itemCount: DailyPostController.instance.posts.length + 1,
          itemBuilder: (context, index) {
            if (index == DailyPostController.instance.posts.length) {
              if (DailyPostController.instance.canLoadMore()) {
                DailyPostController.instance.loadMore();
                return SizedBox(
                  height: 100,
                  width: double.maxFinite,
                  child: Center(
                    child: Text('loading more ...'.tr),
                  ),
                );
              } else {
                return const SizedBox.shrink();
              }
            }
            return PostCard(
              post: DailyPostController.instance.posts[index],
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return const SizedBox(
              height: 16,
            );
          },
        ),
      ),
    );
  }
}
