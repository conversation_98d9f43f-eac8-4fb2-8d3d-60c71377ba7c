import 'package:get/get.dart';
import '../controller/week_plan_controller.dart';
import '../../../features/week_plan_file/controller/week_plan_file_controller.dart';

class WeekPlanBinding extends Bindings {
  @override
  void dependencies() {
    // First register the WeekPlanFileController if it's not already registered
    if (!Get.isRegistered<WeekPlanFileController>()) {
      Get.put<WeekPlanFileController>(WeekPlanFileController(), permanent: true);
    }
    
    // Then register the WeekPlanController
    Get.put<WeekPlanController>(WeekPlanController(), permanent: true);
  }
}
