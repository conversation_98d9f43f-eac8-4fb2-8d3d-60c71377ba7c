import 'dart:developer' as developer;
import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/responses/grouped_week_plan.dart';
import '../../../core/data/models/week_plan_lesson.dart';
import '../../../core/utils/app_consts.dart';
import '../../../core/utils/helper_function.dart';
import '../../../core/data/models/section_with_subjects.dart';
import '../../auth/services/auth_service.dart';

class WeekPlanController extends BaseController {
  static WeekPlanController get instance => Get.find<WeekPlanController>();

  RxList<GroupedWeekPlan> studentLessons = RxList<GroupedWeekPlan>([]);
  RxList<WeekPlanLesson> lessons = RxList<WeekPlanLesson>([]);
  Rx<SectionWithSubjects?> selectedSection = Rx<SectionWithSubjects?>(null);

  // Flag to determine if the student is in kindergarten stage
  RxBool isKindergartenStage = false.obs;

  @override
  void onInit() {
    super.onInit();

    // Set initial page status to loaded to avoid showing loading indicator
    setPageStatus(PageStatus.loaded);

    // Check if the user is in kindergarten stage
    checkStudentStage();
  }

  @override
  void onReady() {
    lessons.listen((value) {
      if (value.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else {
        setPageStatus(PageStatus.loaded);
      }
    });
    super.onReady();
  }

  // Check if the user is in kindergarten stage
  void checkStudentStage() {
    final user = AuthService.instance.user.value;

    if (user?.role == Roles.Student) {
      // First check if the user has the isKindergarten property set
      if (user?.isKindergarten == true) {
        isKindergartenStage.value = true;
        return;
      }

      // Otherwise, determine based on section name
      callApi(() async {
        try {
          final sections = await apiProvider.getSectionsWithSubjects();

          // Check if the student has a section assigned
          if (user?.sectionId == null) {
            isKindergartenStage.value = false;
            getWeekPlan();
            return;
          }

          final studentSection = sections.firstWhereOrNull(
            (section) => section.id == user?.sectionId,
          );

          if (studentSection != null) {
            // Check if the section name contains "KG" or "Kindergarten"
            final sectionName = studentSection.name.toLowerCase();
            isKindergartenStage.value = sectionName.contains('kg') ||
                sectionName.contains('kindergarten') ||
                sectionName.contains('روضة') ||
                sectionName.contains('رياض');

            // If not in kindergarten, fetch regular week plans
            if (!isKindergartenStage.value) {
              getWeekPlan();
            }
          } else {
            isKindergartenStage.value = false;
            getWeekPlan();
          }
        } catch (e) {
          isKindergartenStage.value = false;
          getWeekPlan();
        }
      });
    } else if (user?.role == Roles.Teacher) {
      // For teachers, check if they teach kindergarten classes
      developer.log(
          'Checking if teacher teaches kindergarten classes. isKindergarten: ${user?.isKindergarten}',
          name: 'WeekPlanController');

      if (user?.isKindergarten == true) {
        // Teacher has kindergarten classes, show week plan files
        developer.log(
            'Teacher has kindergarten classes, showing week plan files',
            name: 'WeekPlanController');
        isKindergartenStage.value = true;
      } else {
        // Teacher doesn't have kindergarten classes, show regular week plans
        developer.log(
            'Teacher does not have kindergarten classes, showing regular week plans',
            name: 'WeekPlanController');
        isKindergartenStage.value = false;
        getWeekPlan();
      }
    }
  }

  void getWeekPlan({int? sectionId, int? subjectId}) {
    callApi(() async {
      var role = AuthService.instance.user.value?.role;
      if (role == Roles.Student || role == Roles.Guardian) {
        var response = await apiProvider.getWeekPlansForStudent(
          sectionId,
          subjectId,
        );
        studentLessons.value = response;
        studentLessons.refresh();
        if (studentLessons.isEmpty) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      } else {
        var response = await apiProvider.getWeekPlans(sectionId, subjectId);
        lessons.value = response;
      }
    });
  }

  Future<void> addLesson(WeekPlanLesson lesson) async {
    await callApi(() async {
      // Ensure the lesson has the required v2 fields
      if (lesson.sections == null && lesson.sectionId != null) {
        lesson.sections = [WeekPlanSection(id: lesson.sectionId)];
      }

      if (lesson.lessonDates == null && lesson.date != null) {
        lesson.lessonDates = [
          WeekPlanLessonDate(date: lesson.date, hasQuiz: false),
        ];
      }

      var response = await apiProvider.createWeekPlan(lesson);
      Get.back();
      HelperFunction.showSuccessMessage(response.message ?? "");

      lessons.add(response.weekPlanLesson!);
    }, withLoading: false);
  }

  Future<void> updateLesson(WeekPlanLesson lesson) async {
    await callApi(() async {
      // Ensure the lesson has the required v2 fields
      if (lesson.sections == null && lesson.sectionId != null) {
        lesson.sections = [WeekPlanSection(id: lesson.sectionId)];
      }

      if (lesson.lessonDates == null && lesson.date != null) {
        lesson.lessonDates = [
          WeekPlanLessonDate(date: lesson.date, hasQuiz: false),
        ];
      }

      var response = await apiProvider.updateWeekPlan(lesson.id!, lesson);
      Get.back();
      lessons[lessons.indexWhere((element) => element.id == lesson.id)] =
          response.weekPlanLesson!;
      HelperFunction.showSuccessMessage(response.message ?? "");
    }, withLoading: false);
  }

  Future<void> deleteLesson(int id) async {
    await callApi(() async {
      var response = await apiProvider.deleteWeekPlan(id);
      HelperFunction.showSuccessMessage(response.message ?? "");
      lessons.removeWhere((element) => element.id == id);
    }, withLoading: false);
  }
}
