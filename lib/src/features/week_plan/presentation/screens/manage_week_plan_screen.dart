import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/week_plan_lesson.dart';
import '../../../../core/screens/layout_screen.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/widgets/buttons/submit_button.dart';
import '../../../../core/widgets/form_fields/custom_dropdown_field.dart';
import '../../../../core/widgets/form_fields/custom_text_field.dart';
import '../../../../core/widgets/form_fields/date_time_form_field.dart';
import '../../../auth/services/auth_service.dart';
import '../../../../core/data/models/section_with_subjects.dart';
import '../../controller/week_plan_controller.dart';

class ManageWeekPlanScreen extends StatefulWidget {
  const ManageWeekPlanScreen({super.key});

  @override
  State<ManageWeekPlanScreen> createState() => _ManageWeekPlanScreenState();
}

class _ManageWeekPlanScreenState extends State<ManageWeekPlanScreen> {
  late WeekPlanLesson lesson;
  late bool isUpdated;
  final formKey = GlobalKey<FormState>();

  // For multiple sections
  final List<SectionWithSubjects> selectedSections = [];

  // For multiple lesson dates
  final List<LessonDateEntry> lessonDates = [];

  @override
  void initState() {
    super.initState();
    final arguments = Get.arguments;
    lesson = arguments['data'] ?? WeekPlanLesson();
    isUpdated = arguments['isUpdate'];

    // Initialize selected sections
    if (lesson.sections != null && lesson.sections!.isNotEmpty) {
      for (var section in lesson.sections!) {
        try {
          var sectionWithSubjects = AuthService.instance.sectionWithSubjects
              .firstWhere((element) => element.id == section.id);
          selectedSections.add(sectionWithSubjects);
        } catch (e) {
          // Section not found
        }
      }
    } else if (lesson.sectionId != null) {
      try {
        var sectionWithSubjects = AuthService.instance.sectionWithSubjects
            .firstWhere((element) => element.id == lesson.sectionId);
        selectedSections.add(sectionWithSubjects);

        // Set the selected section for subject dropdown
        WeekPlanController.instance.selectedSection.value = sectionWithSubjects;
      } catch (e) {
        // Section not found
      }
    }

    // Initialize lesson dates
    if (lesson.lessonDates != null && lesson.lessonDates!.isNotEmpty) {
      for (var date in lesson.lessonDates!) {
        lessonDates.add(LessonDateEntry(
          date: date.date,
          hasQuiz: date.hasQuiz ?? false,
        ));
      }
    } else if (lesson.date != null) {
      lessonDates.add(LessonDateEntry(
        date: lesson.date,
        hasQuiz: false,
      ));
    }

    // Add an empty date entry if no dates are available
    if (lessonDates.isEmpty) {
      lessonDates.add(LessonDateEntry(
        date: null,
        hasQuiz: false,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutScreen(
      title: isUpdated == false ? 'Add lesson'.tr : 'Update lesson'.tr,
      withBackground: false,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Multiple Sections Selection
              Text(
                'Classes'.tr,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ...selectedSections.map((section) => Chip(
                        label: Text(section.name),
                        deleteIcon: const Icon(Icons.close, size: 18),
                        onDeleted: () {
                          setState(() {
                            selectedSections.remove(section);
                          });
                        },
                      )),
                  ActionChip(
                    label: Text('Add Class'.tr),
                    avatar: const Icon(Icons.add, size: 18),
                    onPressed: () {
                      _showSectionSelectionDialog();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Subject Selection
              Obx(
                () => CustomDropdownField<Subject>(
                  key: Key(Random.secure().toString()),
                  title: 'Subject'.tr,
                  initialItem: lesson.subjectId == null
                      ? null
                      : WeekPlanController
                          .instance.selectedSection.value?.subjects
                          .firstWhere(
                              (element) => element.id == lesson.subjectId,
                              orElse: () => Subject(id: 0, name: '')),
                  items: WeekPlanController
                          .instance.selectedSection.value?.subjects ??
                      [],
                  headerBuilder: (context, item, isSelected) {
                    return Text(item.name);
                  },
                  listItemBuilder: (context, item, isSelected, onItemSelect) {
                    return Text(item.name);
                  },
                  validator: (value) {
                    if (value == null) {
                      return "Please select a subject".tr;
                    }
                    return null;
                  },
                  onChanged: (value) {
                    lesson.subjectId = value?.id;
                  },
                ),
              ),

              // Lesson Title
              CustomTextField(
                title: 'Lesson name'.tr,
                filled: true,
                filledColor: AppColor.whiteColor,
                hint: 'Enter lesson name'.tr,
                initialValue: lesson.title,
                onSaved: (value) {
                  lesson.title = value;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "The field is required".tr;
                  }
                  return null;
                },
              ),

              // Lesson Details
              CustomTextField(
                title: 'Lesson details'.tr,
                filled: true,
                filledColor: AppColor.whiteColor,
                hint: 'Enter lesson details'.tr,
                maxLine: 6,
                initialValue: lesson.detail,
                onSaved: (value) {
                  lesson.detail = value;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "The field is required".tr;
                  }
                  return null;
                },
              ),

              // Multiple Lesson Dates
              Padding(
                padding: const EdgeInsets.only(top: 16, bottom: 8),
                child: Text(
                  'Lesson dates'.tr,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),

              ...lessonDates.asMap().entries.map((entry) {
                int index = entry.key;
                LessonDateEntry dateEntry = entry.value;
                return _buildLessonDateEntry(index, dateEntry);
              }),

              // Add Date Button
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: TextButton.icon(
                  onPressed: () {
                    setState(() {
                      lessonDates.add(LessonDateEntry(
                        date: null,
                        hasQuiz: false,
                      ));
                    });
                  },
                  icon: const Icon(Icons.add),
                  label: Text('Add another date'.tr),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColor.primaryColor,
                  ),
                ),
              ),

              // Submit Button
              SubmitButton(
                text: isUpdated == false ? 'Add'.tr : 'Update'.tr,
                onSubmit: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();

                    // Validate at least one section is selected
                    if (selectedSections.isEmpty) {
                      Get.snackbar(
                        'Error'.tr,
                        'Please select at least one class'.tr,
                        snackPosition: SnackPosition.BOTTOM,
                      );
                      return;
                    }

                    // Validate at least one date is valid
                    bool hasValidDate = false;
                    for (var dateEntry in lessonDates) {
                      if (dateEntry.date != null) {
                        hasValidDate = true;
                        break;
                      }
                    }

                    if (!hasValidDate) {
                      Get.snackbar(
                        'Error'.tr,
                        'Please add at least one valid lesson date'.tr,
                        snackPosition: SnackPosition.BOTTOM,
                      );
                      return;
                    }

                    // Set sections
                    lesson.sections = selectedSections
                        .map((section) => WeekPlanSection(
                              id: section.id,
                              name: section.name,
                            ))
                        .toList();

                    // Set lesson dates
                    lesson.lessonDates = lessonDates
                        .where((dateEntry) => dateEntry.date != null)
                        .map((dateEntry) => WeekPlanLessonDate(
                              date: dateEntry.date,
                              hasQuiz: dateEntry.hasQuiz,
                            ))
                        .toList();

                    // For backward compatibility
                    if (lesson.sections!.isNotEmpty) {
                      lesson.sectionId = lesson.sections![0].id;
                    }

                    if (isUpdated) {
                      await WeekPlanController.instance.updateLesson(lesson);
                    } else {
                      await WeekPlanController.instance.addLesson(lesson);
                    }
                  }
                },
                formKey: formKey,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLessonDateEntry(int index, LessonDateEntry dateEntry) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${"Lesson date".tr} ${index + 1}'.tr,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (lessonDates.length > 1)
                  IconButton(
                    icon: Icon(Icons.delete, color: AppColor.redColor),
                    onPressed: () {
                      setState(() {
                        lessonDates.removeAt(index);
                      });
                    },
                  ),
              ],
            ),
            const SizedBox(height: 8),
            CustomDateTimeFormField(
              title: '',
              suffixIcon: SvgPicture.asset(
                AppAssets.calender,
              ),
              initialValue: dateEntry.date,
              onSaved: (value) {
                dateEntry.date = value;
              },
              validator: (value) {
                // Allow null dates as long as at least one date is valid
                return null;
              },
              onChanged: (DateTime? value) {
                dateEntry.date = value;
              },
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Checkbox(
                  value: dateEntry.hasQuiz,
                  onChanged: (value) {
                    setState(() {
                      dateEntry.hasQuiz = value ?? false;
                    });
                  },
                  activeColor: AppColor.primaryColor,
                ),
                Text('Has quiz'.tr),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showSectionSelectionDialog() {
    // Get available sections (not already selected)
    final availableSections = AuthService.instance.sectionWithSubjects
        .where((section) => !selectedSections.contains(section))
        .toList();

    if (availableSections.isEmpty) {
      Get.snackbar(
        'Info'.tr,
        'All classes have been selected'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    Get.dialog(
      AlertDialog(
        title: Text('Select Class'.tr),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: availableSections.length,
            itemBuilder: (context, index) {
              final section = availableSections[index];
              return ListTile(
                title: Text(section.name),
                onTap: () {
                  setState(() {
                    selectedSections.add(section);

                    // Set the selected section for subject dropdown if it's the first one
                    if (selectedSections.length == 1) {
                      WeekPlanController.instance.selectedSection.value =
                          section;
                    }
                  });
                  Get.back();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Cancel'.tr),
          ),
        ],
      ),
    );
  }
}

// Helper class for managing lesson dates
class LessonDateEntry {
  DateTime? date;
  bool hasQuiz;

  LessonDateEntry({
    required this.date,
    required this.hasQuiz,
  });
}
