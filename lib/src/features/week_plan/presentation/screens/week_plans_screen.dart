import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/data/enums/page_status.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../../auth/services/auth_service.dart';
import '../../../week_plan_file/presentation/widgets/week_plan_file_viewer_widget.dart';
import '../../../week_plan_file/presentation/widgets/week_plan_file_list_widget.dart';
import '../../../week_plan_file/controller/week_plan_file_controller.dart';
import '../../controller/week_plan_controller.dart';
import '../widgets/week_plan_list_title.dart';
import '../widgets/week_plan_tab_bar.dart';

class WeekPlansScreen extends StatelessWidget {
  const WeekPlansScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize both controllers
    var weekPlanController = WeekPlanController.instance;

    // Initialize WeekPlanFileController if needed
    WeekPlanFileController weekPlanFileController;
    try {
      weekPlanFileController = Get.find<WeekPlanFileController>();
      developer.log('Found existing WeekPlanFileController instance',
          name: 'WeekPlansScreen');
    } catch (e) {
      weekPlanFileController =
          Get.put(WeekPlanFileController(), permanent: true);
      developer.log('Created new WeekPlanFileController instance',
          name: 'WeekPlansScreen');
    }

    var userRole = AuthService.instance.user.value?.role;
    var isKindergarten =
        AuthService.instance.user.value?.isKindergarten ?? false;

    developer.log(
        'WeekPlansScreen build - userRole: $userRole, isKindergarten: $isKindergarten',
        name: 'WeekPlansScreen');
    return LayoutScreen(
      floatingActionButton: Obx(() {
        // Don't show FAB for students
        if (userRole == Roles.Student) {
          return SizedBox.shrink(); // Return an empty widget instead of null
        }

        // For teachers with kindergarten role, show FAB to add week plan files
        if (userRole == Roles.Teacher &&
            weekPlanController.isKindergartenStage.value) {
          return FloatingActionButton(
            backgroundColor: AppColor.primaryColor,
            onPressed: () {
              // Navigate to the week plan file screen
              Get.toNamed(AppRouter.weekPlanFileScreen);
            },
            child: Icon(Icons.add, color: AppColor.whiteColor),
          );
        }

        // For other teachers, show FAB to add regular week plans
        return FloatingActionButton(
          backgroundColor: AppColor.primaryColor,
          onPressed: () {
            Get.toNamed(
              AppRouter.manageWeekPlanScreen,
              arguments: {'isUpdate': false},
            );
          },
          child: Icon(Icons.add, color: AppColor.whiteColor),
        );
      }),
      title: 'Week plane'.tr,
      controller: weekPlanController,
      bodyHeader: Obx(() {
        // For kindergarten teachers, show section selector but not subjects
        if (userRole == Roles.Teacher &&
            weekPlanController.isKindergartenStage.value) {
          return SectionWithSubjectsWidget(
            showSections: true,
            showSubjects:
                false, // Don't show subjects for kindergarten teachers
            onSubjectChanged: (sectionId, subjectId) {
              // When section changes, fetch week plan files for that section
              weekPlanFileController.fetchWeekPlanFiles(sectionId: sectionId);
            },
          );
        }

        // For other teachers, show both section and subject selectors
        return SectionWithSubjectsWidget(
          showSections: userRole == Roles.Teacher,
          showSubjects: userRole == Roles.Teacher,
          onSubjectChanged: (sectionId, subjectId) {
            weekPlanController.getWeekPlan(
                sectionId: sectionId, subjectId: subjectId);
          },
        );
      }),
      body: Obx(() {
        var role = AuthService.instance.user.value?.role;
        var isKindergarten = weekPlanController.isKindergartenStage.value;

        developer.log(
            'WeekPlansScreen body - role: $role, isKindergarten: $isKindergarten, files count: ${weekPlanFileController.weekPlanFiles.length}',
            name: 'WeekPlansScreen');

        // If user is in kindergarten stage, show appropriate week plan file widget
        if (isKindergarten) {
          // For teachers, show the list of files
          if (role == Roles.Teacher) {
            developer.log(
                'Showing WeekPlanFileListWidget for kindergarten teacher',
                name: 'WeekPlansScreen');

            // Force refresh of week plan files if empty and not already loading
            if (weekPlanFileController.weekPlanFiles.isEmpty &&
                weekPlanFileController.pageStatus.value != PageStatus.loading) {
              developer.log('Files list is empty, forcing refresh',
                  name: 'WeekPlansScreen');
              // Use Future.delayed to avoid build-time side effects
              Future.delayed(Duration.zero, () {
                weekPlanFileController.fetchWeekPlanFiles();
              });
            }

            // Use a Column with Expanded to ensure the list takes up all available space
            return Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                children: [
                  // Title
                  Padding(
                    padding: EdgeInsets.only(bottom: 16.r),
                    child: Text(
                      'Week Plan Files'.tr,
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColor.primaryColor,
                      ),
                    ),
                  ),
                  // List of files
                  Expanded(
                    child: WeekPlanFileListWidget(
                      controller: weekPlanFileController,
                    ),
                  ),
                ],
              ),
            );
          }
          // For students, show the single file viewer
          else {
            developer.log(
                'Showing WeekPlanFileViewerWidget for kindergarten student',
                name: 'WeekPlansScreen');
            return SingleChildScrollView(
              padding: EdgeInsets.all(16.r),
              child: WeekPlanFileViewerWidget(
                controller: weekPlanFileController,
              ),
            );
          }
        }

        // For students and guardians who are not in kindergarten, show student week plans
        if (role == Roles.Student || role == Roles.Guardian) {
          var studentLessons = weekPlanController.studentLessons;
          return DefaultTabController(
            length: studentLessons.length,
            child: Column(
              children: [
                WeekPlanTabBar(studentLessons: studentLessons),
                Expanded(
                  child: TabBarView(
                    children: List.generate(studentLessons.length, (index) {
                      return ListView.separated(
                        padding: const EdgeInsets.all(16),
                        itemCount: studentLessons[index].lesson.length,
                        itemBuilder: (context, lessonIndex) {
                          return WeekPlanListTile(
                            lesson: studentLessons[index].lesson[lessonIndex],
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) {
                          return 16.verticalSpace;
                        },
                      );
                    }),
                  ),
                ),
              ],
            ),
          );
        }

        // For teachers who are not in kindergarten, show the regular week plan list
        var lessons = weekPlanController.lessons;
        return ListView.separated(
          padding: const EdgeInsets.all(16),
          itemCount: lessons.length,
          itemBuilder: (context, index) {
            return WeekPlanListTile(lesson: lessons[index]);
          },
          separatorBuilder: (BuildContext context, int index) {
            return 16.verticalSpace;
          },
        );
      }),
    );
  }
}
