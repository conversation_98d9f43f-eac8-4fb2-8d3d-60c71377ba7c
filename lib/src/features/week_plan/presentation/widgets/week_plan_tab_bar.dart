import 'package:flutter/material.dart';
import '../../../../core/data/models/responses/grouped_week_plan.dart';
import '../../../class_schedule/presentation/widgets/day_tab.dart';

class WeekPlanTabBar extends StatefulWidget {
  const WeekPlanTabBar({
    super.key,
    required this.studentLessons,
  });

  final List<GroupedWeekPlan> studentLessons;

  @override
  State<WeekPlanTabBar> createState() => _WeekPlanTabBarState();
}

class _WeekPlanTabBarState extends State<WeekPlanTabBar> {
  int selectedIndex = 0;
  @override
  Widget build(BuildContext context) {
    return TabBar(
      onTap: (value) {
        setState(() {
          selectedIndex = value;
        });
      },
      tabs: List.generate(
        widget.studentLessons.length,
        (index) {
          return DayTab(
            selected: index == selectedIndex,
            text: widget.studentLessons[index].date != null
                ? "${widget.studentLessons[index].day}\n${widget.studentLessons[index].date}"
                : widget.studentLessons[index].day,
          );
        },
      ),
      labelPadding: const EdgeInsets.symmetric(
        horizontal: 4,
      ),
      padding: const EdgeInsets.only(
        top: 16,
        right: 8,
        left: 8,
        bottom: 8,
      ),
      dividerHeight: 0,
      indicatorWeight: 0,
      indicator: const BoxDecoration(),
      isScrollable: true,
      tabAlignment: TabAlignment.center,
    );
  }
}
