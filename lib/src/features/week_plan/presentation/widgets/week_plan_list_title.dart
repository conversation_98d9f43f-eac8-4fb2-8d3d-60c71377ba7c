import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/week_plan_lesson.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/utils/helper_function.dart';
import '../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/week_plan_controller.dart';

class WeekPlanListTile extends StatelessWidget {
  const WeekPlanListTile({super.key, required this.lesson});
  final WeekPlanLesson lesson;
  @override
  Widget build(BuildContext context) {
    var userRole = AuthService.instance.user.value?.role;

    return CustomListTile(
      title: lesson.title ?? "",
      subtitle: AuthService.instance.user.value?.role == Roles.Teacher
          ? null
          : (lesson.subjectName ?? ""),
      subtitleIcon: AuthService.instance.user.value?.role == Roles.Teacher
          ? AppAssets.clock
          : null,
      leadingIcon: AppAssets.one,
      detail: Column(
        children: [
          const SizedBox(
            height: 16,
          ),

          // Display multiple sections if available
          if (lesson.sections != null && lesson.sections!.length > 1)
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Wrap(
                spacing: 4,
                runSpacing: 4,
                children: lesson.sections!
                    .map((section) => Chip(
                          label: Text(
                            section.name ?? "",
                            style: const TextStyle(fontSize: 12),
                          ),
                          padding: EdgeInsets.zero,
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          visualDensity: VisualDensity.compact,
                        ))
                    .toList(),
              ),
            ),

          // Display multiple lesson dates if available
          if (lesson.lessonDates != null && lesson.lessonDates!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: lesson.lessonDates!.map((dateEntry) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: Get.theme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          dateEntry.formattedDate ?? "",
                          style: const TextStyle(fontSize: 12),
                        ),
                        if (dateEntry.hasQuiz == true) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColor.primaryColor.withAlpha(25),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'Quiz'.tr,
                              style: TextStyle(
                                fontSize: 10,
                                color: AppColor.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                }).toList(),
              ),
            )
          else if (lesson.formattedDate != null &&
              lesson.formattedDate!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 14,
                    color: Get.theme.primaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    lesson.formattedDate ?? "",
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),

          SizedBox(
            width: double.maxFinite,
            child: Text(
              lesson.detail ?? "",
              softWrap: true,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(
            height: 8,
          ),
          Align(
            alignment: const AlignmentDirectional(1.12, 0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Material(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: () {
                      HelperFunction.showCommentsBottomSheet(
                          id: lesson.id ?? 0, type: 'week_plan');
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'Comments'.tr,
                        style: TextStyle(
                          fontSize: 13,
                          color: Get.theme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
                Material(
                  borderRadius: BorderRadius.circular(8),
                  color: Get.theme.primaryColor,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: showDetailBottomSheet,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'More'.tr,
                        style: const TextStyle(
                          fontSize: 13,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      onTap: showDetailBottomSheet,
      endAction: userRole == Roles.Student
          ? null
          : [
              SlidableAction(
                onPressed: (context) {
                  Get.toNamed(AppRouter.manageWeekPlanScreen, arguments: {
                    'isUpdate': true,
                    'data': lesson.clone(),
                  });
                },
                icon: Icons.edit,
                backgroundColor: AppColor.primaryColor,
                foregroundColor: AppColor.whiteColor,
                label: 'Edit'.tr,
              ),
              SlidableAction(
                onPressed: (context) {
                  WeekPlanController.instance.deleteLesson(lesson.id ?? 0);
                },
                icon: Icons.delete,
                backgroundColor: AppColor.redColor,
                foregroundColor: AppColor.whiteColor,
                label: 'Delete'.tr,
              ),
            ],
    );
  }

  void showDetailBottomSheet() {
    Get.bottomSheet(
      CustomBottomSheet(
        title: lesson.title,
        body: Container(
          padding: const EdgeInsets.all(16.0),
          constraints: BoxConstraints(
            maxHeight: Get.height * 0.8,
            minHeight: Get.height * 0.5,
          ),
          child: Text(
            lesson.detail ?? "",
            style: const TextStyle(
              height: 2,
            ),
          ),
        ),
      ),
      isScrollControlled: true,
    );
  }
}
