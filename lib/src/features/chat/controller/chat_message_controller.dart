import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/data/enums/message_type.dart';
import '../../../core/data/models/chat_message.dart';
import '../../../core/data/models/view_models/message_vm.dart';
import '../../../core/mixin/paginator_mixin.dart';
import 'chat_controller.dart';

import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';

class ChatMessageController extends BaseController with PaginatorMixin {
  static ChatMessageController get instance => Get.find();

  @override
  void onInit() {
    pageStatus.value = PageStatus.loaded;
    super.onInit();
  }

  RxList<ChatMessage> chatMessages = RxList<ChatMessage>([]).obs();

  TextEditingController textController = TextEditingController();

  // RxList<ChatMessage> messages = RxList.empty(growable: true);
  RxBool getConversationsIsLoading = false.obs;

  Future<void> getConversationMessages(int id, {required int page}) async {
    callApi(() async {
      if (page == 1) {
        setPageStatus(PageStatus.loading);
      }
      currentPage.value = page;
      var response = await apiProvider.getConversationMessages(id, page: page);

      if (page == 1) {
        chatMessages.value = response.chats;
        chatMessages.refresh();
        lastPage.value = response.lastPage;
        totalItems.value = response.itemsCount;

        //to reset  unread count
        var index = ChatController.instance.chatList
            .indexWhere((chat) => chat.id == id);
        if (index != -1) {
          ChatController.instance.chatList[index] =
              ChatController.instance.chatList[index]..unreadCount = 0;
          ChatController.instance.chatList.refresh();
        }

        if (totalItems.value == 0) {
          setPageStatus(PageStatus.empty);
        } else {
          setPageStatus(PageStatus.loaded);
        }
      } else {
        chatMessages.addAll(response.chats);
        chatMessages.refresh();
      }
    }, withLoading: false);
  }

  Future<void> sendMessage(
      int conversationId, String message, MessageType messageType) async {
    textController.clear();
    final dataState = await apiProvider.sendMessage(
      conversationId,
      MessageVm(
        message: message,
        type: messageType,
      ),
    );
    scrollToBottom();
    textController.clear();

    chatMessages.insert(0, dataState);
    chatMessages.refresh();
    setPageStatus(PageStatus.loaded);

    //to update  last message
    var index = ChatController.instance.chatList
        .indexWhere((chat) => chat.id == conversationId);
    if (index != -1) {
      ChatController.instance.chatList[index] =
          ChatController.instance.chatList[index]..lastMessage = dataState;
      ChatController.instance.chatList.refresh();
    }
    ChatController.instance.chatList.refresh(); // Refresh the chat list
    chatMessages.refresh();

    if (chatMessages.isEmpty) {
      setPageStatus(PageStatus.empty);
    } else {
      setPageStatus(PageStatus.loaded);
    }
  }

  @override
  void getMore({int? id}) {
    getConversationMessages(
      id ?? 0,
      page: currentPage.value + 1,
    );
  }
}
