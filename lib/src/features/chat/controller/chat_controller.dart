import 'dart:async';

import 'package:get/get.dart';
import '../../../core/data/models/chat.dart';
import '../../../core/data/models/teachers.dart';
import '../../../core/data/models/user.dart';
import '../../auth/services/auth_service.dart';

import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/utils/app_consts.dart';

class ChatController extends BaseController {
  static ChatController get instance => Get.find();

  List<String> get chatListTypes {
    switch (AuthService.instance.user.value?.role) {
      case Roles.Teacher:
        return ['guardian', 'student'];
      case Roles.Supervisor:
        return ['guardian'];
      case Roles.Guardian:
        return ['teacher', 'supervisor'];
      case Roles.Student:
        return ['teacher'];
      case Roles.AdminAssistant:
        return ['guardian', 'teacher'];
      default:
        return [];
    }
  }

  @override
  void onInit() {
    pageStatus.value = PageStatus.loaded;
    super.onInit();
    getchatList(chatListTypes.isNotEmpty ? chatListTypes[0] : '');
  }

  Rx<String> type = Rx<String>("");
  RxList<Chat> chatList = RxList<Chat>([]).obs();
  Rx<List<Teacher>?> teachers = Rx<List<Teacher>?>(null);
  Rx<List<User>?> supervisors = Rx<List<User>?>(null);
  // RxList<ChatMessage> messages = RxList.empty(growable: true);
  RxBool getConversationsIsLoading = false.obs;

  Future<void> getchatList([String? type]) async {
    type ??= this.type.value;
    this.type.value = type;
    callApi(() async {
      pageStatus.value = PageStatus.loading;
      var response = await apiProvider.getChatList(role: type);
      chatList.value = response;
      updateEmptyStatus(chatList);
      chatList.refresh();
    });
  }

  Future<void> getTeachers(int sectionId) async {
    callApi(
      () async {
        teachers.value = null;
        var response = await apiProvider.getTeachers(sectionId: sectionId);
        teachers.value = response;
      },
      withLoading: false,
    );
  }

  Future<void> getSupervisors() async {
    callApi(
      () async {
        supervisors.value = null;
        var response = await apiProvider.getSupervisors();
        supervisors.value = response;
      },
      withLoading: false,
    );
  }
}
