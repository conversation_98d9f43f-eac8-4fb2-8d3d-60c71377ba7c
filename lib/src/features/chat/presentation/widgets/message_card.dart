// import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/data/models/chat_message.dart';
import '../../../../core/utils/app_color.dart';
// import 'package:rawd_aljenan/src/core/utils/app_consts.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/since_text.dart';

class MessageCard extends StatelessWidget {
  const MessageCard({super.key, required this.message});
  final ChatMessage message;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          message.isSender ? MainAxisAlignment.start : MainAxisAlignment.end,
      children: [
        Flexible(
          child: Container(
              padding: EdgeInsets.all(5),
              decoration: BoxDecoration(
                  color: (message.isSender
                      ? AppColor.primaryColor
                      : AppColor.whiteColor),
                  gradient: message.isSender
                      ? LinearGradient(
                          colors: AppColor.blueGradient,
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter)
                      : null,
                  borderRadius: BorderRadiusDirectional.only(
                    topStart: Radius.circular(10.r),
                    topEnd: Radius.circular(10.r),
                    bottomEnd:
                        !message.isSender ? Radius.zero : Radius.circular(10.r),
                    bottomStart:
                        message.isSender ? Radius.zero : Radius.circular(10.r),
                  )),
              child: Padding(
                padding: EdgeInsets.all(6),
                child:
                    // message.message == null
                    //     ?
                    //      CachedNetworkImage(
                    //         height: 200,
                    //         width: 200,
                    //         imageUrl: imageBaseUrl + message.image.toString(),
                    //         placeholder: (context, url) => const Padding(
                    //           padding: EdgeInsets.all(70),
                    //           child: CircularProgressIndicator(),
                    //         ),
                    //         errorWidget: (context, url, s) => const Padding(
                    //             padding: EdgeInsets.all(70),
                    //             child: CircularProgressIndicator()),
                    //       )
                    //     :
                    Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: message.message!,
                      color: message.isSender ? Colors.white : Colors.black87,
                      maxLine: null,
                      textAlign: TextAlign.start,
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    SinceText(
                      message.createdAt,
                      style: TextStyle(
                        color: message.isSender ? Colors.white : Colors.black87,
                        fontSize: 10,
                      ),
                    ),
                    // CustomText(
                    //   text: message.formattedCreatedAt!,
                    //   color: message.isSender ? Colors.white : Colors.black87,
                    //   fontSize: 10,
                    // ),
                  ],
                ),
              )),
        ),
      ],
    );
  }
}
