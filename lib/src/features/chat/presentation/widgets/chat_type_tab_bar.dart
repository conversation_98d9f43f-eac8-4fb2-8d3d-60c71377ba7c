import 'package:flutter/material.dart';
import '../../../../core/utils/app_consts.dart';
import '../../controller/chat_controller.dart';

class ChatTypeTabBar extends StatelessWidget {
  const ChatTypeTabBar({
    super.key,
    required this.onTap,
  });
  final void Function(String type) onTap;
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: ChatController.instance.chatListTypes.length,
      child: Container(
        color: Colors.white,
        child: TabBar(
          onTap: (index) {
            onTap(ChatController.instance.chatListTypes[index]);
          },
          tabs: List.generate(
            ChatController.instance.chatListTypes.length,
            (index) => Tab(
              text: Roles.getPluralLabel(
                ChatController.instance.chatListTypes[index],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
