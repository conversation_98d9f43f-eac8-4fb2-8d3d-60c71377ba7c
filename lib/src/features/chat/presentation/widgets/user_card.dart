import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/teachers.dart';
import '../../../../core/data/models/user.dart';
import '../../../../core/utils/helper_function.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_list_title.dart';

class UserCard extends StatelessWidget {
  const UserCard({
    super.key,
    this.user,
    this.teacher,
  });
  final User? user;
  final Teacher? teacher;
  @override
  Widget build(BuildContext context) {
    return CustomListTile(
      leading: CustomCachedNetworkImage(
        imageUrl: user?.image ?? teacher?.image ?? "",
        width: 50,
      ),
      title: user?.name ?? teacher?.name ?? "",
      subtitle: user?.job ?? teacher?.job ?? "",
      onTap: () {
        Get.back();
        int receiverId = user?.id ?? teacher?.id ?? 0;
        HelperFunction.navigateToChatDetails(receiverId: receiverId);
      },
    );
  }
}
