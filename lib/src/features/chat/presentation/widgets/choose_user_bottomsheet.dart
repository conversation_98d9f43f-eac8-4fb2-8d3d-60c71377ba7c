import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/enums/page_status.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../core/widgets/section_with_subjects_widget.dart';
import '../../controller/chat_controller.dart';
import 'user_card.dart';
import '../../../student/controller/student_controller.dart';
import '../../../student/presentation/widgets/student_list_tile.dart';

class ChooseUserBottomsheet extends StatelessWidget {
  const ChooseUserBottomsheet({
    super.key,
    required this.type,
  });
  final String type;
  @override
  Widget build(BuildContext context) {
    if (type == "supervisor") {
      ChatController.instance.getSupervisors();
    }
    String title = "";
    if (type == "teacher") {
      title = "Choose Teacher".tr;
    } else if (type == "supervisor") {
      title = "Choose an administrator to start chatting with".tr;
    } else if (type == "guardian") {
      title = "Choose student to start chat with guardian".tr;
    }
    return CustomBottomSheet(
      title: title,
      body: Container(
        constraints: BoxConstraints(maxHeight: Get.height * 0.7),
        color: AppColor.scaffoldColor,
        child: Column(
          children: [
            if (type == "teacher" || type == "guardian" || type == "student")
              SectionWithSubjectsWidget(
                showSubjects: false,
                showSections: true,
                onSectionChanged: (sectionId) {
                  if (type == "guardian" || type == "student") {
                    Get.put(StudentController());
                    StudentController.instance.getStudents(sectionId);
                  } else {
                    ChatController.instance.getTeachers(sectionId);
                  }
                },
              ),
            if (type == "teacher")
              Expanded(
                child: Obx(() {
                  var teachers = ChatController.instance.teachers.value;
                  if (teachers == null) {
                    return Center(child: CircularProgressIndicator());
                  } else {
                    return ListView.builder(
                      itemCount: teachers.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: UserCard(
                            teacher: teachers[index],
                          ),
                        );
                      },
                    );
                  }
                }),
              ),
            if (type == "supervisor")
              Expanded(
                child: Obx(
                  () {
                    var supervisors = ChatController.instance.supervisors.value;
                    if (supervisors == null) {
                      return Center(child: CircularProgressIndicator());
                    } else {
                      return ListView.builder(
                        itemCount: supervisors.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: UserCard(
                              user: supervisors[index],
                            ),
                          );
                        },
                      );
                    }
                  },
                ),
              ),
            if (type == "guardian" || type == "student")
              Expanded(
                child: Obx(
                  () {
                    var students = StudentController.instance.students;
                    if (StudentController.instance.pageStatus.value ==
                        PageStatus.loading) {
                      return Center(
                        child: CircularProgressIndicator(),
                      );
                    } else {
                      return ListView.builder(
                        itemCount: students.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: StudentListTile(
                              student: students[index],
                              forOpenChat: true,
                              isStudent: type == "student",
                            ),
                          );
                        },
                      );
                    }
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}
