import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/chat.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_cached_network_image.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../auth/services/auth_service.dart';

import '../../controller/chat_controller.dart';

class ChatCard extends StatelessWidget {
  const ChatCard({super.key, required this.chat});

  final Chat chat;

  String _buildTitle() {
    if (AuthService.instance.user.value?.role == Roles.Guardian &&
        chat.receiverRole == Roles.Supervisor) {
      return chat.receiverJob;
    }

    return chat.receiverName;
  }

  @override
  Widget build(BuildContext context) {
    print(ChatController.instance.type.value);
    return CustomListTile(
      title: _buildTitle(),
      onTap: () {
        Get.toNamed(AppRouter.chatDetails, arguments: chat);
      },
      // border: Border.all(width: 0.3, color: AppColor.greyColor5),
      leading: ClipRRect(
        child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColor.whiteColor,
            ),
            child: CustomCachedNetworkImage(
              imageUrl: chat.receiverImage,
              fit: BoxFit.fill,
              width: 50,
              height: 50,
            )),
      ),
      subtitleWidget: Text(
        chat.lastMessage?.message?.replaceAll("\n", " ") ?? '',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: chat.unreadCount == 0
          ? null
          : CircleAvatar(
              backgroundColor: AppColor.primaryColor,
              radius: 15,
              child: CustomText(
                text: chat.unreadCount.toString(),
                color: AppColor.whiteColor,
                fontSize: 14,
              ),
            ),
    );
  }
}

String formatDurationToString(Duration duration) {
  final int days = duration.inDays;
  final int hours = duration.inHours.remainder(24);
  final int minutes = duration.inMinutes.remainder(60);
  final int seconds = duration.inSeconds.remainder(60);

  List<String> parts = [];

  if (days > 0) {
    parts.add("$days days ");
  }

  if (hours > 0 || (parts.isNotEmpty && parts.last.endsWith('day'))) {
    parts.add("$hours hours ");
  }

  if (minutes > 0 || (parts.isNotEmpty && parts.last.endsWith('hour'))) {
    parts.add("$minutes minutes ");
  }

  if (seconds > 0) {
    parts.add("just now");
  }
  if (parts.length > 1) parts.removeLast();
  // Remove trailing space and join the parts
  return parts.join().trim();
}
