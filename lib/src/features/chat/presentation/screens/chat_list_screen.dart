import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/chat_controller.dart';
import '../widgets/chat_card.dart';
import '../widgets/chat_type_tab_bar.dart';
import '../widgets/choose_user_bottomsheet.dart';

class ChatListScreen extends StatelessWidget {
  const ChatListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controller = Get.put(ChatController());
    String currentType =
        controller.chatListTypes.isNotEmpty ? controller.chatListTypes[0] : '';
    return LayoutScreen(
      title: 'Chats'.tr,
      bodyHeader: ChatTypeTabBar(
        onTap: (String type) {
          controller.getchatList(type);
          currentType = type;
        },
      ),
      floatingActionButton: [
        Roles.Teacher,
        Roles.Supervisor,
        Roles.Guardian,
        Roles.Student,
      ].any((e) => e == AuthService.instance.user.value?.role)
          ? FloatingActionButton(
              child: const Icon(Icons.add),
              onPressed: () {
                Get.bottomSheet(
                  ChooseUserBottomsheet(
                    type: currentType,
                  ),
                  isScrollControlled: true,
                );
              },
            )
          : null,
      body: Obx(
        () => ListView.builder(
          itemCount: controller.chatList.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: ChatCard(
                chat: controller.chatList[index],
              ),
            );
          },
        ),
      ),
      controller: controller,
    );
  }
}
