import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/data/enums/message_type.dart';
import '../../../../core/data/models/chat.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../controller/chat_controller.dart';
import '../../controller/chat_message_controller.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../auth/services/auth_service.dart';
import '../../../comments/presentation/widgets/typing_input_box.dart';
import '../widgets/message_card.dart';

class ChatDetailsScreen extends GetView<ChatMessageController> {
  const ChatDetailsScreen({super.key, required this.chat});
  final Chat chat;

  String _buildTitle() {
    if (AuthService.instance.user.value?.role == Roles.Guardian &&
        chat.receiverRole == Roles.Supervisor) {
      return chat.receiverJob;
    }

    return chat.receiverName;
  }

  String? _buildSubTitle() {
    if (!(AuthService.instance.user.value?.role == Roles.Guardian &&
        chat.receiverRole == Roles.Supervisor)) {
      return chat.receiverJob;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    if (!Get.isRegistered<ChatMessageController>()) {
      Get.put(ChatMessageController());
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.chatMessages.clear();
      controller.getConversationMessages(chat.id, page: 1);
    });
    return LayoutScreen(
      title: _buildTitle(),
      subtitle: _buildSubTitle(),
      controller: controller,
      centerTitle: true,
      onPopInvokedWithResult: (canPop, result) {
        if (Get.isRegistered<ChatController>()) {
          ChatController.instance.getchatList();
        }
      },
      body: Column(
        // alignment: Alignment.center,
        children: [
          Expanded(
            child: Obx(
              () {
                return ListView.separated(
                  controller: controller.scrollController,
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  itemCount: controller.chatMessages.length + 1,
                  reverse: true,
                  itemBuilder: (context, index) {
                    if (index == controller.chatMessages.length) {
                      if (controller.canLoadMore()) {
                        controller.getMore(id: chat.id);
                        return SizedBox(
                          height: 100,
                          width: double.maxFinite,
                          child: Center(
                            child: Text('loading more ...'.tr),
                          ),
                        );
                      } else {
                        return const SizedBox.shrink();
                      }
                    }
                    return MessageCard(
                      message: controller.chatMessages[index],
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    // if (controller.chatMessages[index].seen != false &&
                    //     !showUnread) {
                    //   showUnread = true;
                    //   return SizedBox(
                    //     height: 16,
                    //     child: Text("Unread messages".tr),
                    //   );
                    // } else {
                    return const SizedBox(
                      height: 16,
                    );
                    // }
                  },
                );
              },
            ),
          ),
        ],
      ),
      bodyFooter: Container(
        margin: EdgeInsets.all(16),
        // bottom: 16,
        // right: 16,
        // left: 16,
        child: TypingInputBox(
          textEditingController: ChatMessageController.instance.textController,
          onTap: (message) async {
            await ChatMessageController.instance
                .sendMessage(chat.id, message, MessageType.text);
          },
          hint: "Write a message ..".tr,
        ),
      ),
    );
  }
}
