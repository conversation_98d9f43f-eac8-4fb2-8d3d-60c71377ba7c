import 'package:get/get.dart';
import '../../../core/router/app_router.dart';
import '../../auth/services/auth_service.dart';
import '../../notification/services/notification_service.dart';

class SplashController extends GetxController {
  @override
  Future<void> onReady() async {
    if (!Get.isRegistered<NotificationService>()) {
      Get.put<NotificationService>(await NotificationService().init());
    }
    if (!Get.isRegistered<AuthService>()) {
      Get.put<AuthService>(await AuthService().init());
    }
    if (AuthService.instance.token.value == null) {
      Get.offAllNamed(AppRouter.loginScreen);
    } else {
      await AuthService.instance.getProfile();
      Get.offAllNamed(AppRouter.homeScreen);
    }
    super.onReady();
  }
}
