import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../core/utils/app_assets.dart';
import '../../controller/splash_controller.dart';

class SplashScreen extends GetView<SplashController> {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(SplashController());
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          SizedBox(
            height: double.maxFinite,
            width: double.maxFinite,
            child: Image.asset(
              AppAssets.splashBackground,
              fit: BoxFit.cover,
            ),
          ),
          Center(
            child: SizedBox(
              width: 200,
              child: SvgPicture.asset(
                AppAssets.logo,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
