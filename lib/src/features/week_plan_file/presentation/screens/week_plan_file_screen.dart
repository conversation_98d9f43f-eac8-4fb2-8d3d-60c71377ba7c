import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/data/enums/page_status.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/widgets/custom_tab_bar.dart';
import '../../../auth/services/auth_service.dart';
import '../../controller/week_plan_file_controller.dart';
import '../widgets/week_plan_file_list_widget.dart';
import '../widgets/week_plan_file_upload_widget.dart';
import '../widgets/week_plan_file_viewer_widget.dart';

class WeekPlanFileScreen extends StatelessWidget {
  const WeekPlanFileScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Try to find an existing instance first, or create a new one if not found
    WeekPlanFileController controller;
    try {
      controller = Get.find<WeekPlanFileController>();
      developer.log('Found existing WeekPlanFileController instance',
          name: 'WeekPlanFileScreen');
    } catch (e) {
      controller = Get.put(WeekPlanFileController());
      developer.log('Created new WeekPlanFileController instance',
          name: 'WeekPlanFileScreen');
    }

    final userRole = AuthService.instance.user.value?.role;
    final isTeacher = userRole == Roles.Teacher;

    developer.log(
        'WeekPlanFileScreen build - userRole: $userRole, isTeacher: $isTeacher',
        name: 'WeekPlanFileScreen');

    return LayoutScreen(
      title: 'Week Plan Files'.tr,
      controller: controller,
      body: Obx(() {
        developer.log(
            'WeekPlanFileScreen body - pageStatus: ${controller.pageStatus.value}',
            name: 'WeekPlanFileScreen');

        // Force refresh if empty and not already loading
        if (controller.weekPlanFiles.isEmpty &&
            controller.pageStatus.value != PageStatus.loading) {
          developer.log('Files list is empty, forcing refresh',
              name: 'WeekPlanFileScreen');
          // Use Future.delayed to avoid build-time side effects
          Future.delayed(Duration.zero, () {
            controller.fetchWeekPlanFiles();
          });
        }

        if (controller.pageStatus.value == PageStatus.loading) {
          return Center(child: CircularProgressIndicator());
        }

        // For teachers, show tabs for upload and list
        if (isTeacher) {
          return Column(
            children: [
              CustomTabBar(
                tabs: [Tab(text: 'Upload'.tr), Tab(text: 'My Files'.tr)],
                children: [
                  // Upload tab
                  SingleChildScrollView(
                    padding: EdgeInsets.all(16.r),
                    child: WeekPlanFileUploadWidget(controller: controller),
                  ),

                  // Files list tab
                  SingleChildScrollView(
                    padding: EdgeInsets.all(16.r),
                    child: WeekPlanFileListWidget(controller: controller),
                  ),
                ],
              ),
            ],
          );
        }
        // For students, show the current week's file viewer
        else {
          return SingleChildScrollView(
            padding: EdgeInsets.all(16.r),
            child: WeekPlanFileViewerWidget(
              controller: controller,
            ),
          );
        }
      }),
    );
  }
}
