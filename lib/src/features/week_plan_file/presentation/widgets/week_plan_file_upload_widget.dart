import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/form_fields/date_form_field.dart';
import '../../../../core/widgets/form_fields/dropdown_form_field.dart';
import '../../controller/week_plan_file_controller.dart';

class WeekPlanFileUploadWidget extends StatelessWidget {
  final WeekPlanFileController controller;

  const WeekPlanFileUploadWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section selection
          DropdownFormField(
            label: 'Section'.tr,
            hint: 'Select Section'.tr,
            value: controller.selectedSectionId.value == 0
                ? null
                : controller.selectedSectionId.value.toString(),
            items: controller.sections.isEmpty
                ? []
                : controller.sections.map((section) {
                    return DropdownMenuItem(
                      value: section.id.toString(),
                      child: Text(section.name),
                    );
                  }).toList(),
            onChanged: (value) {
              if (value != null) {
                controller.selectedSectionId.value = int.parse(value);
              }
            },
          ),
          SizedBox(height: 16.h),

          // Week start date selection
          DateFormField(
            label: 'Week Start Date'.tr,
            hint: 'Select Week Start Date'.tr,
            value: controller.weekStartDate.value,
            onChanged: (date) {
              if (date != null) {
                controller.weekStartDate.value = date;

                // If end date is not set or is before start date, set it to start date + 6 days
                if (controller.weekEndDate.value == null ||
                    controller.weekEndDate.value!.isBefore(date)) {
                  controller.weekEndDate.value = date.add(Duration(days: 6));
                }
              }
            },
          ),
          SizedBox(height: 16.h),

          // Week end date selection
          DateFormField(
            label: 'Week End Date'.tr,
            hint: 'Select Week End Date'.tr,
            value: controller.weekEndDate.value,
            firstDate: controller.weekStartDate.value,
            onChanged: (date) {
              controller.weekEndDate.value = date;
            },
          ),
          SizedBox(height: 16.h),

          // File selection
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: 'Week Plan PDF File'.tr,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              SizedBox(height: 8.h),

              // File picker button
              ElevatedButton.icon(
                onPressed: controller.pickPdfFile,
                icon: Icon(Icons.upload_file),
                label: Text('Select PDF File'.tr),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.primaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: Size(double.infinity, 50.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ),

              // Selected file info
              if (controller.selectedFileName.value.isNotEmpty)
                Container(
                  margin: EdgeInsets.only(top: 12.h),
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: Colors.green.withAlpha(25),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 20.r),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: CustomText(
                          text: controller.selectedFileName.value,
                          fontSize: 14.sp,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          SizedBox(height: 24.h),

          // Upload button
          CustomButton(
            text: 'Upload Week Plan File'.tr,
            onPressed: controller.uploadWeekPlanFile,
            isLoading: controller.isUploading.value,
          ),
        ],
      );
    });
  }
}
