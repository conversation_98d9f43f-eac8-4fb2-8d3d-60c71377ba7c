import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/data/enums/page_status.dart';
import '../../../../core/data/models/week_plan_file.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/helper_function.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../teacher_achievement_file/presentation/screens/pdf_fullscreen_viewer.dart';
import '../../controller/week_plan_file_controller.dart';

class WeekPlanFileListWidget extends StatelessWidget {
  final WeekPlanFileController controller;

  const WeekPlanFileListWidget({Key? key, required this.controller})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    developer.log('WeekPlanFileListWidget build',
        name: 'WeekPlanFileListWidget');

    return Obx(() {
      developer.log(
          'WeekPlanFileListWidget Obx rebuild - pageStatus: ${controller.pageStatus.value}, files count: ${controller.weekPlanFiles.length}',
          name: 'WeekPlanFileListWidget');

      // Only show loading indicator if we're in loading state AND have no files
      if (controller.pageStatus.value == PageStatus.loading &&
          controller.weekPlanFiles.isEmpty) {
        developer.log('WeekPlanFileListWidget showing loading indicator',
            name: 'WeekPlanFileListWidget');
        return Center(child: CircularProgressIndicator());
      }

      // Show empty state if we have no files and are not loading
      if (controller.weekPlanFiles.isEmpty) {
        developer.log('WeekPlanFileListWidget showing empty state',
            name: 'WeekPlanFileListWidget');

        // Force refresh if empty and not already loading
        if (controller.pageStatus.value != PageStatus.loading) {
          Future.delayed(Duration.zero, () {
            controller.fetchWeekPlanFiles();
          });
        }

        return Center(
          child: CustomText(
            text: 'No week plan files available'.tr,
            fontSize: 16.sp,
            textAlign: TextAlign.center,
          ),
        );
      }

      developer.log(
          'WeekPlanFileListWidget showing ${controller.weekPlanFiles.length} files',
          name: 'WeekPlanFileListWidget');

      return ListView.builder(
        // Allow scrolling within the ListView
        physics: AlwaysScrollableScrollPhysics(),
        itemCount: controller.weekPlanFiles.length,
        itemBuilder: (context, index) {
          final file = controller.weekPlanFiles[index];
          developer.log('Building file card for file ${file.id}',
              name: 'WeekPlanFileListWidget');
          return WeekPlanFileCard(
            file: file,
            onDelete: () => _confirmDelete(context, file),
          );
        },
      );
    });
  }

  void _confirmDelete(BuildContext context, WeekPlanFile file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Week Plan File'.tr),
        content: Text(
          'Are you sure you want to delete this week plan file?'.tr,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              controller.deleteWeekPlanFile(file.id!);
            },
            child: Text('Delete'.tr, style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

class WeekPlanFileCard extends StatelessWidget {
  final WeekPlanFile file;
  final VoidCallback? onDelete;

  const WeekPlanFileCard({Key? key, required this.file, this.onDelete})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () => _openFile(),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section name
              CustomText(
                text: file.sectionFullName ?? '',
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: AppColor.primaryColor,
              ),
              SizedBox(height: 8.h),

              // Week date range
              Row(
                children: [
                  Icon(Icons.date_range, size: 16.r, color: AppColor.greyColor),
                  SizedBox(width: 4.w),
                  CustomText(
                    text:
                        '${file.formattedWeekStartDate} - ${file.formattedWeekEndDate}',
                    fontSize: 14.sp,
                    color: AppColor.greyColor,
                  ),
                ],
              ),
              SizedBox(height: 16.h),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // View button
                  ElevatedButton.icon(
                    onPressed: _openFile,
                    icon: Icon(Icons.visibility),
                    label: Text('View'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColor.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),

                  // Delete button (if onDelete is provided)
                  if (onDelete != null)
                    IconButton(
                      onPressed: onDelete,
                      icon: Icon(Icons.delete, color: Colors.red),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _openFile() {
    if (file.fileUrl != null && file.fileUrl!.isNotEmpty) {
      try {
        // Open PDF in fullscreen viewer
        Get.to(
          () => PdfFullscreenViewer(
            pdfUrl: file.fileUrl!,
            title: 'Week Plan File'.tr,
          ),
        );
      } catch (e) {
        HelperFunction.showErrorMessage('Error opening file: $e'.tr);
      }
    } else {
      HelperFunction.showErrorMessage('File URL is not available'.tr);
    }
  }
}
