import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/data/enums/page_status.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/helper_function.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../teacher_achievement_file/presentation/screens/pdf_fullscreen_viewer.dart';
import '../../controller/week_plan_file_controller.dart';

class WeekPlanFileViewerWidget extends StatelessWidget {
  final WeekPlanFileController? controller;

  const WeekPlanFileViewerWidget({
    Key? key,
    this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final WeekPlanFileController effectiveController =
        controller ?? Get.find<WeekPlanFileController>();

    return Obx(() {
      Get.log(
          'WeekPlanFileViewerWidget build - pageStatus: ${effectiveController.pageStatus.value}');
      if (effectiveController.pageStatus.value == PageStatus.loading) {
        return Center(child: CircularProgressIndicator());
      }

      final file = effectiveController.currentWeekPlanFile.value;

      if (file == null) {
        return Center(
          child: CustomText(
            text: 'No week plan file available for this week'.tr,
            fontSize: 16.sp,
            textAlign: TextAlign.center,
          ),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File info card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Section name
                  CustomText(
                    text: file.sectionFullName ?? '',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColor.primaryColor,
                  ),
                  SizedBox(height: 8.h),

                  // Week date range
                  Row(
                    children: [
                      Icon(
                        Icons.date_range,
                        size: 16.r,
                        color: AppColor.greyColor,
                      ),
                      SizedBox(width: 4.w),
                      CustomText(
                        text:
                            '${file.formattedWeekStartDate} - ${file.formattedWeekEndDate}',
                        fontSize: 14.sp,
                        color: AppColor.greyColor,
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),

          // PDF preview placeholder
          Container(
            height: 200.h,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.picture_as_pdf, size: 48.r, color: Colors.red),
                  SizedBox(height: 8.h),
                  CustomText(
                    text: 'Week Plan PDF'.tr,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),

          // Open PDF button
          CustomButton(
            text: 'View PDF'.tr,
            onPressed: () => _openFile(file.fileUrl),
          ),
        ],
      );
    });
  }

  void _openFile(String? fileUrl) {
    if (fileUrl != null && fileUrl.isNotEmpty) {
      try {
        // Open PDF in fullscreen viewer
        Get.to(
          () => PdfFullscreenViewer(
            pdfUrl: fileUrl,
            title: 'Week Plan File'.tr,
          ),
        );
      } catch (e) {
        HelperFunction.showErrorMessage('Error opening file: $e'.tr);
      }
    } else {
      HelperFunction.showErrorMessage('File URL is not available'.tr);
    }
  }
}
