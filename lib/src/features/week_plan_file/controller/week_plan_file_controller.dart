import 'dart:developer' as developer;
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/section_with_subjects.dart';
import '../../../core/data/models/week_plan_file.dart';
import '../../../core/utils/app_consts.dart';
import '../../../core/utils/helper_function.dart';
import '../../../features/auth/services/auth_service.dart';

class WeekPlanFileController extends BaseController {
  static WeekPlanFileController get instance => Get.find();

  // Observable variables
  final RxList<WeekPlanFile> weekPlanFiles = <WeekPlanFile>[].obs;
  final Rx<WeekPlanFile?> currentWeekPlanFile = Rx<WeekPlanFile?>(null);

  // File selection variables
  final Rx<File?> selectedFile = Rx<File?>(null);
  final RxString selectedFileName = ''.obs;

  // Date selection variables
  final Rx<DateTime?> weekStartDate = Rx<DateTime?>(null);
  final Rx<DateTime?> weekEndDate = Rx<DateTime?>(null);

  // Section selection variable
  final RxInt selectedSectionId = 0.obs;

  // Sections list
  final RxList<SectionWithSubjects> sections = <SectionWithSubjects>[].obs;

  // Loading state
  final RxBool isUploading = false.obs;

  @override
  void onInit() {
    super.onInit();
    // Set initial loading state
    setPageStatus(PageStatus
        .loaded); // Start with loaded state to avoid showing loading indicator

    // Debug log to track initialization
    developer.log('WeekPlanFileController initialized',
        name: 'WeekPlanFileController');

    // Initialize controller based on user role
    final user = AuthService.instance.user.value;

    // Fetch sections first, then fetch files
    fetchSections().then((_) {
      // After fetching sections, fetch files
      // For teachers, this will use the first section if none is selected
      if (user?.role == Roles.Teacher && user?.isKindergarten == true) {
        developer.log(
            'User is a kindergarten teacher, fetching week plan files',
            name: 'WeekPlanFileController');
        fetchWeekPlanFiles();
      } else if (user?.role == Roles.Student && user?.isKindergarten == true) {
        developer.log('User is a kindergarten student, fetching week plan file',
            name: 'WeekPlanFileController');
        fetchWeekPlanFiles();
      }
    }).catchError((error) {
      // Handle any errors during initialization
      developer.log('Error during initialization: $error',
          name: 'WeekPlanFileController');
      setPageStatus(PageStatus.noInternet);
    });
  }

  /// Fetches available sections for the teacher
  Future<void> fetchSections() async {
    try {
      final response = await apiProvider.getSectionsWithSubjects();
      sections.value = response;

      // If the user is a teacher, filter sections based on their assigned sections
      final user = AuthService.instance.user.value;
      if (user?.role == Roles.Teacher) {
        // This is a simplified approach - in a real app, you would filter based on the teacher's assigned sections
        // For now, we'll just use all available sections
      }
    } catch (e) {
      developer.log('Error fetching sections: $e',
          name: 'WeekPlanFileController');
      HelperFunction.showErrorMessage(e.toString());
    }
  }

  /// Fetches all week plan files for teacher or current week's file for student
  Future<void> fetchWeekPlanFiles({int? sectionId}) async {
    developer.log('fetchWeekPlanFiles called with sectionId: $sectionId',
        name: 'WeekPlanFileController');

    // Only set loading status if we're not already showing files
    // This prevents the UI from flashing a loading indicator when we already have data
    if (weekPlanFiles.isEmpty) {
      setPageStatus(PageStatus.loading);
    }

    try {
      final user = AuthService.instance.user.value;
      developer.log(
          'User role: ${user?.role}, isKindergarten: ${user?.isKindergarten}',
          name: 'WeekPlanFileController');

      if (user?.role == Roles.Teacher) {
        // For teachers, we need to pass the section_id
        // If sectionId is provided, use it, otherwise use selectedSectionId if it's set
        var effectiveSectionId = sectionId ??
            (selectedSectionId.value > 0 ? selectedSectionId.value : null);

        developer.log(
            'Initial effectiveSectionId: $effectiveSectionId, sections count: ${sections.length}',
            name: 'WeekPlanFileController');

        // If no section is selected yet, try to use the first available section
        if (effectiveSectionId == null && sections.isNotEmpty) {
          effectiveSectionId = sections.first.id;
          selectedSectionId.value = effectiveSectionId;
          developer.log('Using first section: $effectiveSectionId',
              name: 'WeekPlanFileController');
        }

        if (effectiveSectionId == null) {
          // Still no section available, show empty state
          developer.log('No section available, showing empty state',
              name: 'WeekPlanFileController');
          weekPlanFiles.value = [];
          setPageStatus(PageStatus.empty);
          return;
        }

        // Update the selected section ID if a new one was provided
        if (sectionId != null && sectionId != selectedSectionId.value) {
          selectedSectionId.value = sectionId;
          developer.log('Updated selectedSectionId to: $sectionId',
              name: 'WeekPlanFileController');
        }

        // Get files for the selected section
        developer.log('Fetching files for section: $effectiveSectionId',
            name: 'WeekPlanFileController');
        final files = await apiProvider.getTeacherWeekPlanFiles(
          sectionId: effectiveSectionId,
        );

        developer.log(
            'Fetched ${files.length} files for section: $effectiveSectionId',
            name: 'WeekPlanFileController');
        weekPlanFiles.value = files;

        // If there are files, set the current file to the first one
        if (files.isNotEmpty) {
          currentWeekPlanFile.value = files.first;
          developer.log(
              'Set currentWeekPlanFile to first file: ${files.first.id}',
              name: 'WeekPlanFileController');
        }
      } else {
        // For students, get the current week's file
        developer.log('Fetching week plan file for student',
            name: 'WeekPlanFileController');
        final file = await apiProvider.getStudentWeekPlanFile();

        if (file != null) {
          developer.log(
              'Student week plan file fetched successfully: ${file.id}',
              name: 'WeekPlanFileController');
          weekPlanFiles.value = [file];
          currentWeekPlanFile.value = file;
        } else {
          developer.log('No week plan file available for student',
              name: 'WeekPlanFileController');
          weekPlanFiles.value = [];
        }
      }

      if (weekPlanFiles.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else {
        setPageStatus(PageStatus.loaded);
      }
    } catch (e) {
      developer.log('Error fetching week plan files: $e',
          name: 'WeekPlanFileController');
      setPageStatus(PageStatus.noInternet);
      HelperFunction.showErrorMessage(e.toString());
    }
  }

  /// Fetches a specific week plan file by ID
  Future<void> fetchWeekPlanFile(int id) async {
    try {
      final response = await apiProvider.getWeekPlanFile(id);
      if (response.data != null) {
        currentWeekPlanFile.value = response.data;
      } else {
        developer.log('No week plan file found with ID: $id',
            name: 'WeekPlanFileController');
        HelperFunction.showErrorMessage('Week plan file not found');
      }
    } catch (e) {
      developer.log('Error fetching week plan file: $e',
          name: 'WeekPlanFileController');
      HelperFunction.showErrorMessage(e.toString());
    }
  }

  /// Opens file picker to select a PDF file
  Future<void> pickPdfFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.first.path != null) {
        selectedFile.value = File(result.files.first.path!);
        selectedFileName.value = result.files.first.name;
      }
    } catch (e) {
      developer.log('Error picking PDF file: $e',
          name: 'WeekPlanFileController');
      HelperFunction.showErrorMessage('Error selecting file: ${e.toString()}');
    }
  }

  /// Uploads a week plan file
  Future<void> uploadWeekPlanFile() async {
    if (selectedFile.value == null) {
      HelperFunction.showErrorMessage('Please select a PDF file');
      return;
    }

    if (weekStartDate.value == null) {
      HelperFunction.showErrorMessage('Please select a week start date');
      return;
    }

    if (weekEndDate.value == null) {
      HelperFunction.showErrorMessage('Please select a week end date');
      return;
    }

    if (selectedSectionId.value == 0) {
      HelperFunction.showErrorMessage('Please select a section');
      return;
    }

    isUploading.value = true;

    try {
      final response = await apiProvider.uploadWeekPlanFile(
        selectedSectionId.value,
        DateFormat('yyyy-MM-dd').format(weekStartDate.value!),
        DateFormat('yyyy-MM-dd').format(weekEndDate.value!),
        selectedFile.value!,
      );

      // Reset form fields
      selectedFile.value = null;
      selectedFileName.value = '';
      weekStartDate.value = null;
      weekEndDate.value = null;
      selectedSectionId.value = 0;

      // Add the new file to the list
      if (response.data != null) {
        weekPlanFiles.insert(0, response.data!);
      }

      // Show success message
      HelperFunction.showSuccessMessage(
          response.message ?? 'Week plan file uploaded successfully');

      // Refresh the list with the section ID that was just used
      await fetchWeekPlanFiles(sectionId: selectedSectionId.value);
    } catch (e) {
      developer.log('Error uploading week plan file: $e',
          name: 'WeekPlanFileController');
      HelperFunction.showErrorMessage(e.toString());
    } finally {
      isUploading.value = false;
    }
  }

  /// Deletes a week plan file
  Future<void> deleteWeekPlanFile(int id) async {
    try {
      // Get the section ID of the file being deleted for refreshing the list later
      final sectionId = weekPlanFiles
          .firstWhere((file) => file.id == id, orElse: () => WeekPlanFile())
          .sectionId;

      final response = await apiProvider.deleteWeekPlanFile(id);

      // Remove the file from the list
      weekPlanFiles.removeWhere((file) => file.id == id);

      // Show success message
      HelperFunction.showSuccessMessage(
          response.message ?? 'Week plan file deleted successfully');

      // If the current file was deleted, set it to null
      if (currentWeekPlanFile.value?.id == id) {
        currentWeekPlanFile.value = null;
      }

      // Update page status if needed
      if (weekPlanFiles.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else if (sectionId != null) {
        // Refresh the list with the section ID of the deleted file
        await fetchWeekPlanFiles(sectionId: sectionId);
      }
    } catch (e) {
      developer.log('Error deleting week plan file: $e',
          name: 'WeekPlanFileController');
      HelperFunction.showErrorMessage(e.toString());
    }
  }
}
