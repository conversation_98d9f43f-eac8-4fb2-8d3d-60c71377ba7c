import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../core/data/models/activity.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/utils/app_consts.dart';
import '../../../../core/utils/helper_function.dart';
import '../../../../core/widgets/bottom_sheets/custom_bottom_sheet.dart';
import '../../../../core/widgets/custom_list_title.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../auth/services/auth_service.dart';

class ActivityListTile extends StatelessWidget {
  const ActivityListTile({super.key, required this.activity});
  final Activity activity;
  @override
  Widget build(BuildContext context) {
    String title = activity.title ?? "";
    if (AuthService.instance.user.value?.role != Roles.Student) {
      title = "$title - ${activity.sectionName}";
    }
    return CustomListTile(
      image: activity.imageUrl,
      title: activity.title,
      subtitleWidget: Container(
        margin: EdgeInsets.only(top: 8),
        child: Wrap(
          spacing: 6,
          runSpacing: 6,
          children: activity.audience
                  ?.map(
                    (x) => Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Get.theme.primaryColor,
                        ),
                      ),
                      child: CustomText(
                        text: x,
                        fontSize: 14,
                      ),
                    ),
                  )
                  .toList() ??
              [],
        ),
      ),
      leadingIcon: AppAssets.activity,
      leadingIconColor: Get.theme.primaryColor,
      detail: Column(
        children: [
          const SizedBox(
            height: 16,
          ),
          Row(
            children: [
              SvgPicture.asset(
                AppAssets.calender,
              ),
              const SizedBox(
                width: 4,
              ),
              Text(
                activity.formattedDate ?? "",
              ),
              const SizedBox(
                width: 4,
              ),
            ],
          ),
          const SizedBox(
            height: 8,
          ),
          SizedBox(
            width: double.maxFinite,
            child: Text(
              activity.description ?? "",
              softWrap: true,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(
            height: 8,
          ),
          Align(
            alignment: const AlignmentDirectional(1.12, 0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Material(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: () {
                      HelperFunction.showCommentsBottomSheet(
                          id: activity.id ?? 0, type: 'activity');
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'Comments'.tr,
                        style: TextStyle(
                          fontSize: 13,
                          color: Get.theme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
                Material(
                  borderRadius: BorderRadius.circular(8),
                  color: Get.theme.primaryColor,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: () {
                      Get.bottomSheet(
                        CustomBottomSheet(
                          title: title,
                          body: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Text(
                              activity.description ?? "",
                              style: const TextStyle(
                                height: 2,
                              ),
                            ),
                          ),
                        ),
                        isScrollControlled: true,
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'More'.tr,
                        style: const TextStyle(
                          fontSize: 13,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
