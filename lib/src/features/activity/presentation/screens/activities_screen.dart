import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../core/screens/layout_screen.dart';
import '../../../../core/utils/app_assets.dart';
import '../../../../core/widgets/errors/empty_widget.dart';
import '../../controller/activity_controller.dart';
import '../widgets/activity_list_tile.dart';

class ActivitiesScreen extends StatelessWidget {
  const ActivitiesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var controler = Get.put(ActivityController());
    return LayoutScreen(
      title: 'Activities'.tr,
      controller: controler,
      emptyWidegt: EmptyWidget(
        icon: AppAssets.activity,
        iconColor: Get.theme.primaryColor,
      ),
      body: Obx(
        () {
          return ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: controler.activities.length,
            itemBuilder: (context, index) => ActivityListTile(
              activity: controler.activities[index],
            ),
            separatorBuilder: (BuildContext context, int index) =>
                16.verticalSpace,
          );
        },
      ),
    );
  }
}
