import 'package:get/get.dart';
import '../../../core/controller/base_controller.dart';
import '../../../core/data/enums/page_status.dart';
import '../../../core/data/models/activity.dart';

class ActivityController extends BaseController {
  static ActivityController instance = Get.find();

  RxList<Activity> activities = RxList<Activity>([]);

  @override
  void onReady() {
    getActivities();
    super.onReady();
  }

  void getActivities() {
    callApi(() async {
      var response = await apiProvider.getActivities();
      activities.value = response;
      if (activities.isEmpty) {
        setPageStatus(PageStatus.empty);
      } else {
        setPageStatus(PageStatus.loaded);
      }
    });
  }
}
