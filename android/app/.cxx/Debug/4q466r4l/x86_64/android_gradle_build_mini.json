{"buildFiles": ["C:\\android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter\\rawd_aljenan_app\\android\\app\\.cxx\\Debug\\4q466r4l\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter\\rawd_aljenan_app\\android\\app\\.cxx\\Debug\\4q466r4l\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}