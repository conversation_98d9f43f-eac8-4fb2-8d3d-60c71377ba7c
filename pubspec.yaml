name: rawd_aljenan
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
# ios
# version: 1.0.2+1
# android
version: 1.0.0+10

environment:
  sdk: ">=3.3.1 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  get: ^4.6.6
  image_picker: ^1.1.1
  flutter_slidable: ^4.0.0
  flutter_svg: ^2.0.10+1
  table_calendar: ^3.1.1
  auto_size_text: ^3.0.0
  drop_down_list: ^2.0.0
  flutter_screenutil: ^5.9.0
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.1.0
  equatable: ^2.0.5
  dartz: ^0.10.1
  internet_connection_checker: ^3.0.1
  get_storage: ^2.1.1
  shimmer: ^3.0.0
  intl: ^0.19.0
  # fluttertoast: ^8.2.4
  # multi_image_picker2: ^5.0.02
  form_validator: ^2.1.1
  firebase_core: ^3.4.0
  firebase_messaging: ^15.1.0
  dio: ^5.7.0
  cached_network_image: ^3.4.1
  flutter_image_compress: ^2.3.0
  chewie: ^1.8.5
  video_player: ^2.9.1
  google_fonts: ^6.2.1
  retrofit: ^4.4.1
  url_launcher: ^6.3.0
  pretty_dio_logger: ^1.4.0
  universal_platform: ^1.1.0
  logger: ^2.4.0
  extended_image: ^9.1.0
  flutter_widget_from_html_core: ^0.15.2
  pin_code_fields: ^8.0.1
  animated_custom_dropdown: ^3.1.1
  # video_thumbnail: ^0.5.3
  path_provider: ^2.1.4

  http: any
  path: any
  http_parser: any
  date_field: ^6.0.3+1
  fading_edge_scrollview: ^4.1.1
  modal_bottom_sheet: ^3.0.0
  mobile_device_identifier: ^0.0.2
  firebase_crashlytics: ^4.1.2
  timeago: ^3.7.0
  flutter_cached_pdfview: ^0.4.3
  connectivity_plus: ^6.1.0
  get_thumbnail_video: ^0.7.3
  youtube_player_flutter: ^9.1.1
  share_plus: ^10.1.4
  flutter_cache_manager: ^3.4.1
  device_preview: ^1.2.0
  device_preview_screenshot: ^1.0.0
  file_picker: ^10.1.2
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  retrofit_generator: ^9.1.2
  build_runner: ^2.4.12
  flutter_launcher_icons: ^0.14.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/vectors/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
